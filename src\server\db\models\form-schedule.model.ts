import {
  Table,
  Column,
  Model,
  DataType,
  Sequelize
} from 'sequelize-typescript';
import * as CronConverter from 'cron-converter';

@Table({
  tableName: 'form_schedule',
  paranoid: true,
  timestamps: true,
  scopes: {
    all: {
      paranoid: false
    }
  }
})
class FormSchedule extends Model<FormSchedule> {

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  startAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  processedTo: Date;

  @Column({
    type: DataType.STRING({length: 255}),
    defaultValue: '',
    allowNull: false,
  })
  expression: string;

  @Column({
    type: DataType.VIRTUAL,
    get(this: FormSchedule):Date|null {

      if( !this.deletedAt && this.expression && this.expression.length ) {
        const cronInstance = new CronConverter();

        // Set the start month equal to the process date 
        let cronExpression = this.expression
          /**
           * If it is a monthly format executing every few months
           * we want it to occurr on every valid month
           **/
          .replace(/1(?=\/[0-9]+\s[^\s]+$)/,'*')
          /** Every day of week */
          .replace(/\?$/,'*')
          /** Every day of month */
          .replace(/(?<=^[0-9]+\s[0-9]+\s)\?/,'*')
          /** Daily */
          .replace(/(?<=^[0-9]+\s[0-9]+\s)[0-9]+(?=\/[0-9])/,'*')
        ;
        cronInstance.fromString(cronExpression);

        return cronInstance.schedule(this.getProcessDate()).next().toDate();
      }
      
      return null;
    }
  })
  nextExecution:Date|null;

  getProcessDate():Date {
    if( this.processedTo ) {

      const processedTo = new Date(this.processedTo);
      if( this.startAt ) {
        const startAt = new Date(this.startAt);

        if( startAt > processedTo )
          return startAt;
      }

      return processedTo;
    }

    return new Date();
  }
}

export default FormSchedule;

