import * as express from 'express';
import { ResourcePackController } from '../controller/resource-pack.controller';
import { responseWrap } from '../utils/responseWrap';

const controller = new ResourcePackController();
const router = express.Router();

router.post('/resource-packs', responseWrap(controller.create));
router.get('/resource-packs', responseWrap(controller.fetch));
router.get('/resource-packs/:id', responseWrap(controller.fetchById));
router.put('/resource-packs/:id', responseWrap(controller.update));
router.delete('/resource-packs/:id', responseWrap(controller.delete));
router.post('/resource-packs/:id/documents', responseWrap(controller.addAttachment));
router.delete('/resource-packs/documents/:id', responseWrap(controller.removeAttachment));

export default router;