'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDefinition = await queryInterface.describeTable('document_collections');

    if (tableDefinition['reviewSchedule']) {
      return;
    }
    return queryInterface.addColumn(
      'document_collections', // table name
      'reviewSchedule', // new field name
      {
        type: Sequelize.ENUM('ANNUALLY', 'BIANNUALLY'),
      },
    )
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('document_collections', 'reviewSchedule')
  }
};
