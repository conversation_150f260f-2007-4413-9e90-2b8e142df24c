'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.bulkInsert('enums', [{
      name: 'DocumentStandard',
      value: 'StandardPlaceHolder1',
      order: 0
    },{
      name: 'DocumentStandard',
      value: 'StandardPlaceHolder1',
      order: 1
    },{
      name: 'DocumentStandard',
      value: 'StandardPlaceHolder1',
      order: 2
    }], {});
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('enums', [{
      name: 'DocumentStandard',
      value: 'StandardPlaceHolder1',
      order: 0
    },{
      name: 'DocumentStandard',
      value: 'StandardPlaceHolder1',
      order: 1
    },{
      name: 'DocumentStandard',
      value: 'StandardPlaceHolder1',
      order: 2
    }], {});
  }
};
