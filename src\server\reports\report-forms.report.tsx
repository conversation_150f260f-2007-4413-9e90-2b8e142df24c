import * as React from 'react';
import { Document, Page, StyleSheet, View, Text } from '@react-pdf/renderer';
import { IFormOutputValidationModel } from "../../common/contracts/form";
import {ReportFormStage0} from "./report-form-stage0.report";
import { ReportFormStage1 } from "./report-form-stage1.report";
import { ReportFormStage2 } from "./report-form-stage2.report";
import { ReportFormStage3 } from "./report-form-stage3.report";
import { IEnumsOutputModel } from "../../common/contracts/enums";
import FormLocation from "../db/models/formLocation.model";
import { IRecordWithDateOutputModel } from "../../common/contracts/record";

const styles = StyleSheet.create({
  page: {
    flexDirection: 'col',
    backgroundColor: '#FFFFFF',
    padding: 20,
    paddingLeft: 30,
  },
  footerText: {
    display: 'block',
    fontSize: 12,
    marginBottom: 5,
    marginTop: 5,
    textAlign: 'left',
  },
  footer: {
    marginTop: 10
  }
});

export class ReadOnlyFormsReport extends React.Component<{
  formData: IFormOutputValidationModel,
  originatorTypes: IEnumsOutputModel[],
  reportFormTypes: IEnumsOutputModel[],
  location: FormLocation | null,
  records: IRecordWithDateOutputModel[],
  followUpGroupMap: {[n: number]: string},
  reassignedUserMap: {[n: number]: string},
}> {
  render() {

    return (

      <Document>
        <Page size="A4" style={styles.page} wrap={true}>
          {
            this.props.records.map((record, i) => {
              switch (record.stage) {
                case 0: return <ReportFormStage0
                  key={i}
                  recordData={record}
                  originatorTypes={this.props.originatorTypes}
                  reportFormTypes={this.props.reportFormTypes}
                  location={this.props.location}
                  groupName={this.props.formData.userGroup ? this.props.formData.userGroup.groupName : ''}
                />;
                case 1: return <ReportFormStage1
                  key={i}
                  recordData={record}
                  reassignedUserMap={this.props.reassignedUserMap}
                  followUpGroupMap={this.props.followUpGroupMap}
                />;
                case 2: return <ReportFormStage2
                  key={i}
                  recordData={record}
                  reassignedUserMap={this.props.reassignedUserMap}
                  followUpGroupMap={this.props.followUpGroupMap}
                  />;
                case 3: return <ReportFormStage3
                  key={i}
                  recordData={record}
                />;
                default: return null;
              }
            })
          }
          <View style={styles.footer}>
            <Text style={styles.footerText}>Thank you for taking the time to give us your feedback.</Text>
  
            <Text style={styles.footerText}>If at any time you feel we need to revisit this matter, please advise.</Text>
          </View>
        </Page>
      </Document>
    );
  }
}
