'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      return await queryInterface.bulkInsert('groups', [{
        groupName: 'OHSC Management',
      }], {});
    } catch (err) {
      console.error("----------");
      console.error("ERROR DURING ADDiNG INSERTING GROUPS: OHSC Management");
      console.error("----------");
      console.error(err);
    }
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('groups', [{
      groupName: 'OHSC Management',
    }], {});
  }
};
