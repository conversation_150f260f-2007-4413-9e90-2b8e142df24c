import * as t from 'io-ts';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { SortOrderValues } from './common';
import { constructType } from './extend';

const RiskOutputModel = t.interface({
  id: t.number,
  summary: t.string,
  riskLevel: t.string,
  risktype: t.string,
  groupId: t.number,
  locationId: t.number,
  dateOfLastAssessment: t.string,
  dateOfNextAssessment: t.string,
});

export interface IRiskOutputModel extends t.TypeOf<typeof RiskOutputModel> {}

const TextType = t.refinement(
  t.refinement(t.string, s => (s.length >= 0 && s.trim.length >= 0), 'valid length'),
  s => s.length < 128,
  'up to 128 characters',
);

export const CreateRiskInputModel = t.interface({
  summary: TextType,
  riskLevel: TextType,
  riskType: TextType,
  location: TextType
});

export interface ICreateRiskInputModel extends t.TypeOf<typeof CreateRiskInputModel> {}

export const UpdateRiskInputModel = t.interface({
  id: IntFromString,
  summary: TextType,
  riskLevel: TextType,
  riskType: TextType,
  location: TextType
});

//@ts-ignore
export const RiskSortedField = t.union(['summary', 'id'].map(f => t.literal(f)));

export type IRiskSortedField = t.TypeOf<typeof RiskSortedField>;

export const RiskQueryParams = constructType({
  skip: IntFromString,
  limit: IntFromString,
  sortBy: RiskSortedField,
  order: SortOrderValues,
  searchFiler: TextType,
});

export interface IRiskQueryParams extends t.TypeOf<typeof RiskQueryParams> {}

export interface IUpdateRiskInputModel extends t.TypeOf<typeof UpdateRiskInputModel> {}