import { Injectable } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";
import { debounceTime, filter, map, tap } from "rxjs/operators";
import { logger } from "./Logger";

export type ValueChangedEvent = {
	key: string,
	value: unknown
}

const className = "ArbitraryValueCache";

@Injectable()
export class ArbitraryValueCache {

	private valueChangedSubject = new BehaviorSubject<ValueChangedEvent | null>(null);
	private cache: Record<string, unknown> = {};

	public onChange<T = unknown>(key: string, settings?: {
		debounceTime?: number
	}): Observable<T> {
		const signature = className + ".onChange: ";
		const _settings = Object.assign({
			debounceTime: 200
		}, settings);

		return this.valueChangedSubject.pipe(
			filter(evt => evt !== null && evt.key === key),
			debounceTime(_settings.debounceTime),
			tap(evt => logger.silly(signature + `Value Changed at Key[${evt ? evt.key : 'undefined'}] with Value[${evt ? JSON.stringify(evt.value, null, 2) : 'undefined'}]`)),
			map(evt => (evt ? JSON.parse(JSON.stringify(evt.value)) : undefined) as T),
		);
	}

	public get(key: string) {
		const signature = className + ".get: ";
		const hasValue = Object.keys(this.cache).includes(key);

		if (!hasValue) {
			logger.silly(signature + `Cache miss on Key[${key}]`);
			return undefined;
		}

		logger.silly(signature + `Cache hit on Key[${key}]`);
		return this.cache[key];
	}

	public set(key: string, value: unknown) {
		const signature = className + ".set: ";
		const oldValue = Object.keys(this.cache).includes(key) ? this.comparisonValue(this.cache[key]) : undefined;
		const newValue = this.comparisonValue(value);

		if (typeof oldValue === 'undefined' || oldValue !== newValue) {
			logger.silly(signature + `Updating Key[${key}]`);
			this.cache[key] = JSON.parse(newValue);
			this.valueChangedSubject.next({
				key,
				value
			});

			return true;
		}

		logger.silly(signature + `Not updating unchanged Key[${key}]`);
		return false;
	}

	public delete(key: string) {
		const signature = className + ".delete: ";
		const hasValue = Object.keys(this.cache).includes(key);

		if (!hasValue) {
			logger.silly(signature + 'Not deleting undefined key');
			return false;
		}

		delete this.cache[key];
		this.valueChangedSubject.next({
			key,
			value: undefined
		});
		logger.silly(signature + `Deleted Key[${key}]`);
		return true;
	}

	public clear() {
		const signature = className + ".clear: ";
		Object.keys(this.cache).forEach(cacheKey => {
			delete this.cache[cacheKey];
			this.valueChangedSubject.next({
				key: cacheKey,
				value: undefined
			});
		});

		logger.silly(signature + 'Cache Cleared');
	}

	/**
	 * @description Calculates the difference in keys and values between oldValue and comparisonValue.
	 * 
	 * @param {string} key - The key to compare values for.
	 * @param {unknown} value - The value to be compared with oldValue.
	 * @returns {string[]} - An array of keys that either do not exist in comparisonValue or have different values compared to oldValue.
	 */

	public keyDiff(cacheKey: string, value: unknown) {
		const oldData = this.get(cacheKey);
		// Satisfying the older TS compiler
		const _oldValue = (typeof oldData === 'object' ? oldData : {});
		const oldValue = _oldValue === null ? {} : _oldValue;
		const _comparisonValue = (typeof value === 'object' ? value : {});
		const comparisonValue = _comparisonValue === null ? {} : _comparisonValue;

		let result: { key: string, diffType: string }[] = [];

		Object.keys(oldValue).forEach(oldKey => {
			if (!(oldKey in comparisonValue)) {
				result.push({
					key: oldKey,
					diffType: 'NOT_EXIST'
				});
				return;
			}

			if (this.comparisonValue(oldValue[oldKey]) !== this.comparisonValue(comparisonValue[oldKey])) {
				result.push({
					key: oldKey,
					diffType: 'VALUE'
				});
				return;
			}
		});

		Object.keys(comparisonValue).forEach(comparisonKey => {
			if (!(comparisonKey in oldValue)) {
				result.push({
					key: comparisonKey,
					diffType: 'NEW_KEY'
				});
				return;
			}

			if (this.comparisonValue(oldValue[comparisonKey]) !== this.comparisonValue(comparisonValue[comparisonKey])) {
				result.push({
					key: comparisonKey,
					diffType: 'VALUE'
				});
				return;
			}
		});

		return result.reduce((arr, val, idx) => {
			const firstMatch = arr.findIndex(v => v.key === val.key && v.diffType === val.diffType);
			if (firstMatch === -1 || firstMatch === idx) {
				arr.push(val);
			}

			return arr;
		}, [] as typeof result)
	}

	private comparisonValue(value: unknown) {
		return JSON.stringify(value);
	}
}