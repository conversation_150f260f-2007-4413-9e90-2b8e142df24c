<div class="row" *ngIf="!readOnly">
    <div class="col-12 col-lg-6">
        <img src="assets/images/logo.png" class="logoImg">
    </div>
    <div class="col-12 pt-2 pt-lg-0 col-lg-6 text-center">
        <div class="border border-dark rounded border-dashed d-inline-block float-lg-right py-3 px-5">
            <div class="row">
                <div class="col-12">
                    <strong class="font-weight-heavy">Issue Number:</strong>
                    {{ formData ? formData.issueNumber || "TBA" : "TBA" }}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-12">
                    <strong class="font-weight-heavy">Date Created:</strong> {{dateString}}
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-12">
        <h1 class="text-uppercase text-white bg-teal p-2 px-3 my-2">PROGRESS AND RESOLUTION</h1>
    </div>
</div>
<form>
    <div class="row">
        <div class="col-12 col-sm-6">
            <div class="form-group">
                <label>Reassign FUR to another Staff Member:</label>
                <button-toggle [value]="form.reassign.value" (valueChange)="showReassignOptions($event)" [disabled]="readOnly"></button-toggle>
            </div>
        </div>
        <div class="col-12 col-sm-6" *ngIf="form.reassign.value===true">
            <div class="form-group">
                <label>&nbsp;</label>
                <user-select [field]="form.reassignToUserId" [disabled]="readOnly"></user-select>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 col-lg-8 border-lg-right border-dashed">
            <div class="row mt-3 mb-1">
                <div class="col-12">
                    <span>2. FUR Status</span>
                </div>
            </div>
            <div class="row mt-1 mb-3">
                <div class="col-12">
                    <buttons-switch
                        [disabled]="readOnly"
                        [options]="furStatusOptions"
                        [(value)]="form.furStatus.value"
                        (valueChange)="handleStatusChanged($event)">
                    </buttons-switch>
                    <div class="text-center" *ngIf="form.furStatus.showErrorHelp()">
                        <small class="form-text text-danger" >Select FUR Status Above</small>
                    </div>
                </div>
            </div>
            <div class="row my-3">
                <div class="col-12">
                    <div class="form-group">
                        <label>3. Did you check with originator/ customer‘s?</label>
                        <button-toggle [(value)]="form.hasContactedOriginator.value" [disabled]="readOnly"></button-toggle>
                    </div>
                </div>
            </div>
            <div class="row my-3">
                <div class="col-12">
                    <span>4. Detail / Outcome:</span>
                </div>
            </div>
            <div class="row my-3">
                <div class="col-12">
                    <textarea
                        class="form-control {{form.detail.iifIsValid('border-success','border-danger')}}"
                        replaceAmpersand
                        autosize
                        [minRows]="3"
                        [placeholder]="readOnly ? '' : 'Detail'"
                        [spellcheck]="true"
                        [(ngModel)]="form.detail.value"
                        [ngModelOptions]="{standalone: true}"
                        [disabled]="readOnly"
                    ></textarea>
                    <small class="form-text text-danger" *ngIf="form.detail.showErrorHelp()">Invalid Detail</small>
                </div>
            </div>
            <div class="row my-3">
                <div class="col-12">
                    <span>5. Any other action/ referral/ improvement required now:</span>
                </div>
            </div>
            <div class="row my-3">
                <div class="col-12">
                    <textarea
                        class="form-control {{form.notes.iifIsValid('border-success','border-danger')}}"
                        replaceAmpersand
                        autosize
                        [minRows]="3"
                        placeholder="Summary"
                        [spellcheck]="true"
                        [(ngModel)]="form.notes.value"
                        [ngModelOptions]="{standalone: true}"
                        [disabled]="readOnly"
                    ></textarea>
                    <small class="form-text text-danger" *ngIf="form.notes.showErrorHelp()">Invalid Notes</small>
                </div>
            </div>
            <div class="row mb-3 mt-4">
                <div class="col-12">
                    <follow-up-widget
                        #followUpWidget
                        [model]="form.followUps.value"
                        (modelChange)="form.followUps.value=$event"
                        [readOnly]="readOnly"
                    ></follow-up-widget>
                    <div class="text-center" *ngIf="form.followUps.showErrorHelp()">
                        <small class="form-text text-danger">A FUR is required for OnTrack FURs</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 offset-0 col-lg-4 pt-0 pt-lg-4">
            <div class="row">
                <div class="col-12 offset-0 col-xl-10 offset-xl-1">
                    <document-uploads
                        [(documents)]="documents"
                        [readOnly]="readOnly"
                        [disableTicking]="true"></document-uploads>
                </div>
            </div>
        </div>
    </div>
    <submission-detail [formRecord]="formRecord"></submission-detail>
    <div class="row" *ngIf="!readOnly">
        <div class="col-12 col-md-6 col-lg-4 col-xl-2 col-xxl-2">
            <button class="btn btn-block btn-lg btn-success rounded-0" (click)="submit(false)"><span class="capitalize">Submit</span></button>
        </div>
        <div class="col-12 pt-2 col-md-6 pt-md-0 col-lg-5 col-xl-3 col-xxl-2">
            <a class="btn btn-block btn-lg btn-primary rounded-0 text-center text-white" (click)="submit(true)"><span class="capitalize">Save Draft</span></a>
        </div>
    </div>
</form>

