'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the table exists first
      const tableExists = await queryInterface.showAllTables().then(tables =>
        tables.includes('risks')
      );

      if (!tableExists) {
        console.log('Table "risks" does not exist, skipping migration');
        return;
      }

      const tableDefinition = await queryInterface.describeTable('risks');

      if (tableDefinition['dateOfLastAssessment']) {
        await queryInterface.changeColumn('risks', 'dateOfLastAssessment', {
          type: Sequelize.DATE,
          allowNull: true,
          defaultValue: null
        });
      }
    } catch (error) {
      console.log('Error in migration:', error.message);
      // Don't throw error if table doesn't exist
      if (!error.message.includes('does not exist')) {
        throw error;
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      // Check if the table exists first
      const tableExists = await queryInterface.showAllTables().then(tables =>
        tables.includes('risks')
      );

      if (!tableExists) {
        console.log('Table "risks" does not exist, skipping rollback');
        return;
      }

      const tableDefinition = await queryInterface.describeTable('risks');

      if (tableDefinition['dateOfLastAssessment']) {
        await queryInterface.changeColumn('risks', 'dateOfLastAssessment', {
          type: Sequelize.DATE,
          allowNull: false
        });
      }
    } catch (error) {
      console.log('Error in rollback:', error.message);
      // Don't throw error if table doesn't exist
      if (!error.message.includes('does not exist')) {
        throw error;
      }
    }
  }
};
