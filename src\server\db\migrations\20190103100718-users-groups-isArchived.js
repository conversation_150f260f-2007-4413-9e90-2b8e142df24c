'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    let tableDefinition = await queryInterface.describeTable('users');

    if (!tableDefinition['isArchived']) {
      await queryInterface.addColumn('users', 'isArchived', {
        type:Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      });
    }

    tableDefinition = await queryInterface.describeTable('groups');

    if (!tableDefinition['isArchived']) {
      await queryInterface.addColumn('groups', 'isArchived', {
        type:Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      });
    }

  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('users', 'isArchived');
    await queryInterface.removeColumn('groups', 'isArchived');
  }
};
