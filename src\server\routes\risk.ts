import * as express from 'express';
import { responseWrap } from '../utils/responseWrap';
import { RiskController } from '../controller/risk.controller';
import { validateRouteParams } from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';

const router = express.Router();

router.get('/risk/:id', validateRouteParams(GetByIdParams), responseWrap(RiskController.getRiskById));
router.get('/risk/:id/newAssessment', validateRouteParams(GetByIdParams), responseWrap(RiskController.generateNewAssessment));
router.post('/risk', responseWrap(RiskController.createRisk));
router.get('/risk', responseWrap(RiskController.getRisks));
router.put('/risk', responseWrap(RiskController.updateRisk));
router.delete('/risk/:id', responseWrap(RiskController.deleteRisk));
router.get("/risk-import/:importDocumentId", responseWrap(RiskController.importRisk))

export default router;