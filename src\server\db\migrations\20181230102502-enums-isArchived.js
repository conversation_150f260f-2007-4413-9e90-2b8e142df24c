'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    const tableDefinition = await queryInterface.describeTable('enums');
    if (tableDefinition['isArchived']) {
      return;
    }

    await queryInterface.addColumn('enums', 'isArchived', {
      type:Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('enums', 'isArchived');
  }
};
