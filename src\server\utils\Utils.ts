import { isNullOrUndefined, inspect, isString, isNumber } from 'util';

export const findEqual = <T = any>(array: Array<T>, find: T): T | undefined => {
	const deepFind: string = inspect(find, true, null);
	return array.find(val => inspect(val, true, null) === deepFind);
}



/**
 * Reliably converts any data that meets the below formats into an array of numbers, returning a blank array
 * for data that does not contain a valid array of integers
 * 
 * An array of strings that can be converted into numbers should also return a valid array of numbers.
 * 
 * Acceptable formats are:
 * null
 * blank string
 * 1
 * 1,2,3
 * [1]
 * [1,2,3]
 * "1"
 * "1","2","3"
 * ["1"]
 * ["1","2","3"]
 *
 * @param {string} data  The data to convert
 */
export const numStrToArr = (data: any): number[] => {
	let stringData = Array.isArray(data) ? data.join(",") : data;

	if (
		isNullOrUndefined(stringData) ||
		(
			isString(stringData) &&
			stringData.length === 0
		) ||
		(
			isNumber(stringData) &&
			stringData === 0
		)
	) return [];

	if (isString(stringData)) {
		stringData = stringData.replace(/"/g, "");
	}

	if (isNumber(stringData)) {
		return [Number(stringData)];
	}

	if (isString(stringData) && !!stringData.match(/^[0-9,.$]+$/)) return stringData.split(",").map(val => Number(val));

	if (isString(stringData)) {
		if (stringData.substring(0, 1) === '[' && stringData.substring(stringData.length - 1, stringData.length) === ']') {
			const arrData = JSON.parse(stringData).map((arrItem: any) => {
				if (isNullOrUndefined(arrItem)) return undefined;
				if (isNumber(arrItem)) return Number(arrItem);
				if (isString(arrItem) && !!arrItem.match(/^[0-9,.$]+$/)) return Number(arrItem);

				return undefined;
			})
				.filter(arrItem => !!arrItem);

			return arrData;
		}
	}

	return [];
}