import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { adminMiddleware } from '../middleware/adminMiddleware';
import {LocationController} from '../controller/location.controller';
import { validateBodyParams, validateQueryParams, validateRouteParams } from '../middleware/validationMidleware';
import {
  CreateLocationInputModel,
  LocationsQueryParams,
  UpdateLocationInputModel
} from '../../common/contracts/location';
import { GetByIdParams } from '../../common/contracts/common';
import { preFillDefaultQueryParams } from '../middleware/preFillMiddleware';

const router = express.Router();

/**
 Locations routes
 */
router.get('/location',
  preFillDefaultQueryParams({skip: '0', limit: '10', sortBy: 'name', order: 'asc'}),
  validateQueryParams(LocationsQueryParams),
  responseWrap(LocationController.getLocations));

router.get('/location/:id', validateRouteParams(GetByIdParams), responseWrap(LocationController.getLocationById));
router.post('/location', adminMiddleware, validateBodyParams(CreateLocationInputModel), responseWrap(LocationController.createLocation));
router.put('/location', adminMiddleware, validateBodyParams(UpdateLocationInputModel), responseWrap(LocationController.updateLocation));
router.delete('/location/:id', adminMiddleware, validateRouteParams(GetByIdParams), responseWrap(LocationController.archiveLocation));

export default router;
