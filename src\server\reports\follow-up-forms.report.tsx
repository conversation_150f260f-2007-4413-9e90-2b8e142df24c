import * as React from 'react';
import * as moment from 'moment';
import 'moment-timezone';
import { Document, Page, StyleSheet } from '@react-pdf/renderer';
import { IFormOutputValidationModel } from "../../common/contracts/form";
import { IEnumsOutputModel } from "../../common/contracts/enums";
import FormLocation from "../db/models/formLocation.model";
import { IRecordWithDateOutputModel } from "../../common/contracts/record";
import { FollowUpFormStage0 } from "./follow-up-stage0.report";
import { FollowUpFormStage1 } from "./follow-up-stage1.report";
import { FollowUpFormStage2 } from "./follow-up-stage2.report";
import { timeZone } from '../../common/constants';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'col',
    backgroundColor: '#FFFFFF',
    padding: 20,
    paddingLeft: 30,
  }
});

export class FollowUpFormsReport extends React.Component<{
  formData: IFormOutputValidationModel,
  records: IRecordWithDateOutputModel[],
  location: FormLocation | null,
  reportFormTypes: IEnumsOutputModel[],
  followUpGroupMap: {[n: number]: string},
  reassignedUserMap: {[n: number]: string},
}> {
  render() {

    return (

      <Document>
        <Page size="A4" style={styles.page} wrap={true}>
          {
            this.props.records.map((record, i) => {

              switch (record.stage) {

                case 0: return <FollowUpFormStage0
                  key={i}
                  recordData={record}
                  location={this.props.location}
                  reportFormTypes={this.props.reportFormTypes}
                  followUpGroupMap={this.props.followUpGroupMap}
                  dueAt={this.props.formData.dueAt ? moment(this.props.formData.dueAt).tz(timeZone).format('DD/MM/YYYY') : ''}
                />;

                case 1: return <FollowUpFormStage1
                  key={i}
                  recordData={record}
                  followUpGroupMap={this.props.followUpGroupMap}
                />;

                case 2: return <FollowUpFormStage2
                  key={i}
                  recordData={record}
                  followUpGroupMap={this.props.followUpGroupMap}
                  reassignedUserMap={this.props.reassignedUserMap}
                />;

                default: return null;
              }
            })
          }
        </Page>
      </Document>
    );
  }
}
