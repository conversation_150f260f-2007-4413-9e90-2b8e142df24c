const sass = require('sass');

module.exports = (config, options) => {
  // Find and update sass-loader rules
  const rules = config.module.rules;

  for (let rule of rules) {
    if (rule.use) {
      for (let use of rule.use) {
        if (use.loader && use.loader.includes('sass-loader')) {
          use.options = use.options || {};
          use.options.implementation = sass;
          use.options.sassOptions = {
            fiber: false,
          };
        }
      }
    }

    // Handle nested rules
    if (rule.rules) {
      for (let nestedRule of rule.rules) {
        if (nestedRule.use) {
          for (let use of nestedRule.use) {
            if (use.loader && use.loader.includes('sass-loader')) {
              use.options = use.options || {};
              use.options.implementation = sass;
              use.options.sassOptions = {
                fiber: false,
              };
            }
          }
        }
      }
    }
  }

  return config;
};
