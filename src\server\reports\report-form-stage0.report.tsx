import * as React from 'react';
import * as moment from 'moment';
import 'moment-timezone';
import { Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import { IEnumsOutputModel } from "../../common/contracts/enums";
import FormLocation from "../db/models/formLocation.model";
import Footer from "./components/footer";
import { IRecordWithDateOutputModel } from "../../common/contracts/record";
import { timeZone } from '../../common/constants';
import * as fs from 'fs';
import * as path from 'path';

const styles = StyleSheet.create({

  header: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  logo: {
    flex: 1
  },
  logoSpacer: {
    flex: 1,
    'flex-grow': 2
  },

  headerBar: {
    flex: 1,
    'flex-grow': 2,
    height: 16,
    borderBottomWidth: 3,
    borderBottomColor: '#616266',
    borderBottomStyle: 'solid',
  },
  formTitle: {
    flex: 1,
    fontSize: 24,
    marginBottom: 10,
    color: '#ed7d31',
  },

  referenceBox: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderRadius: 3,
    borderStyle: 'dashed',
    padding: 12
  },
  labelReference: {
    display: 'block',
    fontSize: 10,
    marginBottom: 5,
    textAlign: 'center',
  },

  firstRow: {
    flexDirection: 'row',
    marginTop: 10,
    marginBottom: 14,
  },
  firstRowItem: {
    width: '25%',
    flexDirection: 'col',
    fontSize: 11,
  },
  labelText: {
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
  },
  inlineLabel: {
    color: '#555555',
    marginRight: 10,
    fontSize: 11,
  },
  centralBlock: {
    flexDirection: 'row',
  },
  leftCentralBlock: {
    flexDirection: 'col',
    flex: 2,
    paddingRight: 10,
  },
  summaryText: {
    fontSize: 11,
    color: '#000000',
    lineHeight: '180%',
    paddingLeft: 12,
  },
  originatorBlock: {
    flexDirection: 'row',
    padding: 10,
    borderWidth: 1,
    borderColor: '#999999',
    borderStyle: 'solid',
    marginBottom: 12,
  },
  inlineView: {
    flexDirection: 'row',
    flex: 1,
  },
  originatorValue: {
    fontSize: 11,
    flex: 1,
  },
  familyMemberBlock: {
    flexDirection: 'col',
    padding: 10,
    borderWidth: 1,
    borderColor: '#999999',
    borderStyle: 'solid',
    marginBottom: 12,
  },
  familyMemberFirstRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
});

const FirstRow = ({ children }) => {
  return (<View style={styles.firstRow}>
    {children}
  </View>);
};

const FirstRowItem = ({ name, value }) => {
  return (<View style={styles.firstRowItem}>
    <Text style={styles.labelText}>{name}:</Text>
    <Text>{value}</Text>
  </View>);
};

/*
const CareResidentView = ({ originatorType, propertyMap }: { originatorType: IEnumsOutputModel | undefined, propertyMap: any }) => (
  <View style={styles.originatorBlock}>
    <Text style={styles.originatorValue}>{originatorType ? originatorType.value : ''}</Text>
    <View style={styles.inlineView}>
      <Text style={styles.inlineLabel}>Unit/SA Number:</Text>
      <Text style={styles.originatorValue}>{propertyMap['residentRoomNumber'] ? propertyMap['residentRoomNumber'].stringData : ''}</Text>
    </View>
    <Text style={styles.originatorValue}>{propertyMap['residentEmail'] ? propertyMap['residentEmail'].stringData : ''}</Text>
  </View>
);

const FamilyMemberView = ({ originatorType, propertyMap }: { originatorType: IEnumsOutputModel | undefined, propertyMap: any }) => (
  <View style={styles.familyMemberBlock}>
    <View style={styles.familyMemberFirstRow}>
      <Text style={styles.originatorValue}>{originatorType ? originatorType.value : ''}</Text>
      <View style={styles.inlineView}>
        <Text style={styles.inlineLabel}>Room Number:</Text>
        <Text style={styles.originatorValue}>{propertyMap['residentRoomNumber'] ? propertyMap['residentRoomNumber'].stringData : ''}</Text>
      </View>
      <Text style={styles.originatorValue}>{propertyMap['residentName'] ? propertyMap['residentName'].stringData : ''}</Text>
    </View>
    <View style={styles.inlineView}>
      <Text style={styles.inlineLabel}>Originator Email:</Text>
      <Text style={styles.originatorValue}>{propertyMap['originatorEmail'] ? propertyMap['originatorEmail'].stringData : ''}</Text>
    </View>
  </View>
);

const StaffMemberView = ({ originatorType, propertyMap }: { originatorType: IEnumsOutputModel | undefined, propertyMap: any }) => (
  <View style={styles.originatorBlock}>
    <Text style={styles.originatorValue}>{originatorType ? originatorType.value : ''}</Text>
    <Text style={styles.originatorValue}>{propertyMap['originatorEmail'] ? propertyMap['originatorEmail'].stringData : ''}</Text>
  </View>
);
*/

export class ReportFormStage0 extends React.Component<{
  recordData: IRecordWithDateOutputModel,
  originatorTypes: IEnumsOutputModel[],
  reportFormTypes: IEnumsOutputModel[],
  location: FormLocation | null,
  groupName: string,
}> {
  render() {

    const propertyMap = {};

    this.props.recordData.properties.forEach(prop => {
      propertyMap[prop.property.name] = prop;
    });

    // const originatorType = this.props.originatorTypes.find(item => item.id === propertyMap['originatorTypeId'].enumId);
    const reportType = this.props.reportFormTypes.find(item => item.id === propertyMap['reportFormType'].enumId);
    const updatedByUser = this.props.recordData.createdByUser ? this.props.recordData.createdByUser.firstName + ' ' + this.props.recordData.createdByUser.lastName : '';
    const updatedAt = moment(this.props.recordData.createdAt).tz(timeZone).format('DD-MM-YYYY');

    const logoFile = fs.readFileSync(path.resolve(__dirname, '../../app/assets/images/logo.png'));
    const logo64 = new Buffer(logoFile).toString('base64');
    const logoSrc = 'data:image/png;base64, ' + String(logo64);

    /*
      <Text style={styles.labelText}>Originator Type:</Text>
      {originatorType && (
        originatorType.value === 'Care Resident' ||
        originatorType.value === 'ILU Resident'
      ) &&
        <CareResidentView originatorType={originatorType} propertyMap={propertyMap} />
      }
      {originatorType && originatorType.value === 'Family Member' &&
        <FamilyMemberView originatorType={originatorType} propertyMap={propertyMap} />
      }
      {originatorType && originatorType.value === 'Staff Member' &&
        <StaffMemberView originatorType={originatorType} propertyMap={propertyMap} />
      }
    */

    return (
      <View>

        {<View style={styles.header} wrap={false}>
          <View style={styles.logoSpacer} />
          <View style={styles.logo}>
            <Image src={logoSrc} />
          </View>
          <View style={styles.logoSpacer} />
        </View>}

        <View style={styles.header} wrap={false}>
          <View style={styles.formTitle}>
            <Text>FEEDBACK FORM</Text>
          </View>
          <View style={styles.headerBar} />
        </View>

        <FirstRow>
          <FirstRowItem name="Originator Name" value={propertyMap['originatorName'] ? propertyMap['originatorName'].stringData : ''} />
          <FirstRowItem name="Location" value={this.props.location ? this.props.location.name : ''} />
          <FirstRowItem name="Type" value={reportType ? reportType.value : ''} />
          <FirstRowItem name="Department" value={this.props.groupName} />
        </FirstRow>

        <View style={styles.centralBlock}>
          <View style={styles.leftCentralBlock}>
            <Text style={styles.labelText}>Brief Summary:</Text>
            <View>
              <Text style={styles.summaryText}>
                {propertyMap['summary'] ? propertyMap['summary'].stringData : ''}
              </Text>
            </View>
          </View>
        </View>

        <Footer updatedByUser={updatedByUser} updatedAt={updatedAt} />

      </View>
    );
  }
}
