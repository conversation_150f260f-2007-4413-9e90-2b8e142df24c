'use strict';

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			// Check if table exists first
			const tableExists = await queryInterface.showAllTables().then(tables =>
				tables.includes('risks')
			);

			if (!tableExists) {
				console.log('Table "risks" does not exist, skipping migration');
				return Promise.resolve(true);
			}

			const tableDefinition = await queryInterface.describeTable('risks');

			// Only add column if it doesn't exist
			if (!tableDefinition['residentName']) {
				await queryInterface.addColumn('risks', 'residentName', {
					type: Sequelize.STRING,
					allowNull: true
				});
			}

			return Promise.resolve(true);
		} catch (e) {
			console.log("ERROR MIGRATING SCRIPT");
			console.log(e);
			// Don't throw error if column already exists
			if (e.message && e.message.includes('already exists')) {
				console.log('Column already exists, continuing...');
				return Promise.resolve(true);
			}
			throw e;
		}
	},

	down: async (queryInterface, Sequelize) => {
		return false;
	}
};
