import { BadRequestError, NotFoundError, ServerError } from '../../model/HttpErrors';
import {
  ICreateTagInputModel,
  ITagQueryParams,
  IUpdateTagInputModel
} from '../../common/contracts/tag';
import { IGetByIdParams } from '../../common/contracts/common';
import { Sequelize } from 'sequelize-typescript';
import Tag from '../db/models/tag.model';

export class TagController {

  public static async getTags(req: JwtRequest & Query<ITagQueryParams>) {

    const queryParams = req.query as ITagQueryParams;

    const attributes: any[] = ['id', 'name', 'isMeta', 'createdAt', 'updatedAt'];

    const { rows, count } = await Tag.findAndCountAll<Tag>({
      limit: queryParams.limit,
      offset: queryParams.skip,
      where: {
        isArchived: false,
      },
      attributes,
      order: [[Sequelize.literal(queryParams.sortBy) as any as string, queryParams.order.toUpperCase()]],
    });

    return {
      items: rows,
      totalCount: count,
    };
  }

  public static async getTagById(req: JwtRequest) {

    const { id: tagId } = req.params as unknown as IGetByIdParams;

    const tag = await Tag.findOne({
      where: {
        id: tagId,
        isArchived: false,
      },
      attributes: {
        exclude: ['isArchived'],
      }
    });

    if (!tag) {
      throw new NotFoundError('Tag is not found.');
    }

    return tag;
  }

  public static async createTag(req: any, res: any) {

    const { name, isMeta } = req.body as ICreateTagInputModel;

    try {

      return await Tag.create({ name, isMeta }, { fields: ['name', 'isMeta'] });

    } catch (e) {
      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('Document collection with this name already exists.');
      } else {
        console.log('createTag exception', e);
        throw new ServerError();
      }
    }

  }

  public static async updateTag(req: any, res: any) {

    const { id, name, isMeta } = req.body as IUpdateTagInputModel;

    const tag: Tag | null = await Tag.findByPrimary<Tag>(id);

    if (!tag || tag.isArchived) {
      throw new NotFoundError('Tag is not found');
    }

    try {
      const [recordsUpdated] = await Tag.update({ name, isMeta }, { where: { id: id } });
      return { status: 'ok', recordsUpdated };
    } catch (e) {
      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('Tag with this name already exists.');
      } else {
        console.log('updateTag exception', e);
        throw new ServerError();
      }
    }

  }

  public static async archiveTag(req: JwtRequest) {

    const { id: tagId } = req.params as unknown as IGetByIdParams;

    const tag: Tag | null = await Tag.findByPrimary(tagId);

    if (!tag || tag.isArchived) {
      throw new NotFoundError('Tag is not found.');
    }

    const [recordsArchived] = await Tag.update({
      isArchived: true,
    }, {
      where: { id: tagId }
    });

    return {
      archived: recordsArchived,
    };

  }

}
