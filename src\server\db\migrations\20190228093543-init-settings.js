'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    let tables = await queryInterface.showAllTables();

    if (!tables.includes('system_settings')) {
      await queryInterface.createTable('system_settings', {
        id: {
          allowNull: false,
          autoIncrement: true,
          primaryKey: true,
          type: Sequelize.INTEGER
        },
        key:  {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true,
        },
        isEmailsEnabled: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false,
        }
      });
    }

    const existingRecord = await queryInterface.rawSelect('system_settings', {
      where: {
        key: 'actual_system_settings',
      }
    }, ['key']);

    if (!existingRecord) {
      await queryInterface.bulkInsert('system_settings', [{
        key: 'actual_system_settings',
        isEmailsEnabled: false,
      }], {});
    }

  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('system_settings');
  }

};
