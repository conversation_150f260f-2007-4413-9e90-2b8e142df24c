import { Table, Column, Model, <PERSON><PERSON><PERSON>, BelongsToMany, DataType } from 'sequelize-typescript';
import Form from './form.model';
import Risks from './risk.model';
import User from './user.model';
import UserToLocation from './user_to_location.model';

@Table({
  tableName: 'form_locations'
})
class FormLocation extends Model<FormLocation> {

  @Column
  name: string;

  @HasMany(() => Form)
  forms: Form[];



  @HasMany(() => Risks)
  risks: Risks[];

  @BelongsToMany(() => User, () => UserToLocation)
  users: User[];

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isArchived: boolean;

  @Column({
    type: DataType.VIRTUAL,
  })
  usersCount: number;

}

export default FormLocation;
