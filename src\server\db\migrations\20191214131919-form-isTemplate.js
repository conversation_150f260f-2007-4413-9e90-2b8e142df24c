'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    let tableDefinition = await queryInterface.describeTable('forms');

    if (!tableDefinition['isTemplate']) {
      await queryInterface.addColumn('forms', 'isTemplate', {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: true,
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('forms', 'isTemplate');
  }
};
