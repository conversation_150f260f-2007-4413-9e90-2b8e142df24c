import * as t from 'io-ts';
import { PaginationResponse } from './pagination';
import { constructType } from './extend';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { SortOrderValues } from './common';
export function constructDocumentTagType<T>(dateType: t.Type<T>) {
  return t.type({
    id: t.number,
    documentId: t.number,
    tagId: t.number,
    createdAt: dateType,
    updatedAt: dateType,
  });
}

export const DocumentTagOutputModel = constructDocumentTagType(t.string);

export interface IDocumentTagOutputModel extends t.TypeOf<typeof DocumentTagOutputModel> {}

export type DocumentTagPaginationResponse = PaginationResponse<IDocumentTagOutputModel>;

export const UpdateDocumentTagInputModel = t.exact(t.type({
  id:t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  documentId: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  tagId: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
}));

export interface IUpdateDocumentTagInputModel extends t.TypeOf<typeof UpdateDocumentTagInputModel> {}

export const CreateDocumentTagInputModel = t.exact(t.type({
    documentId: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
    tagId: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
}));

export interface ICreateDocumentTagInputModel extends t.TypeOf<typeof CreateDocumentTagInputModel> {}

// See comment in /contracts/common.ts for explanation for this ignore
// @ts-ignore
export const DocumentTagSortedField = t.union(['createdAt', 'updatedAt', 'name'].map(f => t.literal(f)));

export type IDocumentTagSortedField = t.TypeOf<typeof DocumentTagSortedField>;

export const DocumentTagQueryParams = constructType({
  skip: IntFromString,
  limit: IntFromString,
  sortBy: DocumentTagSortedField,
  order: SortOrderValues
}, {
  // countUsers: t.refinement(IntFromString, v => v === 1 || v === 0, 'valid countUsers parameter' ),
});

export interface IDocumentTagQueryParams extends t.TypeOf<typeof DocumentTagQueryParams> {}
