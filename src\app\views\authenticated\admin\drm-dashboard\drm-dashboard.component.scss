.top-card-filter {
  cursor: pointer;
  box-shadow: 0 0 8px transparent;
  &.active {
    /*box-shadow: 3px 3px 18px #555555;*/
    border: 3px solid #0A549A;
  }
  &:hover:not(.active) {
    box-shadow: 1px 1px 8px #999999;
  }
  transition: all 0.5s;
}

.sortable-column {
  cursor: pointer;
  white-space: nowrap;
  & > i {
    display: none;
  }
  &.active {
    color: #007bff;
    & > i {
      display: inline-block;
      margin-left: 10px;
    }
    &.reverse {
      & > i {
        transform: rotate(180deg);
      }
    }
  }
}

.dropdown-link-wrapper {
  padding: 6px 10px 0 4px;
}

a.dropdown-link {
  color: #007bff;
  cursor: pointer;
  white-space: nowrap;
  &:hover {
    color: #0056b3;
  }
}

.second-line-filter {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 6px;
}

.date-filter-block {
  flex: 2;
  align-self: flex-start;
  padding-top: 2px;
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  min-height: 40px;
  padding-bottom: 4px;
}

.show-all-filter-block {
  flex: 1;
  align-self: flex-end;
  padding-bottom: 4px;
}

.date-picker-container {
  padding: 0 6px;
  max-width: 180px;
  min-width: 140px;
}

.search-input-group {
  position: relative;
}

.search-input-clear {
  position: absolute;
  top: 10px;
  right: 8px;
  font-size: 18px;
  font-weight: bolder;
  width: 20px;
  height: 20px;
  cursor: pointer;
  text-align: center;
  line-height: 1;
}

.search-input-control::-ms-clear {
  display: none;
}

.pdf-export-icon {
  font-size: 28px;
  cursor: pointer;
  color: #007bff;
  margin-top: 10px;
  margin-left: 10px;
}
