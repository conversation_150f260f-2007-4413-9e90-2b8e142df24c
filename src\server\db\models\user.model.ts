import {
  Sequelize,
  Table,
  Column,
  Model,
  DataType,
  BelongsToMany, Has<PERSON>any
} from 'sequelize-typescript';

import UserGroup from './group.model';
import UserToGroup from './user_to_group.model';
import { UserRole } from '../../../common/commontTypes';
import FormDocument from './formDocument.model';
import FormLocation from './formLocation.model';
import UserToLocation from './user_to_location.model';

@Table({
  tableName: 'users',
})
class User extends Model<User> {

  @Column({
    type: DataType.STRING(64),
    unique: true,
  })
  username: string;

  @Column({
    type: DataType.STRING(64),
    defaultValue: '',
  })
  firstName: string;

  @Column({
    type: DataType.STRING(64),
    defaultValue: '',
  })
  lastName: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
  })
  phone: string;

  @Column
  password: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: 'user',
  })
  role: UserRole;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)'),
    onUpdate: 'CURRENT_TIMESTAMP(3)'
  })
  updatedAt: Date;

  @BelongsToMany(() => UserGroup, () => UserToGroup)
  groups: UserGroup[];

  @BelongsToMany(() => FormLocation, () => UserToLocation)
  locations: UserGroup[];

  @HasMany(() => FormDocument)
  documents: FormDocument[];

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isArchived: boolean;

}

export default User;

