/*
  A record is a single submission to a form
 */

import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
  Sequelize,
  BelongsToMany
} from 'sequelize-typescript';
import FormDocument from './formDocument.model';
import Form from './form.model';
import User from './user.model';
import FormRecordProperty from './formRecordProperty.model';
import DocumentToRecord from './document_to_record.model';
import TickedDocumentToRecord from './ticked_document_to_record.model';

@Table({
  tableName: 'forms_record'
})
class FormRecord extends Model<FormRecord> {

  /* Starting at 1 the sequence should count up through each FormRecord within a form */
  @Column({
    type: DataType.INTEGER,
    defaultValue: Sequelize.literal('1')
  })
  sequence: number;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  createdAt: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  createdById: number | null;

  @BelongsTo(() => User, 'createdById')
  createdByUser: number;

  @ForeignKey(() => Form)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  formId: number;

  @BelongsTo(() => Form, 'formId')
  form: number;

  @BelongsToMany(() => FormDocument, () => DocumentToRecord)
  documents: FormDocument[];

  @BelongsToMany(() => FormDocument, () => TickedDocumentToRecord)
  tickedDocuments: FormDocument[];

  @HasMany(() => FormRecordProperty)
  properties: FormRecordProperty[];

  @Column({
    type: DataType.INTEGER,
    allowNull: false
  })
  stage: number;


  // This field should indicate that the form has been submitted and is ready for the next step
  // Each "isComplete" record should be output during the read-only stage of the process
  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false
  })
  isComplete: boolean;
}

export default FormRecord;

