version: '3'

services:

  mysql:
    image: mysql:8.0.13
    hostname: mysql
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    expose:
      - 3306
    ports:
      - 3306:3306
    environment:
      MYSQL_ROOT_PASSWORD: mysql
      MYSQL_DATABASE: bethany
      <PERSON>_USER: BethanyDB
      MYSQL_PASSWORD: BAC123
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - my-bridge

  adminer:
    image: adminer
    restart: always
    ports:
      - 8080:8080
    networks:
      - my-bridge

volumes:
  mysql-data:

networks:
  my-bridge:
    driver: bridge
