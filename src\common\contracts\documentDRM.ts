import * as t from 'io-ts';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { PaginationResponse } from './pagination';
import { SortOrderValues } from './common';
import { constructType } from './extend';
import { ISODateRegEx } from '../constants';

export function constructDocumentType<T>(dateType: t.Type<T>) {
  return t.intersection([
    t.type({
      id: t.number,
      name: t.string,
      type: t.string,
      standard: t.number,
      nextReviewAt: dateType,
      createdAt: dateType,
      updatedAt: dateType,
      documentCollectionId: t.number,
      departmentId: t.number,
      documentCode: t.string,
      documentIdentity: t.string,
      documentCollection: t.any
    }),
    t.partial({
      isArchived: t.boolean
    })
  ]);
}

export const DocumentOutputModel = constructDocumentType(t.string);

export interface IDocumentOutputModel extends t.TypeOf<typeof DocumentOutputModel> { }

export type DocumentPaginationResponse = PaginationResponse<IDocumentOutputModel>;

export const UpdateDocumentInputModel = t.exact(t.type({
  id: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  documentCollectionId: t.refinement(t.number, id => id > 0, 'documentCollectionId is invalid'),
  departmentId: t.refinement(t.number, id => id > 0, 'departmentId is invalid'),
  name: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Name should not be empty or it\'s too long '),
  type: t.refinement(t.string, v => (v === 'POLICY' || v === 'PROCEDURE' || v === 'FORM' || v === 'AUDIT'), 'Type is not valid'),
  standard: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  nextReviewAt: t.refinement(t.string, s => s.length > 0 && !!(s as string).match(ISODateRegEx), 'Due date is invalid'),
  documentIdentity: t.refinement(t.string, v => v.length < 25, 'Identity should not be longer than 25 characters'),
}));

export interface IUpdateDocumentInputModel extends t.TypeOf<typeof UpdateDocumentInputModel> { }

export const CreateDocumentInputModel = t.exact(t.type({
  documentCollectionId: t.refinement(t.number, id => id > 0, 'documentCollectionId is invalid'),
  departmentId: t.refinement(t.number, id => id > 0, 'departmentId is invalid'),
  name: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Name should not be empty or it\'s too long '),
  type: t.refinement(t.string, v => (v === 'POLICY' || v === 'PROCEDURE' || v === 'FORM' || v === 'AUDIT'), 'Type is not valid'),
  standard: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  nextReviewAt: t.refinement(t.string, s => s.length > 0 && !!(s as string).match(ISODateRegEx), 'Due date is invalid'),
  documentIdentity: t.refinement(t.string, v => v.length < 25, 'Identity should not be longer than 25 characters'),
}));

export interface ICreateDocumentInputModel extends t.TypeOf<typeof CreateDocumentInputModel> { }

// See comment in /contracts/common.ts for explanation for this ignore
// @ts-ignore
export const DocumentSortedField = t.union(['createdAt', 'updatedAt', 'name', 'type', 'documentCollectionId', 'nextReviewAt', 'departmentId', 'standard', 'documentCode', 'documentIdentity'].map(f => t.literal(f)));

export type IDocumentSortedField = t.TypeOf<typeof DocumentSortedField>;

export const DocumentQueryParams = constructType({
  skip: IntFromString,
  limit: IntFromString,
  sortBy: DocumentSortedField,
  order: SortOrderValues
}, {
  documentCollectionId: t.string,
  includeArchived: t.string
});

export interface IDocumentQueryParams extends t.TypeOf<typeof DocumentQueryParams> { }
