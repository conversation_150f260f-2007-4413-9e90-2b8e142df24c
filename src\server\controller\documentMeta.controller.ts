import DocumentMeta from "../db/models/documentMeta.model";
import { IGetByIdParams } from "../../common/contracts/common";
import { NotFoundError, BadRequestError, ServerError } from "../../model/HttpErrors";
import { ICreateDocumentMetaInputModel, IDocumentMetaSyncInputModel, IUpdateDocumentMetaInputModel } from "../../common/contracts/documentMeta";


export class DocumentMetaController {
  public static async getDocumentMetasByDocumentId(req: JwtRequest) {

    const { id } = req.params as unknown as IGetByIdParams;

    const tags = await DocumentMeta.findAll({
      where: {
        documentId: id,
        // @TODO: Delete this field, delete document meta where archived is true
        isArchived: false,
      }
    });

    if (!tags) {
      throw new NotFoundError('Tag is not found.');
    }

    return tags;
  }

  public static async getDocumentMetabyId(req: JwtRequest) {

    const { id } = req.params as unknown as IGetByIdParams;

    const tags = await DocumentMeta.findOne({
      where: {
        id: id,
      }
    });

    if (!tags) {
      throw new NotFoundError('Tag is not found.');
    }

    return tags;
  }

  public static async syncDocumentMeta(req: any) {
    const { id: documentId } = req.params as IGetByIdParams;
    const tagData = req.body as IDocumentMetaSyncInputModel;

    await DocumentMeta.destroy({ where: { documentId } });

    for (let i = 0; i < tagData.length; i++) {
      const tmpMeta = new DocumentMeta();
      tmpMeta.documentId = documentId;
      tmpMeta.tagId = tagData[i].tagId;
      tmpMeta.value = tagData[i].value;
      await tmpMeta.save();
    }

    return { success: true };
  }

  public static async createDocumentMeta(req: any, res: any) {

    const { documentId, tagId, value } = req.body as ICreateDocumentMetaInputModel;

    try {

      return await DocumentMeta.create({ documentId, tagId, value }, { fields: ['documentId', 'tagId', 'value'] });

    } catch (e) {
      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('Document meta with this name already exists.');
      } else {
        console.log('createTag exception', e);
        throw new ServerError();
      }
    }
  }

  public static async archiveDocumentMeta(req: JwtRequest) {

    const { id } = req.params as unknown as IGetByIdParams;

    const tag: DocumentMeta | null = await DocumentMeta.findByPrimary(id);

    if (!tag || tag.isArchived) {
      throw new NotFoundError('Tag is not found.');
    }

    await DocumentMeta.destroy({ where: { id } });

    return {
      archived: 1,
    };

  }

  public static async updateDocumentMeta(req: any, res: any) {

    const { id, documentId, tagId, value } = req.body as IUpdateDocumentMetaInputModel;

    const tag: DocumentMeta | null = await DocumentMeta.findByPrimary<DocumentMeta>(id);

    if (!tag || tag.isArchived) {
      throw new NotFoundError('Tag is not found');
    }

    try {
      const [recordsUpdated] = await DocumentMeta.update({ documentId, tagId, value }, { where: { id: id } });
      return { status: 'ok', recordsUpdated };
    } catch (e) {
      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('Meta with this name already exists.');
      } else {
        console.log('updateTag exception', e);
        throw new ServerError();
      }
    }

  }

}