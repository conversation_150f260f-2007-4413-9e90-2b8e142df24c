import * as React from 'react';
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import * as moment from 'moment';
import 'moment-timezone';
import { IRecordWithDateOutputModel } from "../../common/contracts/record";
import { IRecordPropertyType } from "../../common/contracts/recordProperty";
import Footer from "./components/footer";
import FormLocation from "../db/models/formLocation.model";
import { IEnumsOutputModel } from "../../common/contracts/enums";
import { IFormOutputValidationModel } from '../../common/contracts/form';
import { timeZone } from '../../common/constants';

const styles = StyleSheet.create({
  wrapper: {
  },
  header: {
    flexDirection: 'row'
  },
  headerBar: {
    flex: 1,
    height: 16,
    borderBottomWidth: 3,
    borderBottomColor: '#616266',
    borderBottomStyle: 'solid',
  },
  formTitle: {
    flex: 4,
    fontSize: 24,
    marginBottom: 10,
    color: '#ed7d31',
  },
  firstRow: {
    flexDirection: 'row',
    marginTop: 10,
    marginBottom: 14,
  },
  firstRowItem: {
    flex: 1,
    flexDirection: 'col',
    fontSize: 11,
  },
  contactedOriginatorBlock: {
    flexDirection: 'row',
    fontSize: 11,
    marginTop: 10,
  },
  labelText: {
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
    marginRight: 5,
  },
  centralBlock: {
    flexDirection: 'row',
  },
  leftCentralBlock: {
    flexDirection: 'col',
    flex: 2,
    paddingRight: 10,
  },
  summaryText: {
    fontSize: 11,
    color: '#000000',
    lineHeight: '180%',
    paddingLeft: 12,
    marginBottom: 8,
  },
});

const FirstRow = ({ children }) => (
  <View style={styles.firstRow}>
    {children}
  </View>
);

const FirstRowItem = ({ name, value }) => {
  return (<View style={styles.firstRowItem}>
    <Text style={styles.labelText}>{name}:</Text>
    <Text>{value}</Text>
  </View>);
};

export class AuditFormStage0 extends React.Component<{
  formData: IFormOutputValidationModel,
  recordData: IRecordWithDateOutputModel,
  userGroupMap: { [n: number]: string },
  location: FormLocation | null,
  auditFormTypes: IEnumsOutputModel[],
  dueAt: string
}> {
  render() {

    const propertyMap: { [k: string]: IRecordPropertyType } = {};

    this.props.recordData.properties.forEach(prop => {
      propertyMap[prop.property.name] = prop;
    });

    const updatedByUser = this.props.recordData.createdByUser ? this.props.recordData.createdByUser.firstName + ' ' + this.props.recordData.createdByUser.lastName : '';
    const updatedAt = moment(this.props.recordData.createdAt).tz(timeZone).format('DD-MM-YYYY');
    const reportType = propertyMap['type'] ? this.props.auditFormTypes.find(item => item.id === propertyMap['type'].enumId) : null;
    const dueAt = this.props.formData.dueAt ? moment(this.props.formData.dueAt).tz(timeZone).format('DD-MM-YYYY') : null;

    const departmentProp = this.props.recordData.properties.find(prop => prop.property.name === 'department');
    const department = departmentProp && departmentProp.intData ? this.props.userGroupMap[departmentProp.intData] : null;

    const periodFrom = propertyMap['periodFrom'] ? moment(propertyMap['periodFrom'].dateData).tz(timeZone).format('DD-MM-YYYY') : null;
    const periodTo = propertyMap['periodTo'] ? moment(propertyMap['periodTo'].dateData).tz(timeZone).format('DD-MM-YYYY') : null;

    return (
      <View style={styles.wrapper}>
        <View style={styles.header} wrap={false}>
          <View style={styles.formTitle}>
            <Text>PROCESSING OF COMPLETED AUDIT</Text>
          </View>
          <View style={styles.headerBar} />
        </View>

        <FirstRow>
          <FirstRowItem name="Audit Type" value={reportType ? reportType.value : ''} />
          <FirstRowItem name="Due Date" value={dueAt} />
        </FirstRow>

        <FirstRow>
          <FirstRowItem name="Location" value={this.props.location ? this.props.location.name : ''} />
          <FirstRowItem name="Department" value={department} />
        </FirstRow>

        <FirstRow>
          <FirstRowItem name="Period Covered By Audit" value={periodFrom} />
          <FirstRowItem name="" value={periodTo} />
        </FirstRow>

        <Footer updatedByUser={updatedByUser} updatedAt={updatedAt} />
      </View>
    );
  }
}
