import { ResourcePack } from "../../model/ResourcePack";
import ResourcePackDTO from '../db/models/resource-pack.model';
import ResourcePackDocument from "../db/models/resource-pack-documents.model";
import FormDocument from "../db/models/formDocument.model";
export class ResourcePackController {
    async create(req: JwtRequest) {
        const rp = req.body as ResourcePack;
        return ResourcePackDTO.create(rp);
    }

    async fetch() {
        return ResourcePackDTO.findAll();
    }

    async fetchById(req: JwtRequest) {
        const id = req.params.id;
        if (!id) {
            throw new Error('Resource pack id not found.')
        }

        return ResourcePackDTO.findById(id, { include: [{ model: ResourcePackDocument }] });
    }

    async update(req: JwtRequest) {
        const name = req.body.name;
        return ResourcePackDTO.update({ name }, { where: { id: req.params.id } })
    }

    async addAttachment(req: JwtRequest) {
        const id = req.params.id;
        const resourcePack = await ResourcePackDTO.findById(id, {
            include: [{
                model: ResourcePackDocument
            }]
        });

        if (!resourcePack) throw new Error('No resource pack found with id ' + id);
        const document = new ResourcePackDocument({
          path: req.body.fileName,
          formDocumentId: req.body.id,
          resourcePackId: resourcePack.id
        });
        
        await document.save();
        
        return ResourcePackDocument.findByPk(document.id, {
            include: [
              FormDocument
            ]
        });
    }

    async delete(req: JwtRequest) {
        const id = req.params.id;
        
        const result = await ResourcePackDTO.destroy({ where: { id } });
        
        return result;
    }

    async removeAttachment(req: JwtRequest) {
        const id = req.params.id;

        const result = await ResourcePackDocument.destroy({ where: { id } });
        
        return result;
    }

}
