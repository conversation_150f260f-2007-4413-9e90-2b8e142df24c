'use strict';

module.exports = {

  up: async (queryInterface, Sequelize) => {
    let tableDefinition = await queryInterface.describeTable('enums');

    if (!tableDefinition['resourcePackId']) {
      await queryInterface.addColumn('enums', 'resourcePackId', {
        type: Sequelize.INTEGER,
        references: {
            model: 'resource_packs',
            key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('enums', 'resourcePackId');
  }
};
