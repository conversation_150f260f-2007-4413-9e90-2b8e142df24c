'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      return await queryInterface.bulkInsert('groups', [{
        groupName: 'Risk Management',
      }, {
        groupName: 'Clinical Governance'
      }], {});
    } catch (err) {
      console.error("----------");
      console.error("ERROR DURING ADDiNG INSERTING GROUPS: Risk Management, Clinical Governance");
      console.error("----------");
      console.error(err);
    }
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('groups', [{
      groupName: 'Risk Management',
    }, {
      groupName: 'Clinical Governance'
    }], {});
  }
};
