import { Table, Column, Model, DataType, Sequelize, BelongsToMany, Unique } from 'sequelize-typescript';
import User from './user.model';
import UserToGroup from './user_to_group.model';

@Table({
  tableName: 'groups'
})
class UserGroup extends Model<UserGroup> {

  @Unique
  @Column
  groupName: string;

  @BelongsToMany(() => User, () => UserToGroup)
  users: User[];

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)'),
    onUpdate: 'CURRENT_TIMESTAMP(3)'
  })
  updatedAt: Date;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isArchived: boolean;

}

export default UserGroup;


