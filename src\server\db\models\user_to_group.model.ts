import { Table, Column, Model, ForeignKey } from 'sequelize-typescript';
import User from './user.model';
import UserGroup from './group.model';


@Table({
  tableName: 'user_to_group',
  timestamps: false,
})
class UserToGroup extends Model<UserToGroup> {

  @ForeignKey(() => User)
  @Column
  userId: number;

  @ForeignKey(() => UserGroup)
  @Column
  groupId: number;

}

export default UserToGroup;
