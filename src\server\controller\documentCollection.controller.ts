import { BadRequestError, NotFoundError, ServerError } from '../../model/HttpErrors';
import {
  ICreateDocumentCollectionInputModel,
  IDocumentCollectionQueryParams,
  IDocumentImportOutput,
  IUpdateDocumentCollectionInputModel
} from '../../common/contracts/documentCollection';
import { IGetByIdParams } from '../../common/contracts/common';
import { Sequelize } from 'sequelize-typescript';
import DocumentCollection from '../db/models/documentCollection.model';
import Document from '../db/models/document.model';
import { DocumentDRMService } from '../service/documentDRM.service';
import Form from '../db/models/form.model';
import documentInstance from '../db/models/documentInstance.model';
import "moment-timezone";
import * as moment from 'moment'
import logger from '../utils/Logger';
import FormDocument from '../db/models/formDocument.model';
import { DocumentType } from '../db/models/document.model';
import * as fs from 'fs';
import * as csv from "csvtojson";
import UserGroup from '../db/models/group.model';
import FormEnum from '../db/models/enum.model';
import { timeZone } from '../../common/constants';

const className = "DocumentCollectionController";
export class DocumentCollectionController {

  public static async getDocumentCollections(req: JwtRequest & Query<IDocumentCollectionQueryParams>) {

    const queryParams = req.query as IDocumentCollectionQueryParams;

    const attributes: any[] = ['id', 'name', 'createdAt', 'updatedAt'];

    const { rows, count } = await DocumentCollection.findAndCountAll<DocumentCollection>({
      limit: queryParams.limit,
      offset: queryParams.skip,
      where: {
        isArchived: false,
      },
      attributes,
      order: [[Sequelize.literal(queryParams.sortBy) as any as string, queryParams.order.toUpperCase()]],
    });

    return {
      items: rows,
      totalCount: count,
    };
  }

  public static async getDocumentCollectionById(req: JwtRequest) {

    const { id: documentCollectionId } = req.params as unknown as IGetByIdParams;

    const documentCollection = await DocumentCollection.findOne({
      where: {
        id: documentCollectionId,
        isArchived: false,
      },
      attributes: {
        exclude: ['isArchived'],
      }
    });

    if (!documentCollection) {
      throw new NotFoundError('DocumentCollection is not found.');
    }

    return documentCollection;
  }

  public static async createDocumentCollection(req: any, res: any) {

    const { name, reviewSchedule } = req.body as ICreateDocumentCollectionInputModel;

    try {
      return await DocumentCollection.create({ name, reviewSchedule }, { fields: ['name', 'reviewSchedule'] });
    } catch (e) {
      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('Document collection with this name already exists.');
      } else {
        logger.error('createDocumentCollection exception', e);
        throw new ServerError();
      }
    }

  }

  public static async updateDocumentCollection(req: any, res: any) {

    const { id, name, reviewSchedule } = req.body as IUpdateDocumentCollectionInputModel;

    const documentCollection: DocumentCollection | null = await DocumentCollection.findByPrimary<DocumentCollection>(id);

    if (!documentCollection || documentCollection.isArchived) {
      throw new NotFoundError('DocumentCollection is not found');
    }

    try {
      const [recordsUpdated] = await DocumentCollection.update({ name, reviewSchedule }, { where: { id: id } });
      return { status: 'ok', recordsUpdated };
    } catch (e) {
      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('DocumentCollection with this name already exists.');
      } else {
        logger.error('updateDocumentCollection exception', e);
        throw new ServerError();
      }
    }

  }

  public static async archiveDocumentCollection(req: JwtRequest) {

    const { id: documentCollectionId } = req.params as unknown as IGetByIdParams,
      nameSuffix = ' Archived ' + moment().format(),
      documentCollection: DocumentCollection | null = await DocumentCollection.findByPrimary(documentCollectionId);

    if (!documentCollection || documentCollection.isArchived) {
      throw new NotFoundError('archiveDocumentCollection: DocumentCollection is not found.');
    }

    const [recordsArchived] = await DocumentCollection.update({
      name: documentCollection.name + nameSuffix,
      isArchived: true,
    }, {
      where: { id: documentCollectionId }
    });

    const documentsInCollection = await Document.findAll({
      where: {
        documentCollectionId: documentCollectionId
      }
    });


    for (let index = 0; index < documentsInCollection.length; index++) {
      const formsAttached = await DocumentDRMService.getFormsAttached(documentsInCollection[index].id),
        completedForms = formsAttached.filter(form => !!form.archivedAt).map(form => form.id),
        deleteReason = "Document collection was archived."

      // Archives completed forms related to document collection
      for (let formIndex = 0; formIndex < completedForms.length; formIndex++) {
        const form = await Form.findByPrimary(completedForms[formIndex]);

        if (!form || form.isDeleted) {
          throw new NotFoundError(`archiveDocumentCollection: Form ${completedForms[formIndex]} is not found`);
        }

        await Form.update({
          isDeleted: true,
          deleteReason,
          archivedAt: new Date(),
        }, {
          where: { id: completedForms[formIndex] }
        });
      }

      //Archives document instances related to document collection
      const documentInstances = await documentInstance.findAll({
        where: {
          documentId: documentsInCollection[index].id
        }
      });

      for (let instanceIndex = 0; instanceIndex < documentInstances.length; instanceIndex++) {
        await documentInstance.update({ isArchived: true }, { where: { id: documentInstances[instanceIndex].id } });
      }

      await Document.update({
        isArchived: true,
        name: documentsInCollection[index].name + nameSuffix
      },
        {
          where: {
            id: documentsInCollection[index].id
          }
        });
    }
    return {
      archived: recordsArchived,
    };

  }

  public static async importDocuments(req: JwtRequest): Promise<IDocumentImportOutput> {
    const signature = className + ".importDocuments: ";
    const { id: importDocumentId } = req.params as unknown as IGetByIdParams;

    const formDocument = await FormDocument.findById(importDocumentId);

    if (!formDocument || !formDocument.path || !fs.existsSync(formDocument.path)) {
      throw new NotFoundError("Invalid Import Document");
    }

    const file = fs.readFileSync(formDocument.path);
    let csvData: any;

    const expectedFields = [
      'Collection',
      'Document ID',
      'Name',
      'Type',
      'Department',
      'Standard',
      'Date of next review'
    ] as [
        'Collection',
        'Document ID',
        'Name',
        'Type',
        'Department',
        'Standard',
        'Date of next review'
      ];

    try {
      csvData = await csv().fromString(file.toString());
    } catch (e) {
      throw new BadRequestError("Error reading CSV File");
    }

    if (csvData.length === 0) {
      throw new BadRequestError("Empty Import File");
    }


    const firstRecord = csvData[0];
    const missingFields = expectedFields.filter(field => !Object.prototype.hasOwnProperty.call(firstRecord, field));

    if (missingFields.length) {
      logger.error(signature + `Import rejected due to missing fields[${missingFields}]`);
      throw new BadRequestError("Import file contained Missing Fields " + missingFields.join(", "));
    }

    const typedData = csvData as { [K in typeof expectedFields[number]]: string }[];
    const result = {
      newRecords: 0,
      updatedRecords: 0
    };

    // Validate that all the data can be imported, or build errors
    const errors: string[] = [];
    const data: {
      docType?: DocumentType,
      department?: UserGroup,
      standard?: FormEnum,
      nextReviewAt?: Date
    }[] = typedData.map(data => { return {} });

    for (let i = 0; i < typedData.length; i++) {
      const docTypeStr = String(typedData[i].Type);
      const docTypeEnum = docTypeStr.toUpperCase();
      if (!Object.prototype.hasOwnProperty.call(DocumentType, docTypeEnum)) {
        errors.push(`Line ${i + 2}. Invalid document type [${docTypeStr}]. Must be one of ${Object.keys(DocumentType).join(", ")}`);
      } else {
        data[i].docType = DocumentType[docTypeEnum];
      }

      const department = await UserGroup.findOne({ where: { groupName: typedData[i].Department } });

      if (!department) {
        errors.push(`Line ${i + 2}. Department ${typedData[i].Department} could not be found`);
      } else {
        data[i].department = department;
      }

      const standard = await FormEnum.findOne({
        where: {
          name: 'documentStandard',
          $or: [
            { value: 'Standard ' + typedData[i].Standard },
            { value: typedData[i].Standard }
          ]
        },
        logging: true
      });

      if (!standard) {
        errors.push(`Line ${i + 2}. Unable to find Standard ${typedData[i].Standard}`);
      } else {
        data[i].standard = standard;
      }

      try {
        const reviewMoment = moment(typedData[i]['Date of next review'], 'D-M-YYYY').tz(timeZone);
        reviewMoment.add(12, 'hours');
        data[i].nextReviewAt = reviewMoment.toDate();
      } catch (e) {
        errors.push(`Line ${i + 2}. Error determining reviewDate from ${typedData[i]['Date of next review']}`);
      }
    }

    if (errors.length) {
      throw new BadRequestError("Import file contains errors", errors);
    }

    // Process the records
    for (let i = 0; i < typedData.length; i++) {
      const info = data[i];
      let collection = await DocumentCollection.findOne({ where: { name: typedData[i].Collection } });

      if (!collection) {
        collection = await DocumentCollection.create({
          name: typedData[i].Collection
        });
      }

      const existingDocument = await Document.findOne({ where: { name: typedData[i].Name } });
      const type = data[i].docType || DocumentType.FORM;
      let standard: number | null = null;
      if (info.standard) {
        standard = info.standard.id;
      }

      let departmentId: number | null = null;
      if (info.department) {
        departmentId = info.department.id;
      }

      let nextReviewAt: Date | null = null;
      if (info.nextReviewAt) {
        nextReviewAt = info.nextReviewAt;
      }

      if (!standard) {
        logger.error(signature + `Line ${i + 2} Missing data: Standard`);
        throw new BadRequestError(`There was an error processing the standard on Line ${i + 2}.`)
      }

      if (!departmentId) {
        logger.error(signature + `Line ${i + 2} Missing data: departmentId`);
        throw new BadRequestError(`There was an error processing the department on Line ${i + 2}.`)
      }

      if (!nextReviewAt) {
        logger.error(signature + `Line ${i + 2} Missing data: nextReviewAt`);
        throw new BadRequestError(`There was an error processing the review date on Line ${i + 2}.`)
      }

      const docProperties: Partial<Document> = {
        standard,
        type,
        departmentId,
        name: typedData[i].Name,
        documentCollectionId: collection.id,
        nextReviewAt,
        documentCode: type.toString() + '-' + typedData[i]['Document ID'],
        documentIdentity: typedData[i]['Document ID']
      };

      if (existingDocument) {
        await existingDocument.update(docProperties);

        await DocumentDRMService.updateAllFormSummaries(existingDocument.id);
        result.updatedRecords++;
      } else {
        await Document.create({
          createdAt: new Date(),
          ...docProperties
        });
        result.newRecords++;
      }
    }

    return { message: `Upload Completed. Created ${result.newRecords} records and Updated ${result.updatedRecords}`, data: result };
  }
}
