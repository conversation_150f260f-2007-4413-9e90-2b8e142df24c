import * as t from 'io-ts';
import { constructType } from './extend';
import { DateType } from './date';
import { constructUserGroupType } from './groups';
import { constructRecordType } from './record';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { DateFromQueryParam, EmailValidationModel, SortOrderValues } from './common';
import { PaginationResponse } from './pagination';
import { ISODateRegEx } from '../constants';
import { DocumentOutputType } from './document';

// TODO: Figure out why when this const is in ./extend and exports, it still cannot be found by TS
class BoolFromStringType extends t.Type<Boolean, string, t.mixed> {
	readonly _tag: 'BoolFromString' = 'BoolFromString';
	constructor() {
		super(
			'BoolFromString',
			(u): u is Boolean => u instanceof Boolean,
			(u, c) => {
				const validation = t.string.validate(u, c);
				if (validation.isLeft()) {
					return validation as any;
				} else {
					const s = validation.value;

					return t.success(Boolean(s));
				}
			},
			a => a.toString()
		)
	}
}

const BoolFromString: BoolFromStringType = new BoolFromStringType();

export const FormUpdateParams = constructType({
	id: t.refinement(t.number, s => s > 0, 'Id is invalid'),
	stage: t.refinement(t.number, s => s >= 0, 'Stage is invalid')
}, {
	userGroupId: t.union([
		t.refinement(t.number, s => s > 0, 'User group is invalid'),
		t.null
	]),
	formLocationId: t.union([
		t.refinement(t.number, s => s > 0, 'Location is invalid'),
		t.null
	]),
	assignedUserId: t.union([
		t.refinement(t.number, s => s > 0, 'Assigned User is invalid'),
		t.null
	]),
	notifyOnComplete: t.union([
		t.refinement(t.string, s => s.length > 0, 'string with length greater than 0'),
		t.null,
		t.undefined
	]),
	dueAt: t.union([
		t.refinement(t.string, s => s.length > 0 && !!(s as string).match(ISODateRegEx), 'Due date is invalid'),
		t.null,
		t.undefined
	]),
	alertAt: t.union([
		t.refinement(t.string, s => s.length > 0 && !!(s as string).match(ISODateRegEx), 'Alert date is invalid'),
		t.null,
		t.undefined
	]),
	summary: t.union([
		t.refinement(t.string, s => s.length > 0, 'summary is invalid'),
		t.null,
		t.undefined
	]),
	isTemplate: t.union([
		t.boolean,
		t.null,
		t.undefined
	])
});

export interface IFormUpdateParams extends t.TypeOf<typeof FormUpdateParams> { }

export const ScheduleParams = constructType({
	startAt: t.union([
		t.refinement(t.string, s => s.length > 0 && !!(s as string).match(ISODateRegEx), 'Start date is invalid'),
		t.null,
		t.undefined
	]),
	expression: t.refinement(t.string, s => s.length > 0, 'expression is invalid'),
}, {
	nextExecution: t.union([
		t.refinement(t.string, s => s.length > 0 && !!(s as string).match(ISODateRegEx), 'Start date is invalid'),
		t.null,
		t.undefined
	])
});

export interface IScheduleParams extends t.TypeOf<typeof ScheduleParams> { }

export const FormScheduleParams = constructType({
	id: t.refinement(t.number, s => s > 0, 'Id is invalid'),
	schedule: t.union([
		ScheduleParams,
		t.null,
		t.undefined
	])
})

export interface IFormScheduleParams extends t.TypeOf<typeof FormScheduleParams> { }

export const FormParams = constructType(
	{
		categoryId: t.refinement(t.number, s => s > 0, 'Form category is invalid'),
		formLocationId: t.union([
			t.refinement(t.number, s => s > 0, 'Form location is invalid'),
			t.null,
			t.undefined
		]),
		userGroupId: t.union([
			t.refinement(t.number, s => s > 0, 'User group is invalid'),
			t.null,
			t.undefined
		]),
		// UTC Date String
		dueAt: t.union([
			t.refinement(t.string, s => s.length > 0 && !!(s as string).match(ISODateRegEx), 'Due date is invalid'),
			t.null,
			t.undefined
		]),
		alertAt: t.union([
			t.refinement(t.string, s => s.length > 0 && !!(s as string).match(ISODateRegEx), 'Alert date is invalid'),
			t.null,
			t.undefined
		]),
		notifyOnComplete: t.union([
			t.refinement(t.string, s => s.length > 0, 'NotifyOnComplete is invalid'),
			t.null,
			t.undefined
		]),
		stage: t.refinement(t.number, s => s >= 0, 'Stage is invalid'),
		parentFormId: t.union([
			t.refinement(t.number, s => s > 0, 'Parent form is invalid'),
			t.null,
			t.undefined,
		])
	},
	{
		assignedUserId: t.union([t.number, t.null]),
		summary: t.union([
			t.refinement(t.string, s => s.length > 0, 'summary is invalid'),
			t.null,
			t.undefined
		]),
		isTemplate: t.union([
			t.boolean,
			t.null,
			t.undefined
		]),
		templateFormId: t.union([
			t.refinement(t.number, s => s > 0, 'Template form is invalid'),
			t.null,
			t.undefined,
		]),
	}
);

export interface IFormParams extends t.TypeOf<typeof FormParams> { }

export const FormRecordPropertyParam = t.intersection([
	t.type({
		name: t.refinement(t.string, s => s.length > 0, 'A String with length greater than 0')
	}),
	t.partial({
		stringData: t.union([
			t.refinement(t.string, s => s.length > 0, 'A String with length greater than 0'),
			t.null,
			t.undefined
		]),
		intData: t.union([t.number, t.null, t.undefined]),
		jsonData: t.union([
			t.refinement(t.string, s => s.length > 0, 'JSON String with length greater than 0'),
			t.null,
			t.undefined
		]),
		enumId: t.union([
			t.refinement(t.number, s => s > 0, 'EnumId is invalid'),
			t.null,
			t.undefined
		]),
		dateData: t.union([
			t.refinement(t.string, s => s.length > 0 && !!(s as string).match(ISODateRegEx), 'Alert date is invalid'),
			t.null,
			t.undefined
		]),
	})
]);

export interface IFormRecordPropertyParam extends t.TypeOf<typeof FormRecordPropertyParam> { }

const DocumentsUpdateType = constructType({ id: t.number }, { isTicked: t.boolean });

export const FormRecordParams = t.exact(t.type({
	formId: t.refinement(t.number, s => s > 0, 'FormId is invalid'),
	properties: t.array(FormRecordPropertyParam),
	stage: t.refinement(t.number, s => s >= 0, 'Stage is invalid'),
	isComplete: t.boolean,
	documents: t.array(DocumentsUpdateType),
}));

export interface IFormRecordParams extends t.TypeOf<typeof FormRecordParams> { }

export interface ChildFormTreeNode {
	id: number;
	summary: string;
	archivedAt: string | null;
	childForms: Array<ChildFormTreeNode> | undefined;
	categoryId: number;
}

const ChildFormTreeNode: t.RecursiveType<t.Type<ChildFormTreeNode>> = t.recursion('ChildFormTreeNode', () =>
	t.type({
		id: t.number,
		summary: t.string,
		categoryId: t.number,
		archivedAt: t.union([t.string, t.null]),
		childForms: t.union([
			t.undefined,
			t.array(ChildFormTreeNode),
		]),
	})
);

function constructFormType<T>(dateType: t.Type<T>) {
	return t.type({
		id: t.number,
		alertAt: t.union([dateType, t.null]),
		approverUserId: t.union([t.number, t.null]),
		archivedAt: t.union([dateType, t.null]),
		category: t.type({
			id: t.number,
			name: t.string,
		}),
		notifyOnComplete: t.union([t.string, t.null]),
		categoryId: t.number,
		createdAt: dateType,
		createdById: t.union([t.number, t.null]),
		dueAt: t.union([dateType, t.null]),
		formLocationId: t.union([t.number, t.null]),
		stage: t.number,
		userGroup: t.union([constructUserGroupType<T>(dateType), t.null]),
		userGroupId: t.union([t.number, t.null]),
		formLocation: t.union([
			t.type({
				id: t.number,
				name: t.string,
			}),
			t.null
		]),
		records: t.array(constructRecordType<T>(dateType)),
		assignedUserId: t.union([t.number, t.null]),
		assignedUser: t.union([
			t.null,
			t.interface({
				id: t.number,
				username: t.string,
				firstName: t.string,
				lastName: t.string,
			})
		]),
		parentFormId: t.union([
			t.null,
			t.number,
		]),
		templateFormId: t.union([
			t.null,
			t.number
		]),
		ancestorFormId: t.union([
			t.undefined,
			t.null,
			t.number,
		]),
		issueNumber: t.union([
			t.null,
			t.string,
		]),
		isOverdue: t.union([
			t.undefined,
			t.boolean
		]),
		isAlertOverdue: t.union([
			t.undefined,
			t.boolean
		]),
		isDeleted: t.union([
			t.boolean,
			t.undefined,
			t.null,
		]),
		deleteReason: t.union([
			t.string,
			t.undefined,
			t.null,
		]),
		summary: t.string,
		childForms: t.union([
			t.array(ChildFormTreeNode),
			t.undefined,
			t.null,
		]),
		schedule: t.union([
			ScheduleParams,
			t.undefined,
			t.null
		]),
		document: t.union([
			t.null,
			t.undefined,
			DocumentOutputType
		]),
		isTemplate: t.union([
			t.boolean,
			t.undefined,
			t.null,
		]),
		executionSuccess: t.union([t.boolean, t.null, t.undefined]),
		executionMessage: t.union([t.string, t.null, t.undefined])
	});
}

export const FormOutputValidationModel = constructFormType<Date>(DateType);

export const FormOutputModel = constructFormType<string>(t.string);
export const FormRecordOutputModel = constructRecordType<string>(t.string);

export interface IFormOutputValidationModel extends t.TypeOf<typeof FormOutputValidationModel> { }

export interface IFormOutputModel extends t.TypeOf<typeof FormOutputModel> { }
export interface IFormRecordOutputModel extends t.TypeOf<typeof FormRecordOutputModel> { }

// See comment in /contracts/common.ts for explanation for this ignore
const FormSortFields: ['dueAt', 'createdAt', 'archivedAt', 'location', 'assignedTo', 'summary', 'category', 'documentIdentity'] = ['dueAt', 'createdAt', 'archivedAt', 'location', 'assignedTo', 'summary', 'category', 'documentIdentity'];
export const FormSortedLiterals = FormSortFields.map(f => t.literal<typeof f>(f));

// @ts-ignore
export const FormsSortedField = t.union<typeof FormSortedLiterals>(FormSortedLiterals);

export type IFormsSortedField = t.TypeOf<typeof FormsSortedField>;

export const FormsQueryParams = constructType({
	skip: t.union([IntFromString, t.number]),
	limit: t.union([IntFromString, t.number]),
	sortBy: FormsSortedField,
	order: SortOrderValues,
}, {
	
	assignedUserId: t.union([IntFromString, t.number]),
	showAll: t.union([t.literal('all'), t.literal('complete'), t.literal('deleted'), t.literal('active')]),
	dueFilter: t.union([t.literal('overdue'), t.literal('alert'), t.literal('remaining')]),
	createdAtStart: DateFromQueryParam,
	createdAtEnd: DateFromQueryParam,
	dueAtStart: DateFromQueryParam,
	dueAtEnd: DateFromQueryParam,
	search: t.string,
	category: t.union([IntFromString, t.number]),

	reportFormType: t.union([IntFromString, t.number]),
	originatorName: t.string,
	isTemplate: t.union([BoolFromString, t.boolean]),

	// Now accepts a comma delimited list
	locationId: t.string,
	groupId: t.string,
	documentId: t.string,
	riskId: t.string,
	excludeCategories: t.string,
	riskTypeId:t.string
});

export interface IFormsQueryParams extends t.TypeOf<typeof FormsQueryParams> { }

export interface FormsPaginationResponse extends PaginationResponse<IFormOutputModel> {
	overdueTasks: number;
	alertTasks: number;
	remainingTasks: number;
}

export const FormDeleteParams = t.interface({
	id: t.number,
	deleteReason: t.refinement(t.string, v => !!v && v.trim().length > 0, 'not empty delete reason'),
});

export interface IFormDeleteParams extends t.TypeOf<typeof FormDeleteParams> { }

export const FormRevertParams = t.interface({
	id: t.number
});

export interface IFormRevertParams extends t.TypeOf<typeof FormRevertParams> { }

export const SendFormReportParams = t.interface({
	id: t.number,
	emails: t.array(EmailValidationModel),
});

export interface ISendFormReportParams extends t.TypeOf<typeof SendFormReportParams> { }

export const DuplicateFormParams = t.interface({
	id: t.number,
	propertiesToClear: t.array(t.string)
});

export interface IDuplicateFormParams extends t.TypeOf<typeof DuplicateFormParams> { }

