import * as nodemailer from 'nodemailer';
import { Transporter } from 'nodemailer';
import * as SMTPTransport from 'nodemailer-smtp-transport';
import * as SendmailTransport from 'nodemailer-sendmail-transport';
import * as hbs from 'nodemailer-express-handlebars';
import * as path from 'path';
import { BadRequestError } from '../../model/HttpErrors';
import { Attachment } from 'nodemailer/lib/mailer';
import has = Reflect.has;
import { systemSettingsService } from './settings.service';

interface IEmailParams {
  from: string;
  to: string | string[];
  subject: string;
  template: string;
  cc?: string;
  context: {
    [key: string]: string;
  };
  attachments?: Attachment[];
}

export enum EmailAttachment {
  logo = "logo",
}

const imagesAll:Attachment[] = [
  {
    filename: "logo.png",
    path: path.resolve(__dirname, "../email-templates/images/logo.png"),
    cid: EmailAttachment.logo
  },
];


interface IEmailParams {
  from: string;
  to: string | string[];
  subject: string;
  template: string;
  context: {
    [key: string]: string,
  };
  attachments?: Attachment[];
}


class MailServiceClass {

  private transporter: Transporter;

  constructor() {

    const transport: string = process.env.EMAIL_TRANSPORT || 'smtp';

    if ( transport === 'sendmail' ) {
      this.transporter = nodemailer.createTransport(SendmailTransport({
        sendMail: true,
        newline: 'unix',
        path: '/sbin/sendmail'
      }));
    }

    if ( transport === 'smtp' ) {
      const mailSettings: { [key: string]: any } = {
        auth: {
          user: process.env.EMAIL_FROM,
          pass: process.env.EMAIL_PASS,
        },
        tls: {rejectUnauthorized: false}
      };

      if (has(process.env, 'EMAIL_HOST')) {
        mailSettings.host = process.env.EMAIL_HOST;

        if (has(process.env, 'EMAIL_PORT')) {
          mailSettings.port = process.env.EMAIL_PORT;
        }

      } else if (has(process.env, 'EMAIL_SERVICE')) {
        mailSettings.service = process.env.EMAIL_SERVICE;
      }

      this.transporter = nodemailer.createTransport(SMTPTransport(mailSettings));
    }

    if ( this.transporter ) {

      /*
       * This is absoloutly crazy.
       *
       * Due to some issues with the current version of nodemailer-handlebars, the view engine
       * can no longer accept 'handlebars' and instead needs a config object with the partialsDir specified.
       *
       * The viewEngine config does not use consistent camelcase while nodemailer-handlebars does, resulting in this
       * chaos.
       *
       * See:
       * https://github.com/ericf/express-handlebars/pull/247
       */
      const handlebarsOptions = {
        viewEngine: {
          extname: '.hbs',
          partialsDir: path.resolve(__dirname, '../email-templates'),
          layoutsDir: path.resolve(__dirname, '../email-templates'),
          defaultLayout: 'template.hbs',
        },
        viewPath: path.resolve(__dirname, '../email-templates'),
        extName: '.hbs',
      };

      // @ts-ignore
      const handlebarsOptions2 = {
        viewEngine: 'handlebars',
        viewPath: path.resolve(__dirname, '../email-templates'),
        extName: '.hbs',
      };

      this.transporter.use('compile', hbs(handlebarsOptions));

      this.transporter.verify(function (error, success) {
        if (error) {
          console.log(error);
        } else {
          console.log('Server is ready to take our messages');
        }
      });
    } else {
      console.log('Unknown or unhandled mail transport ' + transport + '. Emails will not be sent');
    }
  }

  public canSendEmail(): Promise<boolean> {
    return systemSettingsService.getSettingsValue('isEmailsEnabled');
  }

  public async sendEmail(emailParams: IEmailParams, sendMailOptions?: {
    force?: boolean
  }): Promise<void> {

    console.log(`Sending Email. To[${emailParams.to}] From[${emailParams.from}] Subject[${emailParams.subject}]`);

    const isEmailsEnabled = await this.canSendEmail();

    if (!isEmailsEnabled && ( !sendMailOptions || !sendMailOptions.force ) ) {
      console.log('Emails service is disabled, the email has not been sent.');
      return;
    }

    try {
      if ( this.transporter ) {
        if( !emailParams.attachments ) emailParams.attachments = []
        emailParams.attachments.push(        
          ...MailService.addAttachment(EmailAttachment.logo)
        );
        await this.transporter.sendMail(emailParams);
        console.log(`Sending Email Complete. To[${emailParams.to}] From[${emailParams.from}] Subject[${emailParams.subject}]`);
      } else {
        console.log('Email could not be sent due to null transporter.');
      }
    } catch (err) {
      console.error('Exception while sending email:', err);
      throw new BadRequestError('Cannot send an email.');
    }
  }

  readonly addAttachment = (...attachments: EmailAttachment[]): Attachment[] => {
    return attachments
      .map<Attachment|undefined>(attachment => {
        return imagesAll.find(image => image.cid === attachment);
      })
      .filter(a => !!a) as Attachment[];
  };
}

export const MailService = new MailServiceClass();
