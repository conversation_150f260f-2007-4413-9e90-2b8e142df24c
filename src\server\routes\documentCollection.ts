import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { DocumentCollectionController } from '../controller/documentCollection.controller';
import { adminMiddleware } from '../middleware/adminMiddleware';
import {
  validateBodyParams,
  validateQueryParams,
  validateRouteParams
} from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';
import { CreateDocumentCollectionInputModel, DocumentCollectionQueryParams, UpdateDocumentCollectionInputModel } from '../../common/contracts/documentCollection';
import { preFillDefaultQueryParams } from '../middleware/preFillMiddleware';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/documentCollection', adminMiddleware);

/**
 DocumentCollection routes
 */
router.get('/documentCollection',
  preFillDefaultQueryParams({skip: '0', limit: '10', sortBy: 'name', order: 'asc'}),
  validateQueryParams(DocumentCollectionQueryParams),
  responseWrap(DocumentCollectionController.getDocumentCollections));

router.post('/documentCollection/import/:id', validateRouteParams(GetByIdParams), responseWrap(DocumentCollectionController.importDocuments));
router.get('/documentCollection/:id', validateRouteParams(GetByIdParams), responseWrap(DocumentCollectionController.getDocumentCollectionById));
router.post('/documentCollection', adminMiddleware, validateBodyParams(CreateDocumentCollectionInputModel), responseWrap(DocumentCollectionController.createDocumentCollection));
router.put('/documentCollection', adminMiddleware, validateBodyParams(UpdateDocumentCollectionInputModel), responseWrap(DocumentCollectionController.updateDocumentCollection));
router.delete('/documentCollection/:id', adminMiddleware, validateRouteParams(GetByIdParams), responseWrap(DocumentCollectionController.archiveDocumentCollection));

export default router;
