'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    let tableDefinition = await queryInterface.describeTable('form_locations');

    if (!tableDefinition['isArchived']) {
      await queryInterface.addColumn('form_locations', 'isArchived', {
        type:Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('form_locations', 'isArchived');
  }
};
