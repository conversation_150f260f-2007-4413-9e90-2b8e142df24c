import * as t from 'io-ts';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { PaginationResponse } from './pagination';
import { SortOrderValues } from './common';
import { constructType } from './extend';

export function constructTagType<T>(dateType: t.Type<T>) {
  return t.type({
    id: t.number,
    name: t.string,
    isMeta: t.boolean,
    createdAt: dateType,
    updatedAt: dateType,
  });
}

export const TagOutputModel = constructTagType(t.string);

export interface ITagOutputModel extends t.TypeOf<typeof TagOutputModel> {}

export type TagPaginationResponse = PaginationResponse<ITagOutputModel>;

export const UpdateTagInputModel = t.exact(t.type({
  id: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  name: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Name should not be empty or it\'s too long '),
  isMeta: t.boolean
}));

export interface IUpdateTagInputModel extends t.TypeOf<typeof UpdateTagInputModel> {}

export const CreateTagInputModel = t.exact(t.type({
  name: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Name should not be empty or it\'s too long '),
  isMeta: t.boolean
}));

export interface ICreateTagInputModel extends t.TypeOf<typeof CreateTagInputModel> {}

// See comment in /contracts/common.ts for explanation for this ignore
// @ts-ignore
export const TagSortedField = t.union(['createdAt', 'updatedAt', 'name', 'isMeta'].map(f => t.literal(f)));

export type ITagSortedField = t.TypeOf<typeof TagSortedField>;

export const TagQueryParams = constructType({
  skip: IntFromString,
  limit: IntFromString,
  sortBy: TagSortedField,
  order: SortOrderValues
}, {
  // countUsers: t.refinement(IntFromString, v => v === 1 || v === 0, 'valid countUsers parameter' ),
});

export interface ITagQueryParams extends t.TypeOf<typeof TagQueryParams> {}
