.overlay {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999999;
}

.dialog {
  z-index: 1000000;
  position: fixed;
  right: 0;
  left: 0;
  top: 20px;
  margin-bottom: 20px;
  margin-right: auto;
  margin-left: auto;
  min-height: 200px;
  width: 100%;
  max-width: 1090px;
  background-color: #fff;
  max-height: calc(100% - 40px);
  padding-bottom: 12px;
  box-shadow: 0 7px 8px -4px rgba(0, 0, 0, 0.2), 0 13px 19px 2px rgba(0, 0, 0, 0.14), 0 5px 24px 4px rgba(0, 0, 0, 0.12);
  overflow-y: scroll;
}

.dialog.overflow-hidden {
  overflow-y: hidden;
  padding-right: 15px;
}

.dialog.small {
  min-height: 50px;
}

@media (min-width: 768px) {
  .dialog {
    top: 40px;
    margin-bottom: 40px;
    max-height: calc(100% - 80px);
  }
}

.dialog__close-btn {
  border: 0;
  background: none;
  color: #2d2d2d;
  position: absolute;
  top: 8px;
  right: 8px;
  font-size: 1.2em;
  cursor: pointer;
}
