import * as path from 'path';
import * as Express from 'express';
import * as BodyParser from 'body-parser';
import { jwtCheck } from '../middleware/jwtCheck';

import groupRoutes from './groups';
import authRoutes from './auth';
import currentUserRoutes from './currentUser';
import usersRoutes from './users';
import locationRoutes from './location';
import enumRoutes from './enum';
import formRoutes from './form';
import recordRoutes from './record';
import categoryRoutes from './category';
import documentRoutes from './document';
import reportRoutes from './report';
import settingsRoutes from './settings';
import resourcePackRoutes from './resource-pack';
import documentCollectionRoutes from './documentCollection';
import documentDRMRoutes from './documentDRM';
import tagRoutes from './tag';
import documentTagroutes from './documentTag';
import documentMetaRoutes from './documentMeta';
import documentInstanceRoutes from './documentInstance';
import riskRoutes from './risk';
export class Index {
  public static importRoutes(express: Express.Express): void {
    const router: Express.Router = Express.Router();

    router.use(/^\/api\/(?!auth|forgot|reset).*$/, jwtCheck);

    const apiBase = '/api';

    router.use(apiBase, authRoutes);
    router.use(apiBase, currentUserRoutes);
    router.use(apiBase, groupRoutes);
    router.use(apiBase, usersRoutes);
    router.use(apiBase, locationRoutes);
    router.use(apiBase, enumRoutes);
    router.use(apiBase, formRoutes);
    router.use(apiBase, recordRoutes);
    router.use(apiBase, categoryRoutes);
    router.use(apiBase, documentRoutes);
    router.use(apiBase, reportRoutes);
    router.use(apiBase, resourcePackRoutes);
    router.use(apiBase, settingsRoutes);
    router.use(apiBase, documentCollectionRoutes);
    router.use(apiBase, documentDRMRoutes);
    router.use(apiBase, tagRoutes);
    router.use(apiBase, documentTagroutes);
    router.use(apiBase, documentMetaRoutes);
    router.use(apiBase, documentInstanceRoutes);
    router.use(apiBase, riskRoutes);

    router.all(`${apiBase}/*`, (req: any, res: any) => res.status(404).send() );

    // all other routes are handled by Angular
    router.get('/*', function(req, res) {
      res.sendFile(path.join(__dirname, '/../../../build/app/index.html'));
    });

    express.use(Express.static(__dirname + '/../../../build/app'));

    express.use(BodyParser.json());
    express.use(BodyParser.urlencoded({ extended: true }));
    express.use('/', router);
  }

}

