{"name": "bethany-aged-care", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "pm2 start app.js", "build": "npm run tscBuild", "lint": "tslint \"./**/*.ts\" --config ./src/tslint.json --project ./src/tsconfig.json", "lint.fix": "tslint \"./**/*.ts\" --config ./src/tslint.json --project ./src/tsconfig.json --fix", "reinstall": "rm -rf ./node_modules && npm install", "test": "mocha -r ts-node/register **/test.ts --exit", "rmBuild": "rm -rf ./build", "ngBuild": "ng build", "ngBuildStage": "node --max_old_space_size=2048 ./node_modules/@angular/cli/bin/ng build --configuration=stage", "ngBuildProd": "node --max_old_space_size=2048 ./node_modules/@angular/cli/bin/ng build --configuration=production", "ngWatch": "ng build --watch", "ngServe": "ng serve", "copyTemplates": "copyfiles -u 2 \"./src/server/email-templates/**/*.*\" \"./build/server\"", "tscBuild": "tsc --project ./src/tsconfig.json && npm run copyTemplates", "tscWatch": "tsc-watch --project ./src --onSuccess \"node ./build/index.js\" --noClear", "dev": "npm run rmBuild && npm run copyTemplates && ( npm run ngWatch & npm run tscWatch )", "dbSeed": "sequelize db:seed:all", "dbMigrate": "sequelize db:migrate", "fix-memory-limit": "cross-env LIMIT=2048 increase-memory-limit"}, "repository": {"type": "git", "url": "git+ssh://*****************/SteeleParker/bethany-aged-care.git"}, "keywords": [], "author": "", "license": "ISC", "homepage": "https://bitbucket.org/SteeleParker/bethany-aged-care#readme", "devDependencies": {"@angular-builders/custom-webpack": "^7.4.3", "@angular-devkit/build-angular": "^0.12.1", "@angular/cli": "7.2.1", "@angular/compiler-cli": "^7.2.16", "@angular/core": "^7.2.16", "@types/chai": "^4.2.11", "@types/express-serve-static-core": "4.17.6", "@types/lodash": "4.14.170", "@types/mocha": "^5.2.7", "@types/node": "^10.17.21", "@types/prop-types": "15.7.3", "@types/sequelize": "^4.28.14", "@types/validator": "10.11.3", "chai": "^4.1.2", "codelyzer": "^4.5.0", "eslint": "^5.6.0", "mocha": "^5.2.0", "node-sass": "^6.0.1", "prettier": "1.14.3", "sass": "^1.89.2", "sequelize-cli": "^5.5.0", "ts-node": "^7.0.1", "tsc-watch": "^2.4.0", "tslint": "^5.19.0", "typescript": "3.1.1 - 3.2.2"}, "dependencies": {"@angular/animations": "^7.2.16", "@angular/common": "~7.0.0", "@angular/compiler": "^7.2.16", "@angular/forms": "^7.2.16", "@angular/platform-browser": "~7.0.0", "@angular/platform-browser-dynamic": "~7.0.0", "@angular/router": "~7.0.0", "@ng-bootstrap/ng-bootstrap": "^4.2.1", "@react-pdf/renderer": "1.2.0 - 1.5.6", "@sentry/browser": "^4.6.5", "@types/express": "4.17.6", "@types/formidable": "~1.0.32", "@types/jasmine": "3.5.10 - 3.9.1", "@types/mime": "2.0.3", "@types/moment-timezone": "^0.5.13", "@types/nodemailer": "^4.6.5", "@types/react": "~16.9.35", "angular-sortablejs": "^2.6.0", "argon2": "^0.43.1", "argon2-ffi": "^1.2.0", "aws-sdk": "^2.676.0", "axios": "^0.18.1", "body-parser": "^1.18.3", "bootstrap-xxl": "^4.1.1", "classlist.js": "^1.1.20150312", "copyfiles": "^2.2.0", "cron-converter": "^1.0.0", "cron-editor": "^2.1.8", "crypto": "^1.0.1", "csstype": "3.1.0", "csvtojson": "^2.0.10", "dotenv": "^6.1.0", "express": "^4.17.1", "express-jwt-middleware": "^1.0.3", "file-saver": "^2.0.0", "font-awesome": "^4.7.0", "formidable": "^1.2.2", "has": "^1.0.3", "io-ts": "^1.10.4", "io-ts-reporters": "0.0.21", "io-ts-types": "^0.4.8", "jquery": "^3.5.1", "json-rules-engine": "^2.3.0", "jwt-simple": "^0.5.5", "libreoffice-convert": "1.1.1 - 1.4.1", "local-devices": "^1.1.1", "lodash": "^4.17.21", "logform": "2.1.2 - 2.4.2", "lscache": "^1.3.0", "moment": "^2.25.3", "moment-timezone": "^0.5.28", "monocle-ts": "^1.7.2", "mysql2": "^1.7.0", "network": "^0.4.1", "network-list": "^1.1.5", "newtype-ts": "^0.2.4", "ng2-select2": "^1.0.0-beta.16", "ngx-autosize": "1.7.5", "ngx-mask": "^7.9.10", "node-cron": "^2.0.3", "nodemailer": "^5.0.0", "nodemailer-express-handlebars": "^3.3.0", "nodemailer-sendmail-transport": "^1.0.2", "nodemailer-smtp-transport": "^2.7.4", "pg": "^8.16.3", "react": "^16.13.1", "ref-napi": "^2.1.2", "reflect-metadata": "^0.1.13", "rxjs": "^6.5.5", "select2": "^4.0.13", "sequelize": "^4.44.4", "sequelize-typescript": "0.6.11", "sortablejs": "1.8.0-rc1 - 1.9.0", "sqlstring": "^2.3.2", "winston": "3.2.1 - 3.10.0", "winston-transport": "4.5.0", "xml-js": "^1.6.8", "zone.js": "^0.8.26"}}