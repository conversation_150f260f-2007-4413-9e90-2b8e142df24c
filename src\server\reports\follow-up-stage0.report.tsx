import * as React from 'react';
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import * as moment from 'moment';
import 'moment-timezone';
import { IRecordWithDateOutputModel } from "../../common/contracts/record";
import { IRecordPropertyType } from "../../common/contracts/recordProperty";
import Footer from "./components/footer";
import FormLocation from "../db/models/formLocation.model";
import { IEnumsOutputModel } from "../../common/contracts/enums";
import { timeZone } from '../../common/constants';

const styles = StyleSheet.create({
  wrapper: {
  },
  header: {
    flexDirection: 'row'
  },
  headerBar: {
    flex: 1,
    height: 16,
    borderBottomWidth: 3,
    borderBottomColor: '#616266',
    borderBottomStyle: 'solid',
  },
  formTitle: {
    flex: 4,
    fontSize: 32,
    marginBottom: 10,
    color: '#ed7d31',
  },
  firstRow: {
    flexDirection: 'row',
    marginTop: 10,
    marginBottom: 14,
  },
  firstRowItem: {
    flex: 1,
    flexDirection: 'col',
    fontSize: 11,
  },
  contactedOriginatorBlock: {
    flexDirection: 'row',
    fontSize: 11,
    marginTop: 10,
  },
  labelText: {
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
    marginRight: 5,
  },
  centralBlock: {
    flexDirection: 'row',
  },
  leftCentralBlock: {
    flexDirection: 'col',
    flex: 2,
    paddingRight: 10,
  },
  summaryText: {
    fontSize: 11,
    color: '#000000',
    lineHeight: '180%',
    paddingLeft: 12,
    marginBottom: 8,
  },
});

const FirstRow = ({children}) => (
  <View style={styles.firstRow}>
    {children}
  </View>
);

const FirstRowItem = ({name, value}) => {
  return (<View style={styles.firstRowItem}>
    <Text style={styles.labelText}>{name}:</Text>
    <Text>{value}</Text>
  </View>);
};

export class FollowUpFormStage0 extends React.Component<{
  recordData: IRecordWithDateOutputModel,
  followUpGroupMap: {[n: number]: string},
  location: FormLocation | null,
  reportFormTypes: IEnumsOutputModel[],
  dueAt: string,
}> {
  render() {

    const propertyMap: {[k: string]: IRecordPropertyType}  = {};

    this.props.recordData.properties.forEach(prop => {
      propertyMap[prop.property.name] = prop;
    });

    const updatedByUser = this.props.recordData.createdByUser ? this.props.recordData.createdByUser.firstName + ' ' + this.props.recordData.createdByUser.lastName : '';
    const updatedAt = moment(this.props.recordData.createdAt).tz(timeZone).format('DD-MM-YYYY');
    const reportType = propertyMap['type'] ? this.props.reportFormTypes.find(item => item.id === propertyMap['type'].enumId) : null;

    return (
      <View style={styles.wrapper}>
        <View style={styles.header} wrap={false}>
          <View style={styles.formTitle}>
            <Text>FOLLOW UP RECORD (FUR)</Text>
          </View>
          <View style={styles.headerBar} />
        </View>

        <View style={styles.contactedOriginatorBlock}>
          <Text style={styles.labelText}>1. This "FUR" follows-up:</Text>
          <Text>{propertyMap['followUpType'] && (propertyMap['followUpType'].stringData && propertyMap['followUpType'].stringData.toUpperCase()) ||  '-'}</Text>
        </View>

        <FirstRow>
          <FirstRowItem name="Location" value={this.props.location ? this.props.location.name : ''} />
          <FirstRowItem name="Form Title" value={reportType ? reportType.value : ''} />
          <FirstRowItem name="Due Date" value={this.props.dueAt} />
        </FirstRow>

        <View style={styles.centralBlock}>
          <View style={styles.leftCentralBlock}>
            <Text style={styles.labelText}>FUR Detail:</Text>
            <Text style={styles.summaryText}>
              {propertyMap['summary'] ? propertyMap['summary'].stringData : ''}
            </Text>
          </View>
        </View>
        <Footer updatedByUser={updatedByUser} updatedAt={updatedAt} />
      </View>
    );
  }
}
