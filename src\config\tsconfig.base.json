{"compilerOptions": {"module": "commonjs", "target": "es6", "moduleResolution": "node", "rootDir": "../", "outDir": "../../build", "sourceMap": true, "declaration": false, "allowJs": true, "jsx": "react", "noImplicitAny": false, "noUnusedLocals": true, "noImplicitThis": true, "strictNullChecks": true, "noImplicitReturns": true, "preserveConstEnums": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "suppressImplicitAnyIndexErrors": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["../../node_modules/@types"], "lib": ["es2018", "dom"], "types": ["node", "jasmine", "react"]}, "exclude": ["../node_modules", "build", "webpack"]}