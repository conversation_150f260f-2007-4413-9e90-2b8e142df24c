'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if group already exists
      const existingGroups = await queryInterface.sequelize.query(
        "SELECT * FROM groups WHERE \"groupName\" = 'Risk Management'",
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingGroups.length === 0) {
        return await queryInterface.bulkInsert('groups', [{
          groupName: 'Risk Management',
        }], {});
      } else {
        console.log('Group "Risk Management" already exists, skipping...');
        return Promise.resolve();
      }
    } catch (err) {
      console.error("----------");
      console.error("ERROR DURING ADDiNG INSERTING GROUPS: Risk Management");
      console.error("----------");
      console.error(err);
      // Don't throw error if group already exists
      if (err.message && err.message.includes('already exists')) {
        console.log('Group already exists, continuing...');
        return Promise.resolve();
      }
      return Promise.resolve(); // Don't fail the migration
    }
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('groups', [{
      groupName: 'Risk Management',
    }], {});
  }
};
