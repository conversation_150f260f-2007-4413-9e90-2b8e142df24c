import * as t from 'io-ts';
import { EmailValidationModel, PhoneValidationModel } from './common';

export const UpdateUserProfileInputModel = t.exact(t.interface({
  username: EmailValidationModel,
  firstName: t.string,
  lastName: t.string,
  phone: PhoneValidationModel,
  oldPassword: t.union([t.string, t.null]),
  password: t.union([t.string, t.null]),
}));

export interface IUpdateUserProfileInputModel extends t.TypeOf<typeof UpdateUserProfileInputModel> {}
