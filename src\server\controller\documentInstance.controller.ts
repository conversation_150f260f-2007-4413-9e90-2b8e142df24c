import * as express from 'express';
import * as path from 'path';
import * as fs from 'fs';
import * as libre from 'libreoffice-convert';
import { BadRequestError, NotFoundError, ServerError } from '../../model/HttpErrors';
import FormDocument from '../db/models/formDocument.model';
import documentInstance from '../db/models/documentInstance.model';
import Document from '../db/models/document.model';
import { Sequelize } from 'sequelize-typescript';
import DocumentTag from '../db/models/documentTag.model'
import moment = require('moment');
import DocumentMeta from '../db/models/documentMeta.model';
import DocumentCollection from '../db/models/documentCollection.model';
import * as SqlString from 'sqlstring';
import { MailService } from '../service/mail.service';
import logger from '../utils/Logger';

const className: string = "DocumentInstanceController";

export class DocumentInstanceController {

	public static async upload(req: any) {
		const signature = className + ".upload: ";

		let version = 1;
		const { documentFileId, documentId, formId, createdAt, aprovedBy, convertToPdf, documentFileName } = req.body as any,
			aprovedAt = new Date;

		const document = await FormDocument.findByPk(documentFileId);
		const documentDetails = await Document.findByPk(documentId);

		if (!document || !documentDetails) {
			throw new BadRequestError('Document is not found on server.');
		}

		let dirName = process.env.UPLOADS || '../uploads',
			fileName = document.path.split('/')[(document.path.split('/')).length - 1];

		dirName = path.isAbsolute(dirName) ? dirName : path.resolve(process.env.PWD as string, dirName);


		const pathToFile = path.join(dirName, fileName),
			destinationDir = path.join(dirName, "drm-approved"),
			destinationPath = path.join(dirName, "drm-approved", fileName);

		if (!fs.existsSync(destinationDir)) {
			fs.mkdirSync(destinationDir);
		}

		if (!!convertToPdf) {
			let mimeType = 'application/pdf';

			const previousVersion = await documentInstance.max('version', {
				where: {
					documentId: documentId,
				}
			});

			if (!!previousVersion && previousVersion > 0) {
				version = previousVersion + 1;
			}

			const extend = '.pdf'
			const file = fs.readFileSync(pathToFile);
			logger.silly(signature + "Converting Document");
			libre.convert(file, extend, undefined, async (err, done) => {
				logger.silly(signature + "Converting Document " + (!!err ? "Failed" : "Succeeded"));
				if (err) {
					logger.error(`Could not convert ${documentDetails.documentCode}: ${err}`);
					const mailTo = process.env.ERROR_EMAIL as string;
					try {
						await MailService.sendEmail({
							to: mailTo,
							from: process.env.EMAIL_FROM || '<EMAIL>',
							subject: 'Document Review Module - Bethany Christian Care',
							template: 'drm-conversion-failure',
							context: {
								documentId: documentDetails.documentCode,
							},
						});
					} catch (err) {
						logger.error('Exception while sending email', err);
					}

					// Copying original file in place of converted file if conversion fails.
					mimeType = document.mimeType;
					fs.copyFileSync(pathToFile, destinationPath);
				} else {
					logger.silly(signature + `Writing new file to Path[${destinationPath}]`);
					fs.writeFileSync(destinationPath, done);
				}
			});

			let newFileName = documentFileName.substring(0, documentFileName.lastIndexOf('.')) + extend;
			const instance = await documentInstance.create({ documentId, formId, aprovedBy, aprovedAt, createdAt, path: destinationPath, mimeType, version, originalPath: document.path, originalMimeType: document.mimeType, fileName: newFileName, originalFileName: documentFileName }, { fields: ['documentId', 'formId', 'aprovedBy', 'aprovedAt', 'createdAt', 'path', 'mimeType', 'version', 'originalPath', 'originalMimeType', 'fileName', 'originalFileName'] });

			return instance;
		} else {
			const mimeType = document.mimeType;

			const previousVersion = await documentInstance.max('version', {
				where: {
					documentId: documentId,
				}
			});

			if (!!previousVersion) {
				version = previousVersion + 1;
			}

			fs.copyFileSync(pathToFile, destinationPath);
			const file = await documentInstance.create({ documentId, formId, aprovedBy, aprovedAt, createdAt, path: destinationPath, mimeType, version, originalPath: document.path, originalMimeType: document.mimeType, fileName: documentFileName, originalFileName: documentFileName }, { fields: ['documentId', 'formId', 'aprovedBy', 'aprovedAt', 'createdAt', 'path', 'mimeType', 'version', 'originalPath', 'originalMimeType', 'fileName', 'originalFileName'] });
			return file;
		}
	}

	public static async download(req: JwtRequest, res: express.Response) {
		const { id: documentId } = req.params as any;

		if (isNaN(documentId)) {
			throw new ServerError();
		}

		const file = await documentInstance.findByPrimary(documentId);

		if (!file) {
			res.status(404).end();
			return;
		}

		res.sendFile(file.path, {
			headers: {
				'Content-Type': file.mimeType,
			}
		});
	}

	public static async getDraft(req: JwtRequest) {
		const { id: documentId } = req.params as any;

		if (isNaN(documentId)) {
			throw new ServerError();
		}
		const latestVersion = await documentInstance.max('version', {
			where: {
				documentId: documentId,
			}
		});

		if (!latestVersion) {
			throw new NotFoundError('No approved version of document not found.');
		}

		const file = await documentInstance.findOne({
			where: {
				documentId: documentId,
				version: latestVersion
			}
		});

		if (!file) {
			throw new ServerError();
		}

		return file;
	}

	public static async downloadDraft(req: JwtRequest, res: express.Response) {
		const { id: id } = req.params as any;

		if (isNaN(id)) {
			throw new ServerError();
		}

		const file = await documentInstance.findByPrimary(id);

		if (!file || !file.originalPath || !file.originalPath.length) {
			res.status(404).end();
			return;
		}

		res.sendFile(file.originalPath, {
			headers: {
				'Content-Type': file.originalMimeType,
			}
		});
	}

	public static async getAllDocumnets(req: JwtRequest) {
		const queryParams = { ...req.query };
		const isAdmin = req.tokenData.user.role === 'administrator';

		let documentModelWhereClause = {},
			documentTagModelWhereClause = {},
			documentInstanceWhereClause = {},
			documentMetaTagModelWhereClause = {};
		let includesTagArray: any[] = []

		const inputString = SqlString.escape(queryParams.search);
		const likeString = '\'%' + inputString.replace(/(^')|('$)/g, '') + '%\'';

		if (queryParams && queryParams.search) {
			documentModelWhereClause = {
				id: {
					[Sequelize.Op.in]: Sequelize.literal(`(
                  SELECT documents.id FROM documents INNER JOIN document_collections 
                  ON documents.documentCollectionId = document_collections.id 
                  WHERE (document_collections.name LIKE ${likeString}
                        OR document.name LIKE ${likeString}
                        OR document.documentCode LIKE ${likeString})
                  )`)
				}
			}
		}

		documentModelWhereClause['isArchived'] = {
			[Sequelize.Op.eq]: 0
		}
		documentModelWhereClause['isArchived'] = {
			[Sequelize.Op.eq]: 0
		}

		if (isAdmin && queryParams && queryParams.includeArchived === 'true') {
			delete documentModelWhereClause['isArchived'];
			delete documentModelWhereClause['isArchived'];
		}

		if (queryParams && queryParams.standard) {
			documentModelWhereClause['standard'] = {
				[Sequelize.Op.eq]: Number(queryParams.standard)
			}
		}

		if (queryParams && queryParams.tags) {
			let tagIds = (queryParams.tags as string).split(',').map(id => Number(id));
			documentTagModelWhereClause['tagId'] = {
				[Sequelize.Op.in]: tagIds
			}
			includesTagArray.push({
				model: DocumentTag,
				where: documentTagModelWhereClause,
			});
		}

		if (queryParams && queryParams.groupId) {
			let groupIds = (queryParams.groupId as string).split(',').map(id => Number(id));
			documentModelWhereClause['departmentId'] = {
				[Sequelize.Op.in]: groupIds
			}
		}

		if (queryParams && queryParams.lastUpdatedAtStart && queryParams.lastUpdatedAtEnd) {
			let start = moment(queryParams.lastUpdatedAtStart.toString(), 'DD/MM/YYYY').toDate(),
				end = moment(queryParams.lastUpdatedAtEnd.toString(), 'DD/MM/YYYY').toDate(),
				dateArray = [start, end];

			documentInstanceWhereClause['createdAt'] = {
				[Sequelize.Op.between]: dateArray
			}
		}

		if (queryParams && queryParams.metaTags) {
			let metaTagIds = (queryParams.metaTags as string).split(',').map(id => Number(id));
			documentMetaTagModelWhereClause['tagId'] = {
				[Sequelize.Op.in]: metaTagIds
			}
			includesTagArray.push({
				model: DocumentMeta,
				where: documentMetaTagModelWhereClause,
			});
		}

		const files = await documentInstance.findAndCountAll({
			include: [{
				model: Document,
				where: documentModelWhereClause,
				include: [{
					model: DocumentCollection,
				}, ...includesTagArray]
			}],
			where: documentInstanceWhereClause,
			limit: Number(queryParams.limit),
			offset: Number(queryParams.skip),
			distinct: true,
		});

		let filteredFiles = files.rows,
			count = files.count;
		if (queryParams && queryParams.metaTagValue && !!files.rows.length) {
			let metaTagValues = (queryParams.metaTagValue as string).split(',');
			let metaTagIds = (queryParams.metaTags as string).split(',').map(id => Number(id));
			let filesToFilter: any = []
			for (let i = 0; i < files.rows.length; i++) {
				//@ts-ignore
				for (let j = 0; j < (files.rows[i].document.documentMeta).length; j++) {
					for (let valueIndex = 0; valueIndex < metaTagValues.length; valueIndex++) {
						if (metaTagValues[valueIndex] === files.rows[i].document.documentMeta[j].value) {
							if (metaTagIds[valueIndex] === files.rows[i].document.documentMeta[j].tagId) {
								continue;
							} else {
								if (filesToFilter.indexOf(files.rows[i].id) < 0)
									filesToFilter.push(files.rows[i].id);
							}
						} else {
							if (filesToFilter.indexOf(files.rows[i].id) < 0)
								filesToFilter.push(files.rows[i].id);
						}
					}
				}
			}
			filteredFiles = filteredFiles.filter(file => {
				if (filesToFilter.indexOf(file.id) < 0)
					return true;
				return false;
			});
			count = count - filesToFilter.length;
		}
		return filteredFiles;
	}
}