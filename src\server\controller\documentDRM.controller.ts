import { BadRequestError, NotFoundError, ServerError } from '../../model/HttpErrors';
import {
	ICreateDocumentInputModel,
	IDocumentQueryParams,
	IUpdateDocumentInputModel
} from '../../common/contracts/documentDRM';
import { IGetByIdParams } from '../../common/contracts/common';
import { Sequelize } from 'sequelize-typescript';
import Document from '../db/models/document.model';
import documentInstance from '../db/models/documentInstance.model';
import moment = require('moment');
import logger from '../utils/Logger';
import { DocumentDRMService } from '../service/documentDRM.service';
import { timeZone } from '../../common/constants';
import Form from '../db/models/form.model';
import { RecordService } from '../service/record.service';
import { FormService } from '../service/form.service';
import FormRecord from '../db/models/formRecord.model';

export class DocumentController {

	public static async getDocuments(req: JwtRequest & Query<IDocumentQueryParams>) {

		const queryParams = req.query as IDocumentQueryParams;

		const attributes: string[] = ['id', 'name', 'createdAt', 'updatedAt', 'type', 'documentCollectionId', 'nextReviewAt', 'documentCode', 'documentIdentity'];
		const filter = {};
		filter['isArchived'] = false;
		if (queryParams.documentCollectionId) {
			filter['documentCollectionId'] = Number(queryParams.documentCollectionId)
		}
		if (queryParams.includeArchived && queryParams.includeArchived === '1') {
			delete filter['isArchived']
		}
		const { rows, count } = await Document.findAndCountAll<Document>({
			limit: queryParams.limit,
			offset: queryParams.skip,
			where: {
				...filter
			},
			attributes,
			order: [[Sequelize.literal(queryParams.sortBy) as any as string, queryParams.order.toUpperCase()]],
		});

		return {
			items: rows,
			totalCount: count,
		};
	}

	public static async getDocumentById(req: JwtRequest) {

		const { id: documentId } = req.params as unknown as IGetByIdParams;

		const document = await Document.findOne({
			where: {
				id: documentId
			}
		});

		if (!document) {
			throw new NotFoundError('Document is not found.');
		}

		return document;
	}

	public static async createDocument(req: any, res: any) {

		const { name, documentCollectionId, type, nextReviewAt, departmentId, standard, documentIdentity } = req.body as ICreateDocumentInputModel;
		const createdAt = moment().tz(timeZone);

		try {
			let documentCode = type.toString() + '-' + documentIdentity;
			const document = await Document.create({ name, documentCollectionId, type, nextReviewAt, departmentId, standard, createdAt, documentCode, documentIdentity }, { fields: ['name', 'documentCollectionId', 'type', 'nextReviewAt', 'departmentId', 'standard', 'createdAt', 'documentCode', 'documentIdentity'] });

			return await Document.findByPrimary<Document>(document.id);
		} catch (e) {
			if (e && e.name === 'SequelizeUniqueConstraintError') {
				throw new BadRequestError('Document with this name already exists.');
			} else {
				logger.error('createDocument exception', e);
				throw new ServerError();
			}
		}

	}

	public static async updateDocument(req: any, res: any) {
		const { id, name, documentCollectionId, type, nextReviewAt, departmentId, standard, documentIdentity } = req.body as IUpdateDocumentInputModel;

		const document: Document | null = await Document.findByPrimary<Document>(id);

		if (!document || document.isArchived) {
			throw new NotFoundError('Document is not found');
		}

		try {
			let documentCode = type.toString() + '-' + documentIdentity;
			const [recordsUpdated] = await Document.update({ name, documentCollectionId, type, nextReviewAt, departmentId, standard, documentCode, documentIdentity }, { where: { id: id } });
			await DocumentDRMService.updateAllFormSummaries(id);

			return { status: 'ok', recordsUpdated };
		} catch (e) {
			if (e && e.name === 'SequelizeUniqueConstraintError') {
				throw new BadRequestError('Document with this name already exists.');
			} else {
				logger.error('updateDocument exception ', e);
				throw new ServerError();
			}
		}

	}

	public static async archiveDocument(req: JwtRequest) {

		const { id: documentId } = req.params as unknown as IGetByIdParams,
			nameSuffix = ' Archived ' + moment().format(),
			document: Document | null = await Document.findByPrimary(documentId);

		if (!document || document.isArchived) {
			throw new NotFoundError('Document is not found.');
		}

		const [recordsArchived] = await Document.update({
			isArchived: true,
			name: document.name + nameSuffix
		}, {
			where: { id: documentId }
		});

		// Returning archived document details to navigate to parent collection after archival
		const archivedDocument = await Document.findByPrimary(documentId);

		/**
		 * Archive the associated forms
		 */
		const formsAttached = await DocumentDRMService.getFormsAttached(documentId),
			completedForms = formsAttached.filter(form => !!form.archivedAt).map(form => form.id),
			deleteReason = "Document was archived.";

		// Archives completed forms related to document collection
		for (let formIndex = 0; formIndex < completedForms.length; formIndex++) {
			const form = await Form.findByPrimary(completedForms[formIndex]);

			if (!form || form.isDeleted) {
				throw new NotFoundError(`archiveDocumentCollection: Form ${completedForms[formIndex]} is not found`);
			}

			await Form.update({
				isDeleted: true,
				deleteReason,
				archivedAt: new Date(),
			}, {
				where: { id: completedForms[formIndex] }
			});
		}

		return {
			archived: recordsArchived,
			document: archivedDocument
		};
	}

	public static async getDocumentInstanceByDocumentId(req: JwtRequest) {
		const { id } = req.params as any;

		if (isNaN(id)) {
			throw new NotFoundError('Document not found.');
		}

		const latestVersion = await documentInstance.max('version', {
			where: {
				documentId: id,
			}
		});

		if (!latestVersion) {
			throw new NotFoundError('No approved version of document not found.');
		}

		const file = await documentInstance.findOne({
			where: {
				documentId: id,
				version: latestVersion,
			}
		});

		if (!file) {
			throw new NotFoundError('Document not found.');
		}

		return file;
	}

	/**
	 * @description Determines an appropriate file to be attached as the 'draft' document to any given step of the DRM review process
	 * It will search for documents exactly in the following order
	 * 1. Draft Attached to Previous Step of the same Form/Flow - From the most recent submission
	 * 2. Latest Approved Draft
	 * 3. Latest Approved Document
	 * 4. Fail
	 */
	public static async getDefaultDocument(req: JwtRequest) {
		const { documentId, formId } = req.params as {
			documentId: string,
			formId: string | undefined
		};
		const docIdInt = parseInt(documentId);

		if( formId ) {
			const formIdInt = parseInt(formId);
			const form = await FormService.getFormById(formIdInt);

			if (!form) {
				throw new NotFoundError('Form not found.');
			}

			const formRecords = await RecordService.getAllRecordsForForm(formIdInt);
			const sortedRecords = formRecords.rows.filter(record => !!record.isComplete).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
			const recordWithDocument = sortedRecords.find(record => !!record.documents.length);

			if (recordWithDocument) {
				return recordWithDocument.documents[0];
			}
		}

		// Find the latest draft outside of the current form
		const docForms = await DocumentDRMService.getFormsAttached(docIdInt);
		if (docForms.length > 0) {
			const allRecords: FormRecord[] = [];
			for (let i = 0; i < docForms.length; i++) {
				if( !!docForms[i].archivedAt && !docForms[i].isDeleted ) {
					const docFormRecords = await RecordService.getAllRecordsForForm(docForms[i].id);
					if (docFormRecords.count > 0) {
						allRecords.push(...docFormRecords.rows);
					}
				}
			}

			const sortedDocRecords = allRecords.filter(record => !!record.isComplete).sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
			const docRecordWithDocument = sortedDocRecords.find(record => !!record.documents.length);

			if (docRecordWithDocument) {
				return docRecordWithDocument.documents[0];
			}
		}
		
		return null;
	}
}