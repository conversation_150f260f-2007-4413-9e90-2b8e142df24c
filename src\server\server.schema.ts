import {Sequelize} from 'sequelize-typescript';

const env = process.env.NODE_ENV || 'development';
const config = require(__dirname + '/db/config/config')[env];

export class DbInstance {

  seqInstance: Sequelize;

  constructor() {
    this.init();
  }

  public init(): void {
    this.seqInstance  = new Sequelize({
      database: config.database,
      username: config.username,
      password: config.password,
      host: config.host,
      dialect: config.dialect,
      modelPaths: [__dirname + '/db/models/**/*.model.*' ],
      logging: false
    });
  }

  public connect() {
    return this.seqInstance.authenticate()
      .then(() => {
        console.log(`DB Connection established`);
      });
  }
}

const dbInstance = new DbInstance();

export namespace Db {

  export function init() {

    console.log('model path', __dirname + '/db/models/**/*.model.ts');

    return dbInstance.connect()
      .then(() => {
        return dbInstance.seqInstance.sync();
      })
      .then(() => {
        console.log('DB synced');
      })
      .catch((err: Error) => {
        console.log(`Connection to database failed due to an error:\n` +
          `${err.name}: ${err.message}\n${err.stack}`);
      });
  }
}
