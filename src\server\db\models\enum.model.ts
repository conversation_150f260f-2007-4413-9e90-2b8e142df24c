/*
  When a form has a property that must be from a list of options
  then the enum_id should be stored against that formProperty
  rather than just storing text. This is far more efficient
 */

import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo
} from 'sequelize-typescript';
import FormLocation from './formLocation.model';
import ResourcePack from './resource-pack.model';

@Table({
  tableName: 'enums'
})
class FormEnum extends Model<FormEnum> {

  /* The name of the enum, such as "ReportType" */
  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  name: string;

  /* The value for the entry, such as "Comment" */
  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  value: string;

  /* The order for the entry for display, nullable, lowest first */
  @Column({
    type: DataType.INTEGER,
    allowNull: true
  })
  order: number;

  @ForeignKey(() => FormLocation)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  formLocationId: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isArchived: boolean;

  @ForeignKey(() => ResourcePack)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  resourcePackId: number;

  @BelongsTo(() => ResourcePack)
  resourcePack: ResourcePack;
}

export default FormEnum;

