import * as t from 'io-ts';
import {constructType} from './extend';

export const DocumentType = constructType({
  id: t.number,
  fileName: t.string,
  mimeType: t.string,
  size: t.number,
}, {
  isTicked: t.boolean
});

export interface IDocumentType extends t.TypeOf<typeof DocumentType> {}

export function constructDocumentType<T>(dateType: t.Type<T>) {
  return t.exact(t.type({
    id: t.number,
    fileName: t.string,
    size: t.number,
    mimeType: t.string,
    createdAt: dateType,
    updatedAt: dateType,
  }));
}

export const TFormDocument = constructDocumentType(t.string);

export const DocumentOutputType = constructType({
  id: t.number,
  createdAt: t.string,
  departmentId: t.union([t.number, t.null]),
  documentCode: t.string,
  documentIdentity: t.string,
  documentCollectionId: t.number,
  isArchived: t.number,
  name: t.string,
  nextReviewAt: t.union([t.string, t.null]),
  standard: t.union([t.number, t.null]),
  type: t.string,
  updatedAt: t.string,
}, {
  deletedAt: t.string,
});