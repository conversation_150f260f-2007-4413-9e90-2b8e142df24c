import { BadRequestError, NotFoundError, ServerError } from '../../model/HttpErrors';
import { IRiskQueryParams, IUpdateRiskInputModel } from '../../common/contracts/risk';
import Risks from '../db/models/risk.model';
import { IFormParams, IFormRecordParams } from '../../common/contracts/form';
import { FormService } from '../service/form.service';
import RiskToForm from '../db/models/risk_to_form.model'
import { Sequelize } from 'sequelize-typescript';
import { IGetByIdParams } from '../../common/contracts/common';
import Form from '../db/models/form.model';
import FormRecord from '../db/models/formRecord.model';
import { RecordService } from '../service/record.service';
import * as moment from 'moment';
import "moment-timezone";
import { timeZone } from '../../common/constants';
import FormRecordProperty from '../db/models/formRecordProperty.model';
import Property from '../db/models/property.model';
import FormLocation from '../db/models/formLocation.model';
import * as fs from 'fs';
import * as csv from "csvtojson";
import logger from '../utils/Logger';
import FormDocument from '../db/models/formDocument.model';
import UserGroup from '../db/models/group.model';
import FormCategory from '../db/models/category.model';
import UserToLocation from '../db/models/user_to_location.model';
const className = "RiskController"
export class RiskController {

  public static async getRisks(req: JwtRequest) {
    const queryParams = req.query as IRiskQueryParams;

    const whereClause = {
      id: {
        [Sequelize.Op.ne]: null
      }
    }

    if (queryParams.searchFilter && queryParams.searchFilter.length > 0) {
      whereClause['summary'] = {
        [Sequelize.Op.like]: '%' + queryParams.searchFilter + '%'
      }
    }

    if (queryParams.riskLevelFilter && queryParams.riskLevelFilter.length > 0) {
      whereClause["riskLevel"] = queryParams.riskLevelFilter
    }

    if (queryParams.riskStatusFilter && queryParams.riskStatusFilter.length > 0) {
      whereClause["riskStatus"] = queryParams.riskStatusFilter;
    }

    if (queryParams.riskTypeId && queryParams.riskTypeId.length > 0) {
      Object.assign(whereClause['id'], {
        [Sequelize.Op.in]: Sequelize.literal(`(
					SELECT riskId
					FROM forms_record_property
					INNER JOIN forms_record
					ON forms_record_property.formRecordId = forms_record.id
					INNER JOIN risk_to_form
					ON forms_record.formId = risk_to_form.formId
					WHERE
					(
						propertyID = (
						SELECT id
						FROM properties
						WHERE \`name\` = 'riskType'
						)
						AND intData = ${queryParams.riskTypeId}     
					)
				)`)
      });
    }


    /**
		 * Location Limitations
		 * All users will be constrained by locations assigned to them, including Admins
		 * All users with no locations assigned will have access to all locations
		 * All users may constrain locations which they see data from
		 */

    const locations = await UserToLocation.findAll<UserToLocation>({
			where: { userId: req.tokenData.user.id },
			attributes: ['locationId']
		});

    if (queryParams.locationFilter && queryParams.locationFilter.length > 0) {
      const locationIds: number[] = queryParams.locationFilter
        .split(",")
        .map(id => Number(id))
        .filter(locationId => !locations.length || !!locations.find(userLocation => userLocation.locationId === locationId))
      ;
      
      whereClause["locationId"] = { [Sequelize.Op.in]: locationIds };
    } else if(locations.length) {
      whereClause["locationId"] = { [Sequelize.Op.in]: locations.map(l => l.locationId) };
    }

    if (queryParams.groupFilter && queryParams.groupFilter.length > 0) {
      const groupIds: number[] = queryParams.groupFilter
        .split(",")
        .map(id => Number(id));
      ;
      
      whereClause["groupId"] = { [Sequelize.Op.in]: groupIds };
    }

    // console.log("----- todo validate that the groups are required ----");

    const { rows, count } = await Risks.findAndCountAll<Risks>({
      limit: Number(queryParams.limit),
      offset: Number(queryParams.skip),
      order: [[Sequelize.literal(queryParams.sortBy) as any as string, queryParams.order.toUpperCase()]],
      where: whereClause,
      // TODO: Validate that groups is required here
      include: [{ model: FormLocation, as: "form_locations" }, { model: UserGroup, as: "groups" }],
      attributes: {
        include: [
          [Sequelize.literal(`(
						SELECT distinct intData
						FROM forms_record_property
						INNER JOIN forms_record
						ON forms_record_property.formRecordId = forms_record.id
						INNER JOIN risk_to_form
						ON forms_record.formId = risk_to_form.formId
						WHERE
						propertyID = (
							SELECT id
							FROM properties
							WHERE \`name\` = 'riskType'
						)
						AND risk_to_form.riskId = Risks.id
					)`), "riskTypeId"]
        ]
      }
    });

    return {
      items: rows,
      totalCount: count,
    };
  }

  public static async createRisk(req: JwtRequest) {
    const {
      categoryId,
      formLocationId,
      userGroupId,
      dueAt,
      alertAt,
      notifyOnComplete,
      stage,
      parentFormId,
      assignedUserId,
      summary
    } = req.body as IFormParams;
    try {
      const result = await Risks.create({ summary }, { fields: ['summary'] });
      const form = await FormService.createForm({
        categoryId,
        formLocationId,
        userGroupId,
        dueAt,
        alertAt,
        notifyOnComplete,
        stage,
        parentFormId,
        assignedUserId
      }, req.tokenData.user.id);
      const formId = form.id;
      const riskId = result.id;
      await RiskToForm.create({ riskId, formId }, { fields: ['riskId', 'formId'] });
      return form
    } catch (e) {
      console.error('Exception in RiskController.createRisk', e);
      throw new ServerError();
    }
  }

  public static async deleteRisk(req: JwtRequest) {

    const { id } = req.params as { id: string };

    const risk: Risks | null = await Risks.findByPrimary<Risks>(id);

    if (!risk) {
      throw new NotFoundError('Risk is not found');
    }

    try {
      await risk.destroy();
      return {
        status: 'ok',
        recordsUpdated: 1
      };
    } catch (e) {
      console.error('Exception in RiskController.deleteRisk', e);
      throw new ServerError();
    }

  }

  public static async updateRisk(req: JwtRequest) {

    const { id, summary } = req.body as IUpdateRiskInputModel;

    const risk: Risks | null = await Risks.findByPrimary<Risks>(id);

    if (!risk) {
      throw new NotFoundError('Risk is not found');
    }

    try {
      const [recordsUpdated] = await Risks.update({ summary }, { where: { id: id } });
      return {
        status: 'ok',
        recordsUpdated
      };
    } catch (e) {
      console.error('Exception in RiskController.updateRisk', e);
      throw new ServerError();
    }

  }

  public static async getRiskById(req: JwtRequest): Promise<Risks> {

    const { id } = req.params as unknown as IGetByIdParams;
    try {
      const risk = await Risks.findByPrimary(id);

      if (!risk) {
        throw new NotFoundError('Risk is not found');
      }

      return risk;
    } catch (e) {
      console.error('Exception in RiskController.getRiskById', e);
      throw new ServerError();
    }
  }

  public static async generateNewAssessment(req: JwtRequest) {
    const risk = await RiskController.getRiskById(req);

    return RiskController.generateNewAssessmentByRisk(risk);
  }

  public static async generateNewAssessmentByRisk(risk: Risks): Promise<Form> {
    const riskForms = await RiskToForm.unscoped().findAll({ where: { riskId: risk.id } });

    if (!riskForms.length) {
      console.error("Error generating New Assessment. All risks must have at least one associated form.");
      throw new ServerError("Error Generating Assessment");
    }

    const forms = await Form.unscoped().findAll({
      where: { id: riskForms.map(riskForm => riskForm.formId), archivedAt: { [Sequelize.Op.ne]: null } },
      order: [['id', 'ASC']]
    });

    if (!forms.length) {
      console.error("Error generating New Assessment. All risks must have at least one valid form.");
      throw new ServerError("Error Generating Assessment");
    }

    const stage0Submission = await FormRecord.unscoped().findOne({
      where: {
        isComplete: true,
        stage: 0,
        formId: forms[0].id
      },
      include: [{
        model: FormRecordProperty,
        include: [Property]
      }],
      order: [['sequence', 'DESC']]
    });

    if (!stage0Submission) {
      console.error("Error generating New Assessment. Initial form must have a valid stage0 Submission");
      throw new ServerError("Error Generating Assessment");
    }

    const initialAssessingDepartment = RecordService.getFieldInRecord(stage0Submission, 'initialAssessorDepartmentId');

    if( !initialAssessingDepartment || !initialAssessingDepartment.intData ) {
      console.error("Error generating New Assessment. Initial form must have a valid initialAssessorDepartmentId in stage0 Submission");
      throw new ServerError("Error Generating Assessment");
    }

    const form = await FormService.createForm({
      categoryId: forms[0].categoryId,
      formLocationId: risk.locationId,
      userGroupId: initialAssessingDepartment.intData,
      notifyOnComplete: null,
      stage: 1,
      parentFormId: null,
      dueAt: moment().tz(timeZone).startOf('day').add(7, 'days').toDate().toString(),
      alertAt: moment().tz(timeZone).startOf('day').add(5, 'days').toDate().toString()
    }, null);

    const newRecordData: IFormRecordParams = {
      formId: form.id,
      stage: 0,
      isComplete: true,
      documents: [],
      properties: [
        { name: 'location', intData: risk.locationId },
        { name: 'isExistingRisk', intData: 1 },
        { name: 'existingRisk', intData: risk.id },
        { name: 'existingRiskLevel', stringData: risk.riskLevel }
      ]
    };

    const riskTypeField = stage0Submission.properties.find(property => property.property.name.toLowerCase() === 'risktype');
    if (riskTypeField) {
      newRecordData.properties.push({ name: 'riskType', intData: riskTypeField.intData });
    }

    const initialDepartmentField = stage0Submission.properties.find(property => property.property.name.toLowerCase() === 'initialassessordepartmentid');
    if (initialDepartmentField) {
      newRecordData.properties.push({ name: 'initialAssessorDepartmentId', intData: initialDepartmentField.intData });
    }

    const residentNameField = stage0Submission.properties.find(property => property.property.name.toLowerCase() === 'residentname');
    if (residentNameField) {
      newRecordData.properties.push({ name: 'residentName', stringData: residentNameField.stringData });
    }

    const summaryField = stage0Submission.properties.find(property => property.property.name.toLowerCase() === 'summary');
    newRecordData.properties.push({ name: 'summary', stringData: summaryField ? summaryField.stringData : risk.summary });

    await RecordService.createRecord(newRecordData, stage0Submission.createdById);

    await RiskToForm.create({
      riskId: risk.id,
      formId: form.id
    });

    return form;
  }

  public static async importRisk(req: JwtRequest) {
    const signature = className + ".importDocuments: ";
    const { importDocumentId } = req.params;
    const formDocument = await FormDocument.findById(importDocumentId);

    if (!formDocument || !formDocument.path || !fs.existsSync(formDocument.path)) {
      throw new NotFoundError("Invalid Import Document");
    }

    const file = fs.readFileSync(formDocument.path);
    let csvData: any;

    const expectedFields = [
      "RiskId", "Name", "Risk Type", "Location", "Resident Name", "Risk Level", "Risk Status", "Group Name", "Next Assessment", "Last Assessment", "Initial Assessment"
    ] as [
        "RiskId", "Name", "Risk Type", "Location", "Resident Name", "Risk Level", "Risk Status", "Group Name", "Next Assessment", "Last Assessment", "Initial Assessment"
      ];

    try {
      csvData = await csv().fromString(file.toString());
      //   console.log(csvData)
    } catch (e) {
      throw new BadRequestError("Error reading CSV File");
    }

    if (csvData.length === 0) {
      throw new BadRequestError("Empty Import File");
    }

    const firstRecord = csvData[0];
    const missingFields = expectedFields.filter(field => !Object.prototype.hasOwnProperty.call(firstRecord, field));

    if (missingFields.length) {
      logger.error(signature + `Import rejected due to missing fields[${missingFields}]`);
      throw new BadRequestError("Import file contained Missing Fields " + missingFields.join(", "));
    }

    const typedData = (csvData as { [K in typeof expectedFields[number]]: string }[]).filter(row => {
      const notNullRow = Object.keys(row).find(rowKey => !!row[rowKey]);
      return !!notNullRow;
    });

    const result = {
      newRecords: 0,
      updatedRecords: 0
    };

    // Validate that all the data can be imported, or build errors
    const errors: string[] = [];
    const data: {
      riskTypeIdStr?: string,
      location?: FormLocation,
      group?: UserGroup,
      createdAt?: Date,
      dateOfNextAssessment?: Date,
      dateOfLastAssessment?: Date,
    }[] = typedData.map(data => { return {} });


    const formCategory = await FormCategory.findOne({ where: { name: 'Risk Task' } });

    if (!formCategory) {
      errors.push(`Invalid Form Category "Risk Task". Seek developer assistance.`);
      throw new BadRequestError("Import file contains errors", errors);
    }

    const riskTypeMap = {
      '1': 'General Risk',
      '2': 'Resident Clinical Risk',
      '3': 'Resident Non-Clinical Risk'
    }

    for (let i = 0; i < typedData.length; i++) {
      let riskTypeIdStr = Object.keys(riskTypeMap).find(k => riskTypeMap[k] === typedData[i]['Risk Type']);
      let location = await FormLocation.findOne({ where: { name: typedData[i].Location } });
      let group = await UserGroup.findOne({ where: { groupName: typedData[i]['Group Name'] } });

      if (!location) {
        errors.push(`Line ${i + 2}. Location ${typedData[i].Location} could not be found`);
      } else {
        data[i].location = location;
      }

      if (!group) {
        errors.push(`Line ${i + 2}. Group ${typedData[i]['Group Name']} could not be found`);
      } else {
        data[i].group = group;
      }

      if (!riskTypeIdStr) {
        errors.push(`Line ${i + 2}. Risk Type ${typedData[i]['Risk Type']} could not be found`);
      } else {
        data[i].riskTypeIdStr = riskTypeIdStr;
      }

      const acceptableRiskLevel = ['Low', 'Moderate', 'High', 'Critical'];

      if (!acceptableRiskLevel.find(level => level === typedData[i]['Risk Level'])) {
        errors.push(`Line ${i + 2}. Invalid Risk Level [${typedData[i]['Risk Level']}]. Must be one of ${acceptableRiskLevel.join(", ")}`);
      }

      try {
        const initialMoment = moment(typedData[i]['Initial Assessment'], 'D-M-YYYY').tz(timeZone);
        initialMoment.add(12, 'hours');
        data[i].createdAt = initialMoment.toDate();
      } catch (e) {
        errors.push(`Line ${i + 2}. Error determining reviewDate from ${typedData[i]['Date of next review']}`);
      }

      try {
        const nextMoment = moment(typedData[i]['Next Assessment'], 'D-M-YYYY').tz(timeZone);
        nextMoment.add(12, 'hours');
        data[i].dateOfNextAssessment = nextMoment.toDate();
      } catch (e) {
        errors.push(`Line ${i + 2}. Error determining reviewDate from ${typedData[i]['Date of next review']}`);
      }

      try {
        const lastMoment = moment(typedData[i]['Last Assessment'], 'D-M-YYYY').tz(timeZone);
        lastMoment.add(12, 'hours');
        data[i].dateOfLastAssessment = lastMoment.toDate();
      } catch (e) {
        errors.push(`Line ${i + 2}. Error determining reviewDate from ${typedData[i]['Date of next review']}`);
      }
    }


    if (errors.length) {
      throw new BadRequestError("Import file contains errors", errors);
    }

    // Process the records
    for (let i = 0; i < typedData.length; i++) {
      const optionalInfo = data[i];
      const infoKeys = [
        'riskTypeIdStr',
        'location',
        'group',
        'createdAt',
        'dateOfNextAssessment',
        'dateOfLastAssessment'
      ];

      for (let n = 0; n < infoKeys.length; n++) {
        if (!optionalInfo[infoKeys[n]]) {
          logger.error(signature + `Line ${i + 2} Missing data: ${infoKeys[n]}`);
          throw new BadRequestError(`There was an error processing the risk on Line ${i + 2}.`)
        }
      }
      const info = optionalInfo as Required<typeof optionalInfo>;

      let existingRisk = await Risks.findOne({ where: { summary: typedData[i].Name, locationId: info.location.id } });

      const docProperties: Partial<Risks> = {
        summary: typedData[i].Name,
        riskLevel: typedData[i]['Risk Level'],
        residentName: typedData[i]['Resident Name'],
        riskStatus: typedData[i]['Risk Status'],
        dateOfNextAssessment: info.dateOfNextAssessment,
        dateOfLastAssessment: info.dateOfLastAssessment,
        locationId: info.location.id,
        groupId: info.group.id,
        createdAt: info.createdAt
      };

      if (existingRisk) {
        await existingRisk.update(docProperties);
        existingRisk.changed('createdAt', true);
        await existingRisk.update({
          createdAt: info.createdAt
        });
        result.updatedRecords++;
      } else {
        const risk = await Risks.create({
          ...docProperties
        });
        risk.changed('createdAt', true);
        await risk.update({
          createdAt: info.createdAt
        });

        const form = await FormService.createForm({
          categoryId: formCategory.id,
          formLocationId: info.location.id,
          userGroupId: info.group.id,
          notifyOnComplete: null,
          stage: 4, // TODO Update this if you update the total number of stages in a risk
          parentFormId: null,
          dueAt: moment().tz(timeZone).startOf('day').toDate().toString(),
          alertAt: moment().tz(timeZone).startOf('day').toDate().toString()
        }, req.tokenData.user.id);

        const newRecordData: IFormRecordParams = {
          formId: form.id,
          stage: 0,
          isComplete: true,
          documents: [],
          properties: [
            { name: 'location', intData: info.location.id },
            { name: 'isExistingRisk', intData: 0 },
            { name: 'initialAssessorDepartmentId', intData: info.group.id },
            { name: 'riskType', intData: parseInt(info.riskTypeIdStr) },
            { name: 'summary', stringData: typedData[i]['Name'] }
          ]
        };

        if (typedData[i]['Resident Name']) {
          newRecordData.properties.push({ name: 'residentName', stringData: typedData[i]['Resident Name'] });
        }

        await RecordService.createRecord(newRecordData, req.tokenData.user.id);

        await RiskToForm.create({
          riskId: risk.id,
          formId: form.id
        });

        await form.update({
          archivedAt: moment().tz(timeZone).startOf('day').toDate().toString()
        });

        result.newRecords++;
      }
    }
    return { message: `Upload Completed. Created ${result.newRecords} records and Updated ${result.updatedRecords}`, data: result };
  }
}