import * as express from 'express';

import { AuthController } from '../controller/auth.controller';
import { responseWrap } from '../utils/responseWrap';
import { validateBodyParams, validateRouteParams } from '../middleware/validationMidleware';
import { AuthParams, ForgotPassParams, ResetPassParams } from '../../common/contracts/auth';
import { adminMiddleware } from '../middleware/adminMiddleware';
import { GetByIdParams } from '../../common/contracts/common';

const router = express.Router();

router.post('/auth', validateBodyParams(AuthParams), responseWrap(AuthController.login));
router.post('/forgot', validateBodyParams(ForgotPassParams), responseWrap(AuthController.forgotPassword));
router.post('/reset', validateBodyParams(ResetPassParams), responseWrap(AuthController.resetPassword));
router.get('/switch/:id', adminMiddleware, validateRouteParams(GetByIdParams), responseWrap(AuthController.emulateUser))

export default router;
