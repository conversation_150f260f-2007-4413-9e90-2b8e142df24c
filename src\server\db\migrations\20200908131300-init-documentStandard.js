'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    let tables = await queryInterface.showAllTables();

    const existingRecord = await queryInterface.rawSelect('enums', {
      where: {
        name: 'documentStandard',
      }
    }, ['documentStandard']);

    if (!existingRecord) {
      await queryInterface.bulkInsert('enums', [{
        name: 'documentStandard',
        value: "Standard 1",
        order: 1
      },{
        name: 'documentStandard',
        value: "Standard 2",
        order: 2
      },{
        name: 'documentStandard',
        value: "Standard 3",
        order: 3
      },{
        name: 'documentStandard',
        value: "Standard 4",
        order: 4
      },{
        name: 'documentStandard',
        value: "Standard 5",
        order: 5
      },{
        name: 'documentStandard',
        value: "Standard 6",
        order: 6
      },{
        name: 'documentStandard',
        value: "Standard 7",
        order: 7
      },{
        name: 'documentStandard',
        value: "Standard 8",
        order: 8
      }], {});
    }

  },

  down: async (queryInterface, Sequelize) => {}

};
