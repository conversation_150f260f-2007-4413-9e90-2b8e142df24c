'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    const tableDefinition = await queryInterface.describeTable('forms_record_property');
    if (tableDefinition['dateData']) {
      return;
    }

    await queryInterface.addColumn('forms_record_property', 'dateData', {
      type:Sequelize.DATE,
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('forms_record_property', 'dateData');
  }
};
