import * as cron from 'node-cron';
import { FormService } from './service/form.service';
import logger from './utils/Logger';

const className = "ServerSchedule";

export class ServerSchedule {
	public static init() {
		const signature = className + ".init: ";
		// Every Second
		let handlingTemplates = false;

		// Do something every hour
		cron.schedule('0 0 * * * *', async () => {
			if (!handlingTemplates) {
				handlingTemplates = true;

				await FormService.processFormSchedules();

				handlingTemplates = false;
			}
		});

		// Do every day at 00:00
		cron.schedule('0 0 * * *', async () => {
			await FormService.processDocumentReviewSchedules();
		});
		
		cron.schedule('0 0 * * *', async () => {
			await FormService.processRiskSchedules();
		});

		logger.info(signature + "Initialised Server Schedule");
	}
}
