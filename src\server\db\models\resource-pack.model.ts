import { Model, Column, Table, <PERSON><PERSON>any } from "sequelize-typescript";
import ResourcePackDocument from "./resource-pack-documents.model";
import FormEnum from "./enum.model";

@Table({ 
    tableName: 'resource_packs',
    timestamps: true,
    paranoid: true
})
export default class ResourcePack extends Model<ResourcePack> {
    @Column
    name: string;
    @HasMany(() => ResourcePackDocument)
    documents: ResourcePackDocument[];
    
    @HasMany(() => FormEnum)
    enum: FormEnum;
}