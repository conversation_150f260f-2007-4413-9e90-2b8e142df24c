'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    /*
      Add altering commands here.
      Return a promise to correctly handle asynchronicity.

      Example:
      return queryInterface.createTable('users', { id: Sequelize.INTEGER });
    */
    try {
      await queryInterface.bulkInsert('form_categories', [{
        name: 'DRM-Task',
      }], {});
    } catch (err) {
      console.error("----------");
      console.error("ERROR DURING ADDING DRM-Task Category");
      console.error("----------");
      console.error(err);
    }
  },

  down: (queryInterface, Sequelize) => {
    /*
      Add reverting commands here.
      Return a promise to correctly handle asynchronicity.

      Example:
      return queryInterface.dropTable('users');
    */
  }
};
