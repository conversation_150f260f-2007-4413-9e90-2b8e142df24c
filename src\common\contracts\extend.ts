/*
  Extending the io-ts functionality
 */

import * as t from 'io-ts';

// Source: https://github.com/gcanti/io-ts/pull/131/commits/ac6ab9b1ac86664966b818ff356dd2a70a5ea527
export function constructType<R extends t.Props, O extends t.Props>(
  required: R,
  optional: O = {} as O,
  name?: string
): t.Type<
  { [K in keyof R]: t.TypeOf<R[K]> } & { [K in keyof O]?: t.TypeOf<O[K]> },
  { [K in keyof R]: t.OutputOf<R[K]> } & { [K in keyof O]?: t.OutputOf<O[K]> }
  > {
  const loose = t.intersection([t.interface(required), t.partial(optional)]);
  const props = Object.assign({}, required, optional);
  return new t.Type(
    name || `StrictInterfaceWithOptionals(${loose.name})`,
    (m): m is t.TypeOfProps<R> & t.TypeOfPartialProps<O> =>
      loose.is(m) && Object.getOwnPropertyNames(m).every(k => props.hasOwnProperty(k)),
    (m, c) =>
      loose.validate(m, c).chain(o => {
        const errors: t.Errors = Object.getOwnPropertyNames(o)
        .map(
          key =>
            !props.hasOwnProperty(key) ? t.getValidationError(o[key], t.appendContext(c, key, t.never)) : undefined
        )
        .filter((e): e is t.ValidationError => e !== undefined);
        return errors.length ? t.failures(errors) : t.success(o);
      }),
    loose.encode
  );
}