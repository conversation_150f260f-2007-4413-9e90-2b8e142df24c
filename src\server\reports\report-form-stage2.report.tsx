import * as React from 'react';
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import * as moment from 'moment';
import 'moment-timezone';
import { IRecordWithDateOutputModel } from "../../common/contracts/record";
import { IRecordPropertyType } from "../../common/contracts/recordProperty";
import Footer from "./components/footer";
import FollowUps from "./components/followUps";
import { timeZone } from '../../common/constants';

const styles = StyleSheet.create({
  wrapper: {
    marginTop: 18,
  },
  header: {
    backgroundColor: '#19a0b7',
    padding: 10,
    paddingTop: 12,
    marginTop: 10,
    marginBottom: 10,
    fontSize: 32,
    lineHeight: 0.8,
    flexDirection: 'col',
    justifyContent: 'center',
    color: '#FFFFFF',
  },
  contactedOriginatorBlock: {
    flexDirection: 'row',
    fontSize: 11,
    marginTop: 10,
  },
  labelText: {
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
    marginRight: 5,
  },
  inlineLabel: {
    color: '#555555',
    marginRight: 10,
    fontSize: 11,
  },
  centralBlock: {
    flexDirection: 'row',
  },
  leftCentralBlock: {
    flexDirection: 'col',
    flex: 2,
    paddingRight: 10,
  },
  summaryText: {
    fontSize: 11,
    color: '#000000',
    lineHeight: '180%',
    paddingLeft: 12,
  },
  inlineView: {
    flexDirection: 'row',
    flex: 1,
  },
  reassignedUser: {
    fontSize: 11,
    flex: 1,
  },
});

export class ReportFormStage2 extends React.Component<{
  recordData: IRecordWithDateOutputModel,
  reassignedUserMap: {[n: number]: string},
  followUpGroupMap: {[n: number]: string},
}> {
  render() {

    const propertyMap: {[k: string]: IRecordPropertyType}  = {};

    this.props.recordData.properties.forEach(prop => {
      propertyMap[prop.property.name] = prop;
    });

    const updatedByUser = this.props.recordData.createdByUser ? this.props.recordData.createdByUser.firstName + ' ' + this.props.recordData.createdByUser.lastName : '';
    const updatedAt = moment(this.props.recordData.createdAt).tz(timeZone).format('DD-MM-YYYY');

    const followUpsProperty = propertyMap['followUps'];

    return (
      <View style={styles.wrapper}>
        <View style={styles.header} wrap={false}>
          <Text>SENIOR MANAGEMENT REVIEW</Text>
        </View>

        <View style={styles.centralBlock}>
          <View style={styles.leftCentralBlock}>
            <Text style={styles.labelText}>Senior Management Comments:</Text>
            <Text style={styles.summaryText}>
              {propertyMap['summary'] ? propertyMap['summary'].stringData : ''}
            </Text>
            <FollowUps followUpsJson={followUpsProperty.jsonData} followUpGroupMap={this.props.followUpGroupMap}/>
          </View>
        </View>
        <Footer updatedByUser={updatedByUser} updatedAt={updatedAt} />
      </View>
    );
  }
}
