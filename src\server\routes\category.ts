import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { adminMiddleware } from '../middleware/adminMiddleware';
import {CategoryController} from '../controller/category.controller';
import { validateBodyParams } from '../middleware/validationMidleware';
import { CreateCategoryInputModel, UpdateCategoryInputModel } from '../../common/contracts/category';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/category', adminMiddleware);

/**
 Group routes
 */
router.get('/category', responseWrap(CategoryController.getCategories));
router.post('/category', adminMiddleware, validateBodyParams(CreateCategoryInputModel), responseWrap(CategoryController.createCategory));
router.put('/category', adminMiddleware, validateBodyParams(UpdateCategoryInputModel), responseWrap(CategoryController.updateCategory));

export default router;
