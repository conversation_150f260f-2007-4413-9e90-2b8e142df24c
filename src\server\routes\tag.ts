import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { TagController } from '../controller/tag.controller';
import { adminMiddleware } from '../middleware/adminMiddleware';
import {
  validateBodyParams,
  validateQueryParams,
  validateRouteParams
} from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';
import { CreateTagInputModel, TagQueryParams, UpdateTagInputModel } from '../../common/contracts/tag';
import { preFillDefaultQueryParams } from '../middleware/preFillMiddleware';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/tag', adminMiddleware);

/**
 Tag routes
 */
router.get('/tag',
  preFillDefaultQueryParams({skip: '0', limit: '10', sortBy: 'name', order: 'asc'}),
  validateQueryParams(TagQueryParams),
  responseWrap(TagController.getTags));

router.get('/tag/:id', validateRouteParams(GetByIdParams), responseWrap(TagController.getTagById));
router.post('/tag', adminMiddleware, validateBodyParams(CreateTagInputModel), responseWrap(TagController.createTag));
router.put('/tag', adminMiddleware, validateBodyParams(UpdateTagInputModel), responseWrap(TagController.updateTag));
router.delete('/tag/:id', adminMiddleware, validateRouteParams(GetByIdParams), responseWrap(TagController.archiveTag));

export default router;
