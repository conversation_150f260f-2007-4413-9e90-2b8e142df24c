'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if AUDIT value already exists in the enum
      const result = await queryInterface.sequelize.query(
        "SELECT unnest(enum_range(NULL::enum_documents_type)) as enum_value",
        { type: Sequelize.QueryTypes.SELECT }
      );

      const enumValues = result.map(row => row.enum_value);

      if (!enumValues.includes('AUDIT')) {
        // Add AUDIT to the enum type
        await queryInterface.sequelize.query(
          "ALTER TYPE enum_documents_type ADD VALUE 'AUDIT'"
        );
      }

      return Promise.resolve();
    } catch (error) {
      console.log('Error updating enum:', error.message);
      // If enum doesn't exist, create it with all values
      if (error.message.includes('does not exist')) {
        return queryInterface.changeColumn('documents', 'type', {
          type: Sequelize.ENUM('POLICY', 'PROCEDURE', 'FORM', 'AUDIT'),
          allowNull: false
        });
      }
      throw error;
    }
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('documents', 'type', {
      type: Sequelize.ENUM('POLICY', 'PROCEDURE', 'FORM'),
    });
  }
};
