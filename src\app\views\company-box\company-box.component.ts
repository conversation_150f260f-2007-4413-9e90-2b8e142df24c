import { Component, EventEmitter, Input, Output } from '@angular/core';
//import { BrowserModule } from '@angular/platform-browser';
import { animate, style, transition, trigger } from '@angular/animations';
//import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
//import { DialogModule } from '../dialog/dialog.component';

@Component({
  selector: '[company-box]',
  templateUrl: './company-box.component.html',
  styleUrls: ['./company-box.component.css'],
  animations: [
    trigger('cBox', [
      transition('void => *', [
        style({ transform: 'scale3d(0, 0, 0)' }),
        animate('400ms ease-in-out'),
      ]),
    ]),
  ],
})
export class CompanyBoxComponent {
  /* Determine if we should show the complete details in a large format or small tiles */
  @Input() verbose = false;
  @Input() variant: number;

  @Output() contactFormVisibleChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  showDialog: boolean = false;

  constructor() {
  }

  showContactForm(event: MouseEvent) {
    this.showDialog = true;

    this.contactFormVisibleChange.emit(this.showDialog);
  }

  echoContactFormVisibility(isVisible: boolean) {
    this.contactFormVisibleChange.emit(isVisible);
  }
}
