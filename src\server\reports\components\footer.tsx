import * as React from 'react';
import { StyleSheet, Text, View } from "@react-pdf/renderer";

const styles = StyleSheet.create({
  footer: {
    backgroundColor: '#DDDDDD',
    padding: 14,
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 10,
  },
  property: {
    fontSize: 11,
    flexDirection: 'row',
  },
  inlineLabel: {
    color: '#555555',
    marginRight: 10,
    fontSize: 11,
  },
  blueValue: {
    color: '#3333FF'
  },
});

const FooterProperty = ({name, value}) => (
  <View style={styles.property}>
    <Text style={styles.inlineLabel}>{name}: </Text><Text style={styles.blueValue}>{value}</Text>
  </View>
);

const Footer = ({updatedByUser, updatedAt}: {updatedByUser: string, updatedAt: string}) => (
  <View style={styles.footer} wrap={false}>
    <FooterProperty name="Updated By" value={updatedByUser} />
    <FooterProperty name="Updated At" value={updatedAt} />
  </View>
);

export default Footer;
