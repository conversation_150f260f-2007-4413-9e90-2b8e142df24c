import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  BelongsTo,
  <PERSON>quelize,
  <PERSON><PERSON>any,
  AllowNull
} from 'sequelize-typescript';
import FormCategory from './category.model';
import User from './user.model';
import UserGroup from './group.model';
import FormLocation from './formLocation.model';
import FormRecord from './formRecord.model';
import FormSchedule from './form-schedule.model';

@Table({
  tableName: 'forms'
})
class Form extends Model<Form> {

  @Column({
    type: DataType.DATE,
    allowNull: true
  })
  archivedAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true
  })
  dueAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true
  })
  alertAt: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  createdById: number | null;

  @BelongsTo(() => User, 'createdById')
  createdByUser: User;

  @ForeignKey(() => FormCategory)
  @Column
  categoryId: number;

  @BelongsTo(() => FormCategory)
  category: FormCategory;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  approverUserId: number;

  @BelongsTo(() => User, 'approverUserId')
  approverUser: User;

  @ForeignKey(() => FormLocation)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  formLocationId: number;

  @BelongsTo(() => FormLocation, 'formLocationId')
  formLocation: FormLocation;

  @ForeignKey(() => UserGroup)
  @Column({
    type: DataType.INTEGER,
    allowNull: true
  })
  userGroupId: number;

  @BelongsTo(() => UserGroup, 'userGroupId')
  userGroup: UserGroup;

  @Column({
    type: DataType.INTEGER,
    allowNull: false
  })
  stage: number;

  @HasMany(() => FormRecord)
  records: FormRecord[];

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  notifyOnComplete: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  issueNumber: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    defaultValue: null,
    allowNull: true,
  })
  assignedUserId: number;

  @BelongsTo(() => User, 'assignedUserId')
  assignedUser: User;

  @ForeignKey(() => Form)
  @Column({
    type: DataType.INTEGER,
    defaultValue: null,
    allowNull: true,
  })
  parentFormId: number;

  @ForeignKey(() => Form)
  @Column({
    type: DataType.INTEGER,
    defaultValue: null,
    allowNull: true,
  })
  templateFormId: number;

  /** Used to store the very top level form attached to this form */
  @Column({
    type: DataType.VIRTUAL,
    defaultValue: null,
    allowNull: true,
  })
  ancestorFormId: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  isDeleted: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: null,
  })
  deleteReason: string;

  @Column({
    type: DataType.VIRTUAL,
    get: function() {
      return this.dataValues['isOverdue'] === 1;
    }
  })
  isOverdue: boolean;

  @Column({
    type: DataType.VIRTUAL,
    get: function() {
      return this.dataValues['isAlertOverdue'] === 1;
    }
  })
  isAlertOverdue: boolean;

  @Column({
    type: DataType.STRING({length: 15000}),
    defaultValue: '',
    allowNull: false,
  })
  summary: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: false,
  })
  isTemplate: boolean;
  
  @AllowNull(true)
  @ForeignKey(() => FormSchedule)
  @Column({
    type: DataType.INTEGER,
    allowNull: true 
  })
  formScheduleId: number;
  
  @BelongsTo(() => FormSchedule, 'formScheduleId')
  schedule: FormSchedule;

  @Column({
	  type: DataType.VIRTUAL
  })
  executionSuccess: boolean | undefined;

  @Column({
	  type: DataType.VIRTUAL
  })
  executionMessage: string | undefined;
}

export default Form;

