import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { DocumentController } from '../controller/documentDRM.controller';
import {
  validateBodyParams,
  validateQueryParams,
  validateRouteParams
} from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';
import { CreateDocumentInputModel, DocumentQueryParams, UpdateDocumentInputModel } from '../../common/contracts/documentDRM';
import { preFillDefaultQueryParams } from '../middleware/preFillMiddleware';
import { docEditMiddleware } from '../middleware/adminRoleGroupMiddleware';
import { TFormDocument } from '../../common/contracts/document';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/documentDRM', adminMiddleware);

/**
 Document routes
 */
router.get('/documentDRM',
  preFillDefaultQueryParams({skip: '0', limit: '10', sortBy: 'name', order: 'asc'}),
  validateQueryParams(DocumentQueryParams),
  responseWrap(DocumentController.getDocuments));

router.get('/documentDRM/:id', validateRouteParams(GetByIdParams), responseWrap(DocumentController.getDocumentById));
router.post('/documentDRM', docEditMiddleware, validateBodyParams(CreateDocumentInputModel), responseWrap(DocumentController.createDocument));
router.put('/documentDRM', docEditMiddleware, validateBodyParams(UpdateDocumentInputModel), responseWrap(DocumentController.updateDocument));
router.delete('/documentDRM/:id', docEditMiddleware, validateRouteParams(GetByIdParams), responseWrap(DocumentController.archiveDocument));
router.get('/documentDRM/:id/documentInstance', validateRouteParams(GetByIdParams), responseWrap(DocumentController.getDocumentInstanceByDocumentId));
router.get('/documentDRM/:documentId/defaultDraftInstance/:formId', responseWrap(DocumentController.getDefaultDocument, TFormDocument));
router.get('/documentDRM/:documentId/defaultDraftInstance', responseWrap(DocumentController.getDefaultDocument, TFormDocument));

export default router;
