import * as React from 'react';
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import * as moment from 'moment';
import 'moment-timezone';
import { IRecordWithDateOutputModel } from "../../common/contracts/record";
import { IRecordPropertyType } from "../../common/contracts/recordProperty";
import Footer from "./components/footer";
import FollowUps from "./components/followUps";
import { timeZone } from '../../common/constants';

const styles = StyleSheet.create({
  wrapper: {
  },
  header: {
    backgroundColor: '#19a0b7',
    padding: 10,
    paddingTop: 12,
    marginTop: 10,
    marginBottom: 10,
    fontSize: 32,
    lineHeight: 0.8,
    flexDirection: 'col',
    justifyContent: 'center',
    color: '#FFFFFF',
  },
  formTitle: {
    flex: 4,
    fontSize: 32,
    marginBottom: 10,
    color: '#ed7d31',
  },
  firstRow: {
    flexDirection: 'row',
    marginTop: 10,
    marginBottom: 14,
  },
  firstRowItem: {
    flex: 1,
    flexDirection: 'col',
    fontSize: 11,
  },
  contactedOriginatorBlock: {
    flexDirection: 'row',
    fontSize: 11,
    marginTop: 10,
  },
  blockLabelText: {
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
    marginRight: 5,
  },
  labelText: {
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
    marginRight: 5,
    marginTop: 10,
  },
  centralBlock: {
    flexDirection: 'row',
  },
  leftCentralBlock: {
    flexDirection: 'col',
    flex: 2,
    paddingRight: 10,
  },
  summaryText: {
    fontSize: 11,
    color: '#000000',
    lineHeight: '180%',
    paddingLeft: 12,
    marginBottom: 8,
  },
});

const furStatusMap = {
  resolved: 'NOW RESOLVED',
  onTrack: 'ON TRACK',
  other: 'OTHER',
};

export class FollowUpFormStage1 extends React.Component<{
  recordData: IRecordWithDateOutputModel,
  followUpGroupMap: {[n: number]: string},
}> {
  render() {

    const propertyMap: {[k: string]: IRecordPropertyType}  = {};

    this.props.recordData.properties.forEach(prop => {
      propertyMap[prop.property.name] = prop;
    });

    const updatedByUser = this.props.recordData.createdByUser ? this.props.recordData.createdByUser.firstName + ' ' + this.props.recordData.createdByUser.lastName : '';
    const updatedAt = moment(this.props.recordData.createdAt).tz(timeZone).format('DD-MM-YYYY');

    const followUpsProperty = propertyMap['followUps'];

    return (
      <View style={styles.wrapper}>
        <View style={styles.header} wrap={false}>
          <Text>PROGRESS AND RESOLUTION</Text>
        </View>

        <View style={styles.centralBlock}>
          <View style={styles.leftCentralBlock}>
            <View style={styles.contactedOriginatorBlock}>
              <Text style={styles.blockLabelText}>2. This FUR is:</Text>
              <Text>{propertyMap['furStatus'] ? furStatusMap[propertyMap['furStatus'].stringData as string] : ''}</Text>
            </View>
            <View style={styles.contactedOriginatorBlock}>
              <Text style={styles.blockLabelText}>3. Did you check with originator/customer‘s:</Text>
              <Text>{propertyMap['hasContactedOriginator'] ? (propertyMap['hasContactedOriginator'].intData === 1 ? 'Yes' : 'No') : ''}</Text>
            </View>
            <Text style={styles.labelText}>4. Detail / Outcome:</Text>
            <Text style={styles.summaryText}>
              {propertyMap['detail'] ? propertyMap['detail'].stringData : ''}
            </Text>
            <Text style={styles.labelText}>5. Any other action/referral/improvement required now:</Text>
            <Text style={styles.summaryText}>
              {propertyMap['notes'] ? propertyMap['notes'].stringData : ''}
            </Text>
            <FollowUps followUpsJson={followUpsProperty.jsonData} followUpGroupMap={this.props.followUpGroupMap}/>
          </View>
        </View>
        <Footer updatedByUser={updatedByUser} updatedAt={updatedAt} />
      </View>
    );
  }
}
