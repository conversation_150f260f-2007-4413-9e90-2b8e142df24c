import {
    Table,
    Column,
    Model,
    DataType,
    ForeignKey,
    BelongsTo,
  } from 'sequelize-typescript';
import Document from './document.model';
import FormRecord from './formRecord.model';
  
  @Table({
    tableName: 'document_instance'
  })
  class DocumentInstance extends Model<DocumentInstance> {
  
    @ForeignKey(()=> Document)
    @Column({
      type: DataType.INTEGER,
      allowNull: false
    })
    documentId: number;

    @BelongsTo(() => Document)
    document: Document;
  
    @Column({
      type: DataType.STRING,
      allowNull: true
    })
    path: string;
  
    @ForeignKey(() => FormRecord)
    @Column({
      type: DataType.INTEGER,
      allowNull: false
    })
    formId: number;
  
    @Column({
        type: DataType.DATE,
        allowNull: true,
    })
    createdAt: Date

    @Column({
        type: DataType.DATE,
        allowNull: true,
    })
    aprovedAt: Date

    @Column({
        type: DataType.STRING,
        allowNull: true,
    })
    aprovedBy: string

    @Column({
      type: DataType.STRING(100),
      allowNull: false,
    })
    mimeType: string;

    @Column({
      type: DataType.INTEGER,
      allowNull: false,
      defaultValue: 1
    })
    version: number;
    
    @Column({
      type: DataType.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    })
    isArchived: boolean;

    @Column({
      type: DataType.STRING,
      allowNull: true
    })
    fileName: string;

    /**
     * This is required for providing draft versions
     * of documents for references or for future edits.
     * 'Original' refers to the orignal draft document which
     * was aproved.
     */
    @Column({
      type: DataType.STRING,
      allowNull: false,
    })
    originalPath: string;

    @Column({
      type: DataType.STRING,
      allowNull: false,
    })
    originalMimeType: string;

    @Column({
      type: DataType.STRING,
      allowNull: true
    })
    originalFileName: string
  }
  
  export default DocumentInstance;
  