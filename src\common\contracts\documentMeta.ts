import * as t from 'io-ts';
import { PaginationResponse } from './pagination';
import { constructType } from './extend';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { SortOrderValues } from './common';
export function constructDocumentMetaType<T>(dateType: t.Type<T>) {
  return t.type({
    id: t.number,
    documentId: t.number,
    tagId: t.number,
    value: t.string,
    createdAt: dateType,
    updatedAt: dateType,
  });
}

export const DocumentMetaOutputModel = constructDocumentMetaType(t.string);

export interface IDocumentMetaOutputModel extends t.TypeOf<typeof DocumentMetaOutputModel> {}

export type DocumentMetaPaginationResponse = PaginationResponse<IDocumentMetaOutputModel>;

export const UpdateDocumentMetaInputModel = t.exact(t.type({
  id:t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  documentId: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  tagId: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  value: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Value should not be empty or it\'s too long '),
}));

export interface IUpdateDocumentMetaInputModel extends t.TypeOf<typeof UpdateDocumentMetaInputModel> {}

export const SyncDocumentMetaInputModel = t.array(t.exact(t.type({
  tagId: t.refinement(t.number, id => id > 0, 'TagId is invalid'),
  value: t.refinement(t.string, v => (v.length > 0 || v.length < 128), 'Value should not be empty or longer than 128 characters'),
})));

export interface IDocumentMetaSyncInputModel extends t.TypeOf<typeof SyncDocumentMetaInputModel> {}

export const CreateDocumentMetaInputModel = t.exact(t.type({
    documentId: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
    tagId: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
    value: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Value should not be empty or it\'s too long '),
}));

export interface ICreateDocumentMetaInputModel extends t.TypeOf<typeof CreateDocumentMetaInputModel> {}

// See comment in /contracts/common.ts for explanation for this ignore
// @ts-ignore
export const DocumentMetaSortedField = t.union(['createdAt', 'updatedAt', 'name'].map(f => t.literal(f)));

export type IDocumentMetaSortedField = t.TypeOf<typeof DocumentMetaSortedField>;

export const DocumentMetaQueryParams = constructType({
  skip: IntFromString,
  limit: IntFromString,
  sortBy: DocumentMetaSortedField,
  order: SortOrderValues
}, {
  // countUsers: t.refinement(IntFromString, v => v === 1 || v === 0, 'valid countUsers parameter' ),
});

export interface IDocumentMetaQueryParams extends t.TypeOf<typeof DocumentMetaQueryParams> {}
