import * as React from 'react';
import * as moment from 'moment';
import 'moment-timezone';
import { Document, Page, StyleSheet } from '@react-pdf/renderer';
import { IFormOutputValidationModel } from "../../common/contracts/form";
import { IEnumsOutputModel } from "../../common/contracts/enums";
import FormLocation from "../db/models/formLocation.model";
import { IRecordWithDateOutputModel } from "../../common/contracts/record";
import { AuditFormStage0 } from './audit-stage0.report';
import { AuditFormStage1 } from './audit-stage1.report';
import { AuditFormStage2 } from './audit-stage2.report';
import { timeZone } from '../../common/constants';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'col',
    backgroundColor: '#FFFFFF',
    padding: 20,
    paddingLeft: 30,
  }
});

export class AuditFormsReport extends React.Component<{
  formData: IFormOutputValidationModel,
  records: IRecordWithDateOutputModel[],
  location: FormLocation | null,
  auditFormTypes: IEnumsOutputModel[],
  followUpGroupMap: {[n: number]: string},
  userGroupMap: {[n: number]: string},
  reassignedUserMap: {[n: number]: string},
}> {
  render() {

    return (

      <Document>
        <Page size="A4" style={styles.page} wrap={true}>
          {
            this.props.records.map((record, i) => {

              switch (record.stage) {

                case 0: return <AuditFormStage0
                  key={i}
                  formData={this.props.formData}
                  recordData={record}
                  location={this.props.location}
                  auditFormTypes={this.props.auditFormTypes}
                  userGroupMap={this.props.userGroupMap}
                  dueAt={this.props.formData.dueAt ? moment(this.props.formData.dueAt).tz(timeZone).format('DD/MM/YYYY') : ''}
                />;

                case 1: return <AuditFormStage1
                  key={i}
                  recordData={record}
                  followUpGroupMap={this.props.followUpGroupMap}
                />;

                case 2: return <AuditFormStage2
                  key={i}
                  recordData={record}
                  followUpGroupMap={this.props.followUpGroupMap}
                  reassignedUserMap={this.props.reassignedUserMap}
                />;

                default: return null;
              }
            })
          }
        </Page>
      </Document>
    );
  }
}
