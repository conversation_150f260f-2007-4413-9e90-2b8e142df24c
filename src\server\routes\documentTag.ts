import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { DocumentTagController } from '../controller/documentTag.controller';
import {
  validateBodyParams,
  validateRouteParams
} from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';
import { CreateDocumentTagInputModel, UpdateDocumentTagInputModel } from '../../common/contracts/documentTag';
import { docEditMiddleware } from '../middleware/adminRoleGroupMiddleware';

const router = express.Router();

/**
 * Allow access to admins only
 */

/**
 Tag routes
 */
router.get('/document-tag/document/:id', validateRouteParams(GetByIdParams), responseWrap(DocumentTagController.getDocumentTagsByDocumentId));
router.post('/document-tag', docEditMiddleware, validateBodyParams(CreateDocumentTagInputModel), responseWrap(DocumentTagController.createDocumentTag));
router.put('/document-tag', docEditMiddleware, validateBodyParams(UpdateDocumentTagInputModel), responseWrap(DocumentTagController.updateDocumentTag));
router.delete('/document-tag/:id', docEditMiddleware, validateRouteParams(GetByIdParams), responseWrap(DocumentTagController.archiveDocumentTag));

export default router;
