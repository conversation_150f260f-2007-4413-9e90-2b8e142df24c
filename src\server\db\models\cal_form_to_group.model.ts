import { Table, Column, Model, ForeignKey } from 'sequelize-typescript';
import Form from './form.model';
import UserGroup from './group.model';


@Table({
  tableName: 'cal_form_to_group',
  timestamps: false,
})
class CALFormToGroup extends Model<CALFormToGroup> {

  @ForeignKey(() => UserGroup)
  @Column
  groupId: number;

  @ForeignKey(() => Form)
  @Column
  formId: number;

}

export default CALFormToGroup;
