'use strict';

module.exports = {
	up: async (queryInterface, Sequelize) => {
		try {
			// Check if table exists first
			const tableExists = await queryInterface.showAllTables().then(tables =>
				tables.includes('risks')
			);

			if (!tableExists) {
				console.log('Table "risks" does not exist, skipping migration');
				return Promise.resolve(true);
			}

			const tableDefinition = await queryInterface.describeTable('risks');

			// Only add columns if they don't exist
			if (!tableDefinition['locationId']) {
				await queryInterface.addColumn('risks', 'locationId', {
					type: Sequelize.INTEGER,
					allowNull: false
				});
			}

			if (!tableDefinition['groupId']) {
				await queryInterface.addColumn('risks', 'groupId', {
					type: Sequelize.INTEGER,
					allowNull: false,
				});
			}

			return Promise.resolve(true);
		} catch (e) {
			console.log("ERROR MIGRATING SCRIPT");
			console.log(e);
			// Don't throw error if columns already exist
			if (e.message && e.message.includes('already exists')) {
				console.log('Columns already exist, continuing...');
				return Promise.resolve(true);
			}
			throw e;
		}
	},

	down: async (queryInterface, Sequelize) => {
		return false;
	}
};
