'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    const tableDefinition = await queryInterface.describeTable('forms');

    if (tableDefinition['assignedUserId']) {
      return;
    }

    await queryInterface.addColumn('forms', 'assignedUserId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: null,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('forms', 'assignedUserId');
  }
};
