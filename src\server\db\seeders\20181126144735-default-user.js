'use strict';
const argon2 = require('argon2');

module.exports = {
  up: (queryInterface, Sequelize) => {
    return argon2.hash('testPassword').then(bootstrapPassword => {
      return queryInterface.bulkInsert('users', [{
        username: '<EMAIL>',
        password: bootstrapPassword,
        firstName: 'Admin',
        lastName: '',
        role: 'administrator',
      }], {});
    });
  },

  down: (queryInterface, Sequelize) => {
    /*
      Add reverting commands here.
      Return a promise to correctly handle asynchronicity.

      Example:
      return queryInterface.bulkDelete('People', null, {});
    */
  }
};
