'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDefinition = await queryInterface.describeTable('risks');

    if (tableDefinition['riskCode']) {
      await queryInterface.removeColumn('risks', 'riskCode');
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tableDefinition = await queryInterface.describeTable('risks');

    if (!tableDefinition['riskCode']) {
      await queryInterface.addColumn('risks', 'riskCode', {
        type: Sequelize.STRING,
        allowNull: true,
        defaultValue: null
      });
    }
  }
};
