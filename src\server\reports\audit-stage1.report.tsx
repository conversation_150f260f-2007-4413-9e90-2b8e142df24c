import * as React from 'react';
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import * as moment from 'moment';
import 'moment-timezone';
import { IRecordWithDateOutputModel } from "../../common/contracts/record";
import { IRecordPropertyType } from "../../common/contracts/recordProperty";
import Footer from "./components/footer";
import FollowUps from "./components/followUps";
import { timeZone } from '../../common/constants';

const styles = StyleSheet.create({
  wrapper: {
  },
  header: {
    backgroundColor: '#19a0b7',
    padding: 10,
    paddingTop: 10,
    marginTop: 10,
    marginBottom: 10,
    fontSize: 32,
    lineHeight: 0.8,
    flexDirection: 'col',
    justifyContent: 'center',
    color: '#FFFFFF',
  },
  formTitle: {
    flex: 4,
    fontSize: 32,
    marginBottom: 10,
    color: '#ed7d31',
  },
  firstRow: {
    flexDirection: 'row',
    marginTop: 10,
    marginBottom: 14,
  },
  firstRowItem: {
    flex: 1,
    flexDirection: 'col',
    fontSize: 11,
  },
  contactedOriginatorBlock: {
    flexDirection: 'col',
    fontSize: 11,
    marginTop: 10,
  },
  blockLabelText: {
    flexDirection: 'col',
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
    marginRight: 5,
  },
  labelText: {
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
    marginRight: 5,
    marginTop: 10,
  },
  centralBlock: {
    flexDirection: 'row',
  },
  leftCentralBlock: {
    flexDirection: 'col',
    flex: 2,
    paddingRight: 10,
  },
  summaryText: {
    fontSize: 11,
    color: '#000000',
    lineHeight: '180%',
    paddingLeft: 12,
    marginBottom: 8,
  },
});

const FirstRow = ({children}) => (
  <View style={styles.firstRow}>
    {children}
  </View>
);

const FirstRowItem = ({name, value}) => {
  return (<View style={styles.firstRowItem}>
    <Text style={styles.labelText}>{name}:</Text>
    <Text>{value}</Text>
  </View>);
};

export class AuditFormStage1 extends React.Component<{
  recordData: IRecordWithDateOutputModel,
  followUpGroupMap: {[n: number]: string},
}> {
  render() {

    const propertyMap: {[k: string]: IRecordPropertyType}  = {};

    this.props.recordData.properties.forEach(prop => {
      propertyMap[prop.property.name] = prop;
    });

    const updatedByUser = this.props.recordData.createdByUser ? this.props.recordData.createdByUser.firstName + ' ' + this.props.recordData.createdByUser.lastName : '';
    const updatedAt = moment(this.props.recordData.createdAt).tz(timeZone).format('DD-MM-YYYY');

    const followUpsProperty = propertyMap['followUps'];
    const facilityOption:string = propertyMap['facilityOption'] ? propertyMap['facilityOption'].stringData || '' : '';
    const facilityDetail:string = propertyMap['facilityDetail'] ? propertyMap['facilityDetail'].stringData || '' : '';
    const facilityText = facilityOption.length ? 
      facilityOption === 'facilitywide' ? 'Facility Wide' : 'Part Of Facility'
      : '';

    return (
      <View style={styles.wrapper}>
        <View style={styles.header} wrap={false}>
          <Text>Audit Review</Text>
        </View>

        <View style={styles.centralBlock}>
          <View style={styles.leftCentralBlock}>

          <FirstRow>
            <FirstRowItem name="Affected Area" value={facilityText} />
            { facilityOption !== 'facilitywide' &&
              <FirstRowItem name="" value={facilityDetail} />
            }
          </FirstRow>

            <Text style={styles.blockLabelText}>3. Analysis/Findings/Action - Any Adverse results or action items identifed?</Text>
            <Text style={styles.summaryText}>
              {propertyMap['summary'] ? propertyMap['summary'].stringData : ''}
            </Text>

            <View style={styles.contactedOriginatorBlock}>
              <Text style={styles.blockLabelText}>4. Any action/s mentioned in point 3 above?</Text>
              <Text style={styles.summaryText}>
                {propertyMap['anyActions'] ? (propertyMap['anyActions'].intData === 1 ? 'Yes' : 'No') : ''}
              </Text>
            </View>

            { propertyMap['anyActions'].intData === 1 &&
              <View>
                <Text style={styles.labelText}>Summary of Action/s</Text>
                <Text style={styles.summaryText}>
                  {propertyMap['actionSummary'] ? propertyMap['actionSummary'].stringData : ''}
                </Text>
              </View>
            }

            <FollowUps followUpsJson={followUpsProperty.jsonData} followUpGroupMap={this.props.followUpGroupMap}/>
          </View>
        </View>
        <Footer updatedByUser={updatedByUser} updatedAt={updatedAt} />
      </View>
    );
  }
}
