'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.bulkInsert('groups', [{
      groupName: 'Admin',
    },{
      groupName: 'Clinical Care',
    },{
      groupName: 'Domestic Services',
    },{
      groupName: 'Maintenance',
    },{
      groupName: 'Lifestyle'
    },{
      groupName: 'Senior Management'
    }], {});
  },

  down: (queryInterface, Sequelize) => {
    /*
      Add reverting commands here.
      Return a promise to correctly handle asynchronicity.

      Example:
      return queryInterface.bulkDelete('People', null, {});
    */
  }
};
