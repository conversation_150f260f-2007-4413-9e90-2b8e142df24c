import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { adminMiddleware } from '../middleware/adminMiddleware';
import { FormController } from '../controller/form.controller';
import { validateBodyParams, validateQueryParams, validateRouteParams } from '../middleware/validationMidleware';
import {
	DuplicateFormParams,
	FormDeleteParams,
	FormParams,
	FormsQueryParams,
	FormUpdateParams,
	SendFormReportParams,
	FormScheduleParams,
	FormRevertParams
} from '../../common/contracts/form';
import { preFillDefaultQueryParams } from '../middleware/preFillMiddleware';
import { GetByIdParams } from '../../common/contracts/common';
import { adminManagerMiddleware } from '../middleware/adminManagerMiddleware';

const router = express.Router();

/**
 Form routes
 */
router.get('/form',
	preFillDefaultQueryParams({ skip: '0', limit: '10', sortBy: 'dueAt', order: 'asc' }),
	validateQueryParams(FormsQueryParams),
	responseWrap(FormController.getForms));
router.get('/form/:id', validateRouteParams(GetByIdParams), responseWrap(FormController.getFormById));
router.get('/form/:id/execute', validateRouteParams(GetByIdParams), responseWrap(FormController.runTemplate));
router.post('/form', validateBodyParams(FormParams), responseWrap(FormController.createForm));
router.put('/form', validateBodyParams(FormUpdateParams), responseWrap(FormController.updateForm));
router.put('/form/schedule', validateBodyParams(FormScheduleParams), responseWrap(FormController.updateFormSchedule));
router.get('/form/schedule/run/all', adminManagerMiddleware, responseWrap(FormController.runSchedules));
router.post('/form/duplicate', validateBodyParams(DuplicateFormParams), responseWrap(FormController.duplicateForm));
router.get('/form/finalize/:id', validateRouteParams(GetByIdParams), responseWrap(FormController.finalizeForm));
router.put('/form/delete', adminMiddleware, validateBodyParams(FormDeleteParams), responseWrap(FormController.deleteForm));
router.put('/form/revert', adminMiddleware, validateBodyParams(FormRevertParams), responseWrap(FormController.revertForm));
router.post('/form/sendReport', adminManagerMiddleware, validateBodyParams(SendFormReportParams), responseWrap(FormController.sendReport));
router.get('/form/document/:id', validateRouteParams(GetByIdParams), responseWrap(FormController.getFormsFromDocumentId));
router.post('/form/reviewNotification', responseWrap(FormController.sendReviewFormNotifications));

export default router;
