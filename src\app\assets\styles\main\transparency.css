/* Generic Transparency Definitions */
.t-95 {
  /* IE 8 */
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=95)";

  /* IE 5-7 */
  filter: alpha(opacity=95);

  /* Netscape */
  -moz-opacity: 0.95;

  /* Safari 1.x */
  -khtml-opacity: 0.95;

  /* Good browsers */
  opacity: 0.95;
}

.t-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=90)";
  filter: alpha(opacity=90);
  -moz-opacity: 0.9;
  -khtml-opacity: 0.9;
  opacity: 0.9;
}

.t-85 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)";
  filter: alpha(opacity=85);
  -moz-opacity: 0.85;
  -khtml-opacity: 0.85;
  opacity: 0.85;
}

.t-80 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
  filter: alpha(opacity=80);
  -moz-opacity: 0.8;
  -khtml-opacity: 0.8;
  opacity: 0.8;
}

.t-75 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=75)";
  filter: alpha(opacity=75);
  -moz-opacity: 0.75;
  -khtml-opacity: 0.75;
  opacity: 0.75;
}

.t-70 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=70)";
  filter: alpha(opacity=70);
  -moz-opacity: 0.7;
  -khtml-opacity: 0.7;
  opacity: 0.7;
}

.t-65 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=65)";
  filter: alpha(opacity=65);
  -moz-opacity: 0.65;
  -khtml-opacity: 0.65;
  opacity: 0.65;
}

.t-60 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=60)";
  filter: alpha(opacity=60);
  -moz-opacity: 0.6;
  -khtml-opacity: 0.6;
  opacity: 0.6;
}

.t-55 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=55)";
  filter: alpha(opacity=55);
  -moz-opacity: 0.55;
  -khtml-opacity: 0.55;
  opacity: 0.55;
}

.t-50 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
  filter: alpha(opacity=50);
  -moz-opacity: 0.5;
  -khtml-opacity: 0.5;
  opacity: 0.5;
}

.t-45 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=45)";
  filter: alpha(opacity=45);
  -moz-opacity: 0.45;
  -khtml-opacity: 0.45;
  opacity: 0.45;
}

.t-40 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
  filter: alpha(opacity=40);
  -moz-opacity: 0.4;
  -khtml-opacity: 0.4;
  opacity: 0.4;
}

.t-35 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=35)";
  filter: alpha(opacity=35);
  -moz-opacity: 0.35;
  -khtml-opacity: 0.35;
  opacity: 0.35;
}

.t-30 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
  filter: alpha(opacity=30);
  -moz-opacity: 0.3;
  -khtml-opacity: 0.3;
  opacity: 0.3;
}

.t-25 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=25)";
  filter: alpha(opacity=25);
  -moz-opacity: 0.25;
  -khtml-opacity: 0.25;
  opacity: 0.25;
}

.t-20 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=20)";
  filter: alpha(opacity=20);
  -moz-opacity: 0.2;
  -khtml-opacity: 0.2;
  opacity: 0.2;
}

.t-15 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=15)";
  filter: alpha(opacity=15);
  -moz-opacity: 0.15;
  -khtml-opacity: 0.15;
  opacity: 0.15;
}

.t-10 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=10)";
  filter: alpha(opacity=10);
  -moz-opacity: 0.1;
  -khtml-opacity: 0.1;
  opacity: 0.1;
}

.t-5 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=05)";
  filter: alpha(opacity=05);
  -moz-opacity: 0.05;
  -khtml-opacity: 0.05;
  opacity: 0.05;
}

.t-0 {
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  -moz-opacity: 0;
  -khtml-opacity: 0;
  opacity: 0;
}
