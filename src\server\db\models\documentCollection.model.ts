import { Table, Column, Model, DataType, Sequelize, Unique } from 'sequelize-typescript';

export enum reviewScheduleType {
  ANNUALLY = 'ANNUALLY',
  BIANNUALLY = 'BIANNUALLY',
  BIENNIALLY = 'BIENNIALLY'
}
@Table({
  tableName: 'document_collections'
})
class DocumentCollection extends Model<DocumentCollection> {

  @Unique
  @Column
  name: string;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)'),
    onUpdate: 'CURRENT_TIMESTAMP(3)'
  })
  updatedAt: Date;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isArchived: boolean;

  @Column({
    type: DataType.ENUM('ANNUALLY','BIANNUALLY', 'BIEN<PERSON>ALLY'),
    defaultValue: 'ANNUALLY'
  })
  reviewSchedule: reviewScheduleType
}

export default DocumentCollection;


