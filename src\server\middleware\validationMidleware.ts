import * as express from 'express';
import * as t from 'io-ts';
import { reporter } from 'io-ts-reporters';

export const validateBodyParams = (reqType: t.Type<any>) => (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const result = reqType.decode(req.body);

  if (result.isRight()) {
    next();
  } else {
    res.status(406).json({
      success: false,
      errors: reporter(result),
    });
  }
};

export const validateRouteParams  = (reqType: t.Type<any>) => (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const result = reqType.decode(req.params);

  if (result.isRight()) {
    req.params = result.value;
    next();
  } else {
    res.status(406).json({
      success: false,
      errors: reporter(result),
    });
  }
};

export const validateQueryParams = (reqType: t.Type<any>) => (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const result = reqType.decode(req.query);

  if (result.isRight()) {
    req.query = result.value;
    next();
  } else {
    res.status(406).json({
      success: false,
      errors: reporter(result),
    });
  }
};
