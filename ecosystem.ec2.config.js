module.exports = {
  apps : [{
    name: 'bcc-development',
    // Cannot be set per env, something to be cautious of
    script: './current/build/index.js',

    // Options reference: https://pm2.io/doc/en/runtime/reference/ecosystem-file/
    args: 'one two',
    interpreter: "/home/<USER>/.nvm/versions/node/v12.18.3/bin/node",
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development',
    },
    env_production: {
      NODE_ENV: 'production'
    },
    "log_date_format" : "YYYY-MM-DD HH:mm Z"
  }]
};
