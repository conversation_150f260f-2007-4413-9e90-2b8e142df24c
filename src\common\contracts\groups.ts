import * as t from 'io-ts';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { PaginationResponse } from './pagination';
import { SortOrderValues } from './common';
import { constructType } from './extend';

export function constructUserGroupType<T>(dateType: t.Type<T>) {
  return t.type({
    id: t.number,
    groupName: t.string,
    createdAt: dateType,
    updatedAt: dateType,
    usersCount: t.union([t.number, t.undefined]),
  });
}

export const UserGroupOutputModel = constructUserGroupType(t.string);

export interface IUserGroupOutputModel extends t.TypeOf<typeof UserGroupOutputModel> {}

export type GroupsPaginationResponse = PaginationResponse<IUserGroupOutputModel>;

export const UpdateUserGroupInputModel = t.exact(t.type({
  id: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  groupName: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Group name should not be empty or it\'s too long '),
}));

export interface IUpdateUserGroupInputModel extends t.TypeOf<typeof UpdateUserGroupInputModel> {}

export const CreateUserGroupInputModel = t.exact(t.type({
  groupName: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Group name should not be empty or it\'s too long '),
}));

export interface ICreateUserGroupInputModel extends t.TypeOf<typeof CreateUserGroupInputModel> {}

// See comment in /contracts/common.ts for explanation for this ignore
// @ts-ignore
export const GroupsSortedField = t.union(['createdAt', 'updatedAt', 'groupName', 'usersCount'].map(f => t.literal(f)));

export type IGroupsSortedField = t.TypeOf<typeof GroupsSortedField>;

export const GroupsQueryParams = constructType({
  skip: IntFromString,
  limit: IntFromString,
  sortBy: GroupsSortedField,
  order: SortOrderValues
}, {
  countUsers: t.refinement(IntFromString, v => v === 1 || v === 0, 'valid countUsers parameter' ),
});

export interface IGroupsQueryParams extends t.TypeOf<typeof GroupsQueryParams> {}
