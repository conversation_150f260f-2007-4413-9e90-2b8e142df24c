import * as t from 'io-ts';

export const RecordPropertyType = t.type({
  id: t.number,
  formRecordId: t.number,
  enumId: t.union([t.number, t.null]),
  intData: t.union([t.number, t.null]),
  jsonData: t.union([t.string, t.null]),
  propertyId: t.number,
  stringData: t.union([t.string, t.null]),
  dateData: t.any,
  property: t.type({
    id: t.number,
    name: t.string,
  }),
});

export interface IRecordPropertyType extends t.TypeOf<typeof RecordPropertyType> {}

