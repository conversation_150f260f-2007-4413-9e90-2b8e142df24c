.inner {
  height: 100%;
  cursor: pointer;
  position: relative;
}

.contact-details {
  font-size: 24px;
}

.contact-details a {
  font-weight: bold;
  font-color: #619671;
}

.contact-details .fa {
  display: inline-block;
  width: 35px;
  text-align: center;
}

.banner img {
  max-height: 150px;
}

.content, .details {
  padding: 0 18px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.content {
  margin-bottom: 42px;
}

/* styles for '...' */
.block-with-text {
  /* hide text if it more than N lines  */
  overflow: hidden;
  /* for set '...' in absolute position */
  position: relative;
  /* Se the font size */
  font-size: 14px;
  /* use this value to count block height */
  line-height: 16px;
  /* max-height = line-height (1.2) * lines max number (3) */
  max-height: 48px;
  /* fix problem when last visible word doesn't adjoin right side  */
  text-align: justify;

  /* */
  margin-right: -14px;
  padding-right: 14px;
}
.block-with-text:before {
  /* points in the end */
  content: '...';
  /* absolute position */
  position: absolute;
  /* set position to right bottom corner of block */
  right: 0;
  bottom: 0;
}
.block-with-text:after {
  /* points in the end */
  content: '';
  /* absolute position */
  position: absolute;
  /* set position to right bottom corner of text */
  right: 0;
  width: 1em;
  /* set width and height */
  height: 1em;
  margin-top: 0.2em;
  background: white;
}

.title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 22px;
  margin-bottom: 0;
}

.subtitle {
  font-size: 15px;
  margin-top: 0;
}

.shortcuts {
  font-size: 24px;
  letter-spacing: 20px;
  position: absolute;
  width: 100%;
  bottom: 0;
}

/* When the details are full screen */
.verbose .details {
  padding: 10px;
}

.verbose .banner img {
  max-height: 250px;
}
