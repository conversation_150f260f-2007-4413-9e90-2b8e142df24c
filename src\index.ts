require('dotenv').config(); // Init environment variables from .env, Important to run this before connecting to DB

const server = require('./server/server').default;
const { Db } = require('./server/server.schema');

const port = process.env.APP_PORT || 4200;

console.log(`Application will run on Port: ${port}`);

Db.init()
  .then(() => {
    console.log('Database connected successfully');
    startServer();
  })
  .catch((error: any) => {
    console.log('Database connection failed, starting server without database:', error.message);
    startServer();
  });

function startServer() {
  server.listen(port, (err: any) => {
    if (err) {
      return console.log(err);
    }

    return console.log(`server is listening on ${port}`);
  });
}
