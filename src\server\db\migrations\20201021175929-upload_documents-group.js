'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const existing = queryInterface.rawSelect('groups', {
      where: {
        groupName: 'Update Documents'
      }
    }, ['id']);

    if (!existing) {
      return queryInterface.bulkInsert('groups', [{
        groupName: 'Update Documents',
        createdAt: new Date(),
        updatedAt: new Date(),
        isArchived: false
      }]);
    }

    return Promise.resolve();
  },

  down: (queryInterface, Sequelize) => {
  }
};
