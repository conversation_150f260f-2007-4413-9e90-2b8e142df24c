'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    const tableDefinition = await queryInterface.describeTable('forms');

    if (tableDefinition['notifyOnComplete']) {
      return;
    }

    await queryInterface.addColumn('forms', 'notifyOnComplete', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: null
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('forms', 'notifyOnComplete');
  }
};
