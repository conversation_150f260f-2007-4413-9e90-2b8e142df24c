'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    let tableDefinition = await queryInterface.describeTable('forms');

    if (!tableDefinition['isDeleted']) {
      await queryInterface.addColumn('forms', 'isDeleted', {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: true,
      });
    }

    if (!tableDefinition['deleteReason']) {
      await queryInterface.addColumn('forms', 'deleteReason', {
        type: Sequelize.STRING(255),
        allowNull: true,
        defaultValue: null,
      });
    }

  },

  down: async (queryInterface, Sequelize) => {

    await queryInterface.removeColumn('forms', 'isDeleted');
    await queryInterface.removeColumn('forms', 'deleteReason');

  }
};
