import * as argon2 from 'argon2';
import { UsersController } from './users.controller';
import { IUserWithDateOutputModel } from '../../common/contracts/users';
import { IUpdateUserProfileInputModel } from '../../common/contracts/currentUser';
import User from '../db/models/user.model';
import { BadRequestError, ServerError } from '../../model/HttpErrors';

export class CurrentUserController {

  public static async getCurrentUser(req: JwtRequest): Promise<IUserWithDateOutputModel> {
    req.params.id = req.tokenData.user.id.toString();

    return await UsersController.getUserById(req);
  }

  public static async updateUserProfile(req: JwtRequest) {

    const profile = req.body as IUpdateUserProfileInputModel;

    const payload: { [k: string]: any } = {
      username: profile.username,
      phone: profile.phone,
      firstName: profile.firstName,
      lastName: profile.lastName,
    };

    const user = await User.findByPrimary(req.tokenData.user.id);

    if (!user || user.isArchived) {
      throw new ServerError('Cannot find user to change profile');
    }

    if (profile.oldPassword && profile.password) {

      const correct = await argon2.verify(user.password, profile.oldPassword);

      if (!correct) {
        throw new BadRequestError('You entered wrong old password.');
      }

      payload.password = await argon2.hash(profile.password);
    }

    try {

      await User.update(payload, {
        where: { id: req.tokenData.user.id }
      });

      return { status: 'ok' };

    } catch (e) {

      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('User with this e-mail already exists.');
      } else {
        console.log('UpdateUSerProfile exception', e);
        throw new ServerError();
      }
    }

  }

}
