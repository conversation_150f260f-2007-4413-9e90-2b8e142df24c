import { IFormParams, IFormRecordPropertyParam, IFormRecordParams } from '../../common/contracts/form';
import Form from '../db/models/form.model';
import FormCategory from '../db/models/category.model';
import FormSchedule from '../db/models/form-schedule.model';
import { Sequelize } from "sequelize-typescript";
import { NotFoundError } from '../../model/HttpErrors';
import { RecordService } from './record.service';
import FormRecordProperty from '../db/models/formRecordProperty.model';
import FormRecord from '../db/models/formRecord.model';
import * as moment from 'moment';
import "moment-timezone";

import Document from '../db/models/document.model';
import DocumentCollection from '../db/models/documentCollection.model';
import { DocumentDRMService } from './documentDRM.service';
import * as fs from 'fs';
import FormDocument from '../db/models/formDocument.model';
import * as path from 'path';
import Property from '../db/models/property.model';
import { numStrToArr } from '../utils/Utils';
import logger from '../utils/Logger';
import { timeZone } from '../../common/constants';
import Risks from '../db/models/risk.model';
import { RiskController } from '../controller/risk.controller';

const Op = Sequelize.Op;

/** Minimum requirements for calling the function */
interface DuplicateFormOptions {
	form: Form;
	propertiesToClear: string[];
	createdByUserId: number;
	beforePropertyMap: (properties: FormRecordProperty[]) => FormRecordProperty[];
	beforeSave: (form: IFormParams, props: FormRecordProperty[]) => IFormParams;
	beforeRecordSave: (record: IFormRecordParams) => IFormRecordParams;
	targetRecord: FormRecord | null;
}

interface DuplicateFormInputOptions extends Partial<DuplicateFormOptions> {
	form: Form;
}

type FormRecordPropertyWithName = IFormRecordPropertyParam & { name: string };

class FormServiceClass {
	private readonly className = "FormServiceClass";

	public async createForm(data: IFormParams, createdById: number | null) {
		const {
			categoryId,
			formLocationId,
			userGroupId,
			dueAt,
			alertAt,
			notifyOnComplete,
			stage,
			parentFormId,
			assignedUserId,
			isTemplate,
			summary,
			templateFormId
		} = data;

		const createProperties: Partial<Form> = {
			categoryId,
			createdById,
			stage
		};

		if (isTemplate) {
			createProperties['isTemplate'] = isTemplate;
		}

		if (summary) {
			createProperties['summary'] = summary;
		}

		if (formLocationId) {
			createProperties['formLocationId'] = formLocationId;
		}

		if (userGroupId) {
			createProperties['userGroupId'] = userGroupId;
		}

		if (dueAt) {
			createProperties['dueAt'] = new Date(dueAt);
		}

		if (alertAt) {
			createProperties['alertAt'] = new Date(alertAt);
		}

		if (notifyOnComplete) {
			createProperties['notifyOnComplete'] = notifyOnComplete;
		}

		if (parentFormId) {
			createProperties['parentFormId'] = parentFormId;
		}

		if (templateFormId) {
			createProperties['templateFormId'] = templateFormId;
		}

		if (assignedUserId) {
			createProperties.assignedUserId = assignedUserId as number;
		}

		createProperties.issueNumber = await this.getIssueNumber(categoryId, isTemplate);

		return await Form.create(createProperties);
	}

	public async getIssueNumber(categoryId: number, isTemplate: boolean | null | undefined) {
		// Get the category
		const formCategory: FormCategory | null = await FormCategory.findOne({
			where: {
				id: categoryId
			}
		});

		const lastForm: Form | null = await Form.findOne({
			limit: 1,
			where: {
				// your where conditions, or without them if you need ANY entry
			},
			order: [['id', 'DESC']]
		});

		// Next issue number here
		let result = '';

		if (lastForm) {
			result = (190000 + lastForm.id).toString();
		} else {
			result = '190000';
		}

		if (formCategory && formCategory.name.length) {
			result = formCategory.name.substr(0, 1).toUpperCase() + '-' + result;
		} else {
			result = 'U-' + result;
		}

		if (isTemplate)
			result = 'T' + result;

		return result;
	}

	public parseOffsetField(
		properties: FormRecordProperty[],
		destinationFieldName: string,
		sourceDatePeriod: string,
		sourceDateOffset?: string
	) {
		let dateData = moment().tz(timeZone);

		// console.log("--- source offset field ---");
		const sourceOffsetField = properties.find(prop => {
			return prop.property.name === sourceDateOffset
		});

		let offsetCount = 0;

		// console.log("--- source period field ---");
		const sourcePeriodField = properties.find(prop => prop.property.name === sourceDatePeriod);
		let sourcePeriod: string = '';

		if (sourceOffsetField) {
			offsetCount = sourceOffsetField.intData;
		}

		if (sourcePeriodField) {
			sourcePeriod = sourcePeriodField.stringData.toLowerCase();
		} else {
			throw new Error(`Source Period Field[${sourceDatePeriod}] contained no data. Field[${destinationFieldName}] cannot be calculated.`);
		}

		switch (sourcePeriod) {
			case 'same day':
				// Intentionally left blank
				break;
			case 'next day':
				dateData = dateData.add(1, 'day');
				break;
			case 'custom number of days':
				dateData = dateData.add(offsetCount, 'day');
				break;
			case 'monday this week':
				dateData = dateData.startOf('isoWeek');
				break;
			case 'monday of next week':
				dateData = dateData.add(7, 'day');
				dateData = dateData.startOf('isoWeek');
				break;
			case 'custom number of weeks':
				dateData = dateData.add(offsetCount, 'week');
				break;
			case '1st of this month':
				dateData = dateData.add(offsetCount, 'week');
				break;
			case '1st of next month':
				dateData = dateData.add(1, 'month').set({
					'hour': 0,
					'minute': 0,
					'second': 0
				});
				break;
			case 'custom number of months':
				dateData = dateData.add(offsetCount, 'month');
				break;
			default:
				throw new Error(`Unknown Offset Period ${sourcePeriod} in Field ${sourceDatePeriod}`);
		}

		return properties.push({
			dateData: dateData.toDate(),
			property: {
				name: destinationFieldName
			}
		} as unknown as FormRecordProperty);
	}

	public async runTemplate(template: Form): Promise<any> {
		const signature = this.className + `.runTemplate: Form[${template.id}] `;

		const targetRecord = await RecordService.getFirstRecordForDuplication(template.id);

		if (!targetRecord)
			throw new NotFoundError("Could not find target record");

		const locationRecord = targetRecord.properties.find(prop =>
			prop.property.name === 'templateLocation'
		);

		let locations: (number | null)[] = [];

		if (locationRecord && locationRecord.stringData.length) {
			let idArray = numStrToArr(locationRecord.stringData);

			locations.push(...idArray.map(Number));
			logger.silly(signature + `Using Locations[${locations.join(",")}]`);
		} else {
			logger.silly(signature + `Template location not available`)
		}

		if (!locations.length)
			locations = [null];

		const resultPromises = locations.map(async (location: number | null) =>
			new Promise(async (resolve, reject) => {
				try {
					const newForm = await this.duplicateForm({
						form: template,
						targetRecord,
						propertiesToClear: [
							'templateLocation',
							'templateDueAtPeriod',
							'templateDueAtOffset',
							'templatePeriodFromPeriod',
							'templatePeriodFromOffset',
							'templatePeriodToPeriod',
							'templatePeriodToOffset'
						],
						beforePropertyMap: props => {
							const result = [...props];

							this.parseOffsetField(result, 'dueAt', 'templateDueAtPeriod', 'templateDueAtOffset');
							this.parseOffsetField(result, 'periodFrom', 'templatePeriodFromPeriod', 'templatePeriodFromOffset');
							this.parseOffsetField(result, 'periodTo', 'templatePeriodToPeriod', 'templatePeriodToOffset');

							if (location) {
								let propertyRecord = new Property();
								propertyRecord.name = 'location';
								let locationProperty = new FormRecordProperty();
								locationProperty.intData = location;
								locationProperty.property = propertyRecord;

								result.push(locationProperty);
							}

							return result;
						},
						beforeSave: (newForm, props) => {
							newForm.assignedUserId = null;
							newForm.formLocationId = location;
							newForm.stage = 1;

							const dueAtProperty = props.find(prop => prop.property.name === 'dueAt')

							if (dueAtProperty) {
								const start = moment(new Date());
								const dueAt = moment(dueAtProperty.dateData);
								const dueAtOffset: number = (dueAt.diff(start, 'days') / 2);
								const alertAt = start.add(dueAtOffset, 'days');

								newForm.dueAt = dueAtProperty.dateData.toISOString();
								newForm.alertAt = alertAt.toISOString();
								newForm.summary = template.summary;
							}

							logger.silly(signature + `Setting templateFormId[${template.id}]`);
							newForm.templateFormId = template.id;

							return newForm;
						},
						beforeRecordSave: record => {
							record.isComplete = true;

							return record;
						}
					});

					resolve(newForm);
				} catch (e) {
					logger.error(signature + `There was an error processing Template[${template.id ? template.id : 'New Template'}]`);
					console.error(e);
					reject(e);
				}
			})
		);

		const result = await Promise.all<any>(resultPromises);

		return result;
	}

	public async duplicateForm(options: DuplicateFormInputOptions) {

		const opts: DuplicateFormOptions = Object.assign({
			createdByUserId: options.form.createdById,
			beforePropertyMap: props => props,
			beforeSave: form => form,
			beforeRecordSave: record => record,
			targetRecord: await RecordService.getFirstRecordForDuplication(options.form.id),
			propertiesToClear: []
		}, options);

		const { form, propertiesToClear, createdByUserId, targetRecord } = opts;

		if (!targetRecord) {
			throw new NotFoundError('Form has no valid record to duplicate.');
		}

		const beforePropertyMapResult = opts.beforePropertyMap([...targetRecord.properties]);

		const createParams = opts.beforeSave(Object.assign(
			{},
			form.toJSON(),
			{
				id: null,
				isDeleted: 0,
				issueNumber: null,
				stage: 0,
				dueAt: null,
				alertAt: null,
				createdAt: null,
				archivedAt: null,
				isTemplate: 0,
				assignedUserId: createdByUserId
			}
		), beforePropertyMapResult);

		const newForm = await FormService.createForm(
			createParams,
			createdByUserId
		);

		const documents = targetRecord.documents.map(document => ({ id: document.id }));
		const tickedDocuments = targetRecord.tickedDocuments.map(document => ({ id: document.id, isTicked: true }));
		// @ts-ignore
		const allDocuments = documents.concat(tickedDocuments);

		const properties = beforePropertyMapResult
			// Remove any properties that we don't want to reuse
			.filter(recordProperty =>
				!propertiesToClear.find(propertyToClear => propertyToClear === recordProperty.property.name)
			)

			// Now map the properties back to an input format where 'name' replaces the property data
			// Some performance improvements could be achieved by reusing the property id rather than
			// Going through all the normal checking of property names
			.map<FormRecordPropertyWithName>(recordProperty => {
				const { id, formRecordId, propertyId, property, dateData, ...result } = JSON.parse(JSON.stringify(recordProperty, null, 0));

				let dateString: string | null = null;

				if (dateData) {
					const dateDataDate = new Date(dateData);
					if (!(dateDataDate.toString() === 'Invalid Date')) {
						dateString = dateDataDate.toISOString();
					}
				}

				return Object.assign(result, {
					name: recordProperty.property.name,
					dateData: dateString
				});
			})
			;

		const recordCreateParams = opts.beforeRecordSave({
			formId: newForm.id,
			properties,
			stage: 0,
			documents: allDocuments,
			isComplete: false
		});

		await RecordService.createRecord(recordCreateParams, createdByUserId);

		return newForm;
	}

	/**
	 * Find all Forms where ProcessedTo date is LT 
	 */
	public async processFormSchedules() {
		const signature = this.className + ".processFormSchedules: ";
		logger.info(signature + "Processing Form Schedules");

		const schedules = await FormSchedule.findAll();
		logger.info(signature + `Found ${schedules.length.toString()} schedules`);
		const now = new Date();

		const schedulesToRun = schedules.filter(
			schedule => schedule.nextExecution && schedule.nextExecution < now
		);
		logger.info(signature + `Found ${schedulesToRun.length.toString()} requiring processing`);

		if (schedulesToRun.length === 0) {
			logger.info(signature + "Found no schedules to run. Process will end.");
			return;
		}

		const templates = await Form.findAll({
			where: {
				formScheduleId: { [Op.in]: schedulesToRun.map(schedule => schedule.id) }
			}
		});

		logger.info(signature + `Found ${templates.length.toString()} templates`);

		const templatePromises = templates.map(template => {
			const schedule = schedulesToRun.find(scheduleToRun => scheduleToRun.id === template.formScheduleId);

			if (!schedule) {
				logger.error(signature + "Something went wrong. Unable to find schedule belonging to template");
				throw new Error("Something went wrong. Unable to find schedule belonging to template");
			}

			if (template.archivedAt) {
				logger.info(signature + `Template[${template.id}] is archived. Archiving associated schedule`);

				return new Promise<void>(resolve => schedule.destroy().then(() => resolve()));
			}

			return new Promise<Form>(async (resolve, reject) => {
				logger.info(signature + `Executing Template[${template.id}]`);

				try {
					await this.runTemplate(template);
					template.executionSuccess = true;
				} catch (e) {
					template.executionSuccess = false;
					template.executionMessage = e.message;

					return resolve(template);
				}

				schedule.processedTo = new Date((schedule ? schedule.nextExecution || now : now).getTime() + 1000);
				logger.info(signature + `Setting next schedule date for Template[${template.id}] to ${schedule.processedTo}`);
				await schedule.save();

				resolve(template);
			});
		});

		const result = await Promise.all(templatePromises as Promise<Form | void>[]);
		logger.info(signature + `Done`);
		return result;
	}

	public async processRiskSchedules() {
		const minute = 1000 * 60;
		const hour = 60 * minute;
		const day = hour * 24;
		const sevenDaysForward = new Date(new Date().getTime() + (day * 7));

		const allFutureAssessments = await Risks.findAll({
			where: { dateOfNextAssessment: { [Op.lte]: sevenDaysForward } }
		});

		await Promise.all(allFutureAssessments.map(async riskNeedingAssessment => {
			await RiskController.generateNewAssessmentByRisk(riskNeedingAssessment);

			riskNeedingAssessment.dateOfNextAssessment = null;
			riskNeedingAssessment.save();
		}));
	}

	public async processDocumentReviewSchedules() {
		const signature = this.className + ".processDocumentReviewSchedules: ";

		const documentCollections = await DocumentCollection.findAll({ where: { isArchived: false } });
		logger.silly(signature + `Found Total[${documentCollections.length}] documentCollections for inspection`);

		let reviewSchedulesForDocumentCollections: {
			value: number,
			valueType: 'year' | 'month'
		}[] = [];
		documentCollections.forEach(collection => {
			if (collection.reviewSchedule === 'ANNUALLY') {
				reviewSchedulesForDocumentCollections[collection.id] = {
					value: 1,
					valueType: 'year'
				}
			} else if (collection.reviewSchedule === 'BIANNUALLY') {
				reviewSchedulesForDocumentCollections[collection.id] = {
					value: 6,
					valueType: 'month'
				}
			} else if (collection.reviewSchedule === 'BIENNIALLY') {
				reviewSchedulesForDocumentCollections[collection.id] = {
					value: 2,
					valueType: 'year'
				}
			} else {
				throw new Error(`Unknown Review Schedule ${collection.reviewSchedule}`);
			}
		});

		const documents = await Document.findAll({
			where: {
				documentCollectionId: { [Op.in]: documentCollections.map(collection => collection.id) },
				isArchived: false
			}
		});

		logger.silly(signature + `Found Total[${documents.length}] Documents within Total[${documentCollections.length}] documentCollections for inspection`);

		if (documents.length === 0) {
			logger.info(signature + `Found Total[${documents.length}] Documents within Total[${documentCollections.length}] documentCollections for inspection. Process will end`);
			return;
		}

		// Filter out future schedules
		let now = moment().tz(timeZone);
		let documentsToSchedule = documents.filter(document => {
			let nextReviewDate = moment(document.nextReviewAt).tz(timeZone);
			if (document.nextReviewAt && nextReviewDate.isBefore(now)) {
				logger.silly(signature + `NextReviewAt[${nextReviewDate}] is Before[${now}] for Document[${document.id}]`);
				return true;
			}
			return false;
		});

		logger.info(signature + `Found Total[${documentsToSchedule.length}] Documents for review`);

		// Filter out documents with reviews that are still open
		for (let index = 0; index < documentsToSchedule.length; index++) {
			const formsAttachedToDocument = await DocumentDRMService.getFormsAttached(documentsToSchedule[index].id);

			if (formsAttachedToDocument.length === 0) {
				logger.debug(signature + `Document[${documentsToSchedule[index].id}] has no attached forms`);
				continue;
			}

			const openReviews = formsAttachedToDocument.filter( form => !form.archivedAt && !form.deletedAt);

			if (openReviews.length) {
				logger.debug(signature + `Document[${documentsToSchedule[index].id}] will not be reviewed as it has an open review`);
				documentsToSchedule.splice(index, 1);
			}
		}

		let dueAt = moment().tz(timeZone).add(8, 'weeks').set({
			'hour': 0,
			'minute': 0,
			'second': 0
		}).toISOString(false);

		let alertAt = moment().tz(timeZone).add(5, 'weeks').set({
			'hour': 0,
			'minute': 0,
			'second': 0
		}).toISOString(false);

		for (let index = 0; index < documentsToSchedule.length; index++) {
			logger.silly(signature + `Generating form for Document[${documentsToSchedule[index].id}]`);
			let form = await this.createForm({
				categoryId: 4,
				formLocationId: null,
				userGroupId: documentsToSchedule[index].departmentId,
				dueAt,
				alertAt,
				stage: 1,
				parentFormId: null,
				assignedUserId: null,
				notifyOnComplete: null,
				summary: documentsToSchedule[index].documentCode + " - " + documentsToSchedule[index].name
			}, null);
			logger.debug(signature + `Generated form for Document[${documentsToSchedule[index].id}]`);


			logger.silly(signature + `Generating initial record for Review Form[${form.id}] of Document[${documentsToSchedule[index].id}]`);
			let properties: Partial<IFormRecordPropertyParam>[] = [];
			let formId: number = form.id;

			properties.push({
				name: "summary",
				stringData: "This review task was created automatically at the scheduled review date."
			});

			properties.push({
				name: "documentId",
				stringData: documentsToSchedule[index].id.toString()
			});

			properties.push({
				name: "userGroupId",
				intData: documentsToSchedule[index].departmentId
			});

			let formDocument: Partial<FormDocument>;

			const latestAprovedVersionOfDocument: any = await DocumentDRMService.getLatestAprovedVersion(documentsToSchedule[index].id);
			if (!!latestAprovedVersionOfDocument) {
				let uploadPath = process.env.UPLOADS || './uploads'
				let fileName = latestAprovedVersionOfDocument.originalPath.split('/')[(latestAprovedVersionOfDocument.originalPath.split('/')).length - 1];

				logger.debug(signature + `Document[${documentsToSchedule[index].id}] will reference approvedDocument[${fileName}] in inital record of Review Form[${form.id}]`);

				uploadPath = path.isAbsolute(uploadPath) ? uploadPath : path.resolve(process.env.PWD as string, uploadPath);
				uploadPath = uploadPath + '/' + fileName + '_draft' + moment().unix().toString();

				if (fs.existsSync(latestAprovedVersionOfDocument.originalPath)) {
					fs.copyFileSync(latestAprovedVersionOfDocument.originalPath, uploadPath);
					let stats = fs.statSync(uploadPath);

					formDocument = await FormDocument.create({
						fileName: latestAprovedVersionOfDocument.originalFileName,
						mimeType: latestAprovedVersionOfDocument.originalMimeType,
						path: uploadPath,
						size: stats["size"],
						userId: null
					},
						{ fields: ['fileName', 'mimeType', 'path', 'size', 'userId'] }
					);

					await RecordService.createRecord({
						formId: formId,
						properties: properties as any,
						stage: 1,
						documents: [{ id: formDocument.id, isTicked: false }],
						isComplete: false,
					}, 1);
				} else {
					logger.debug(signature + `Document[${documentsToSchedule[index].id}] could not be found at Path[${latestAprovedVersionOfDocument.originalPath}] and will be ignored in the initial record of Review Form[${form.id}]`);
					await RecordService.createRecord({
						formId: formId,
						properties: properties as any,
						stage: 1,
						documents: [],
						isComplete: false,
					}, null);
				}
			} else {
				logger.debug(signature + `Document[${documentsToSchedule[index].id}] does not have an approved document to reference in the initial record of Review Form[${form.id}]`);
				await RecordService.createRecord({
					formId: formId,
					properties: properties as any,
					stage: 1,
					documents: [],
					isComplete: false,
				}, null);
			}

			logger.debug(signature + `Generated initial record for Review Form[${form.id}] of Document[${documentsToSchedule[index].id}]`);
		}

		logger.debug(signature + `Finished updating review documents. Updating nextReviewDates`);

		// Update documents with new review dates.
		documents.forEach(document => {
			if (moment(document.nextReviewAt).tz(timeZone).isBefore(moment().tz(timeZone))) {
				let documentCopy = document;
				let nextReview = moment(document.nextReviewAt);
				nextReview.add(reviewSchedulesForDocumentCollections[document.documentCollectionId].value, reviewSchedulesForDocumentCollections[document.documentCollectionId].valueType)
				documentCopy.nextReviewAt = nextReview.toDate();
				Document.update({ name: documentCopy.name, documentCollectionId: documentCopy.documentCollectionId, type: documentCopy.type, nextReviewAt: documentCopy.nextReviewAt, departmentId: documentCopy.departmentId, standard: documentCopy.standard }, { where: { id: document.id } });
			}
		});

		logger.debug(signature + `Finished generating document reviews`);
	}

	/**
	 * @description Gets a form by its ID
	 * @param {number} id formId
	 */
	public async getFormById(id: number): Promise<Form | null> {
		const result = await Form.findById(id);

		return result;
	}
}

export const FormService = new FormServiceClass();
