import * as express from 'express';
import * as HttpErrors from '../../model/HttpErrors';
import * as t from "io-ts";
import logger from './Logger';
import { reporter } from 'io-ts-reporters';

export function responseWrap(handler: (req: any, res: any) => Promise<any>, codec?: t.Any) {
  return async function wrapRequestHandler(req: express.Request, res: express.Response) {
    try {
      const resp = await handler(req, res);

      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires',  '-1');

      // If the method returns a value but does not explicitly write output, write the response
      if (!res.finished) {
        if (resp) {
          // If a codec is present, filter the content accordingly
          if( codec ) {
            const outputValidation = codec.decode(JSON.parse(JSON.stringify(resp)));
            if( outputValidation.isRight() ) {
              res.json(outputValidation.value);
            } else {
              logger.error("Invalid Server Response");
              logger.error(JSON.stringify(resp, null, 2));
              const report = reporter(outputValidation);
              logger.error(JSON.stringify(report, null, 2));
              res.status(500).json({ message: 'Invalid Server Response' });
            }
          } else {
            res.json(resp);
          }
        } else {
          res.json({ message: 'No Data' });
        }
      }

      return resp;
    } catch (e) {
      console.log(e, e.stack);

      if (e instanceof HttpErrors.HttpError) {
        // Output the error
        const httpError: HttpErrors.HttpError = e;
        const className: string = httpError.constructor.name;
        const response: {
          message?: string;
          errors?: string[]
        } = { message: httpError.message };

        if( httpError.errors ) {
          response.errors = httpError.errors;
        }

        res.status(HttpErrors[className].errorCode).json(response);
        return;
      }

      res.status(500).json({ message: 'Internal Server Error' });
    }
  };
}
