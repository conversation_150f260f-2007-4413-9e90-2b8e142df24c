/*
  The form Records are a list of values submitted to a formRecord
 */

import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import FormRecord from './formRecord.model';
import Property from './property.model';
import FormEnum from './enum.model';

@Table({
  tableName: 'forms_record_property'
})
class FormRecordProperty extends Model<FormRecordProperty> {

  @ForeignKey(() => FormRecord)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  formRecordId: number;

  @BelongsTo(() => FormRecord, 'formRecordId')
  formRecord: number;

  @ForeignKey(() => Property)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  propertyId: number;

  @BelongsTo(() => Property, 'propertyId')
  property: Property;

  @Column({
    type: DataType.STRING({length: 16000}),
    allowNull: true,
  })
  stringData: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  intData: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  jsonData: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true
  })
  enumId: number;

  @Column({
    type: DataType.DATE,
    allowNull: true
  })
  dateData: Date;

  @BelongsTo(() => FormEnum, 'enumId')
  enum: FormEnum;
}

export default FormRecordProperty;

