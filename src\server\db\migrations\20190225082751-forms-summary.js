'use strict';

module.exports = {

  up: async (queryInterface, Sequelize) => {
    let tableDefinition = await queryInterface.describeTable('forms');

    if (!tableDefinition['summary']) {
      await queryInterface.addColumn('forms', 'summary', {
        type: Sequelize.STRING(15000),  // 16000 doesn't work in this table because of storage  storage overhead
        defaultValue: '',
        allowNull: true,
      });
    }

    await queryInterface.bulkUpdate('forms', {
      summary: Sequelize.literal(`(SELECT "stringData" FROM forms_record_property
          WHERE "propertyId" = (SELECT id FROM properties WHERE name = 'summary')
          AND "formRecordId" = (SELECT id FROM forms_record WHERE "formId" = forms.id
          AND sequence = (SELECT MAX(sequence) from forms_record WHERE "formId" = forms.id AND stage = 0))
        )`)
    }, {
    });

  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('forms', 'summary');
  }

};
