import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import {DocumentInstanceController} from '../controller/documentInstance.controller';
import { validateRouteParams } from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/document', adminMiddleware);

/**
 Record routes
 */
router.post('/document-instance', responseWrap(DocumentInstanceController.upload));
router.get('/document-instance/:id', validateRouteParams(GetByIdParams), DocumentInstanceController.download);
router.get('/document-instance', responseWrap(DocumentInstanceController.getAllDocumnets));
router.get('/document-instance/:id/draft',validateRouteParams(GetByIdParams), responseWrap(DocumentInstanceController.getDraft))
router.get('/document-instance/:id/downloadDraft',validateRouteParams(GetByIdParams), DocumentInstanceController.downloadDraft)
export default router;