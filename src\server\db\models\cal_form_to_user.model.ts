import { Table, Column, Model, ForeignKey } from 'sequelize-typescript';
import Form from './form.model';
import User from './user.model';


@Table({
  tableName: 'cal_form_to_user',
  timestamps: false,
})
class CALFormToUser extends Model<CALFormToUser> {

  @ForeignKey(() => User)
  @Column
  userId: number;

  @ForeignKey(() => Form)
  @Column
  formId: number;

}

export default CALFormToUser;


