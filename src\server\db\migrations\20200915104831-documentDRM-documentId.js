'use strict';

module.exports = {
	up: async (queryInterface, Sequelize) => {

		const tableDefinition = await queryInterface.describeTable('documents');

		if (tableDefinition['documentCode']) {
			return;
		}

		return queryInterface.addColumn(
			'documents',
			'documentCode',
			{
				type: Sequelize.STRING,
				allowNull: true,
			},
		);
	},

	down: (queryInterface, Sequelize) => {
		return queryInterface.removeColumn('documents', 'documentCode');
	}
};
