<div *ngIf="!showForm">
    Loading...
</div>
<form-actions [formData]="formData" showDuplicateButton="true"></form-actions>
<div class="row" *ngIf="showForm && formData && formData.parentFormId">
    <div class="col-12 text-center">
        <a [routerLink]="['/report', formData.ancestorFormId]" target="_blank">Parent Form</a>
    </div>
</div>

<div class="row" *ngIf="showForm && formData && formData.templateFormId && isAdmin">
    <div class="col-12 text-center">
        <a [routerLink]="['/report', formData.templateFormId]" target="_blank">Template</a>
    </div>
</div>

<follow-up-form-0
    [formData]="formData"
    *ngIf="showForm && targetStage === 0 && (!formData || !formData.isDeleted)"
    #followUpStage0>
</follow-up-form-0>
<follow-up-form-1
    [formData]="formData"
    *ngIf="showForm && targetStage === 1 && (!formData || !formData.isDeleted)"
    #followUpStage1>
</follow-up-form-1>
<follow-up-form-2
    [formData]="formData"
    *ngIf="showForm && targetStage === 2 && (!formData || !formData.isDeleted)"
    #followUpStage2>
</follow-up-form-2>
<!---
    The read only view of the form should be always visible throughout the process
--->
<div class="{{ targetStage === 3 ? '' : 'pt-2' }}">&nbsp;</div>
<follow-up-form-3 [formData]="formData" *ngIf="showForm && !!formData"></follow-up-form-3>

<div class="row" *ngIf="childForms">
    <div class="col-12">
        <h1 class="text-uppercase text-white bg-teal p-2 px-3 my-2">Child Forms</h1>
        <div class="pl-2">
            <child-forms-tree [childForms]="childForms"></child-forms-tree>
        </div>
    </div>
</div>

