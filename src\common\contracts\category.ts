import * as t from 'io-ts';
import { IntFromString } from 'io-ts-types/lib/IntFromString';

const CategoryOutputModel = t.interface({
  id: t.number,
  name: t.string,
});

export interface ICategoryOutputModel extends t.TypeOf<typeof CategoryOutputModel> {}

const NameType = t.refinement(
  t.refinement(t.string, s => (s.length > 0 && s.trim.length > 0), 'not empty name'),
  s => s.length < 128,
  'up to 128 characters',
);

export const CreateCategoryInputModel = t.interface({
  name: NameType,
});

export interface ICreateCategoryInputModel extends t.TypeOf<typeof CreateCategoryInputModel> {}

export const UpdateCategoryInputModel = t.interface({
  id: IntFromString,
  name: NameType,
});

export interface IUpdateCategoryInputModel extends t.TypeOf<typeof UpdateCategoryInputModel> {}
