import { Table, Column, Model, ForeignKey } from 'sequelize-typescript';
import FormDocument from './formDocument.model';
import FormRecord from './formRecord.model';


@Table({
  tableName: 'document_to_record',
  timestamps: false,
})
class DocumentToRecord extends Model<DocumentToRecord> {

  @ForeignKey(() => FormDocument)
  @Column
  documentId: number;

  @ForeignKey(() => FormRecord)
  @Column
  reportId: number;

}

export default DocumentToRecord;
