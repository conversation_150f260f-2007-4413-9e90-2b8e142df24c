import * as jwt from 'jwt-simple';
import { JWTPayload } from '../../model/Auth';
import * as argon2 from 'argon2';
import { ForbiddenError } from '../../model/HttpErrors';
import UserModel from '../db/models/user.model';
import { IAuthParams } from '../../common/contracts/auth';

export const authSecret = '2B5FFC1F55B24';

export namespace AuthService {
  /*
   * Retrieve the attempted auth user
   * Verify the password
   * Generate a JWT Response
   */
  export async function login(credentials: IAuthParams): Promise<JWTPayload> {

    const user = await UserModel.findOne({ where: { username: credentials.username, isArchived: false } });

    if (!user) {
      console.error('Could Not Find User[${credentials.username}]');
      throw new ForbiddenError(`Invalid Username or Password`);
    }

    console.info('Verifying Password');

    const correct = await argon2.verify(user.password, credentials.password);

    if (correct) {
      console.info('Successfully Authenticated');

      return issueJwtPayload(user.id, user.username, user.role);

    } else {
      console.error('Invalid Password');
      throw new ForbiddenError('Invalid Username or Password');
    }

  }

  export async function emulateUser(userId: number): Promise<JWTPayload> {
    const user = await UserModel.findOne({ where: { id: userId, isArchived: false } });

    if (!user) {
      console.error('Could Not Find User[${credentials.username}]');
      throw new ForbiddenError(`Invalid Username or Password`);
    }

    return issueJwtPayload(user.id, user.username, user.role);
  }

  export function issueJwtPayload(userId: number, username: string, userRole: string): JWTPayload {
    return ({
      accessToken: jwt.encode({
        user: {
          id: userId,
          username: username,
          role: userRole,
        }
      }, authSecret),
      tokenType: 'Bearer'
    });
  }
}
