import { BadRequestError, NotFoundError } from '../../model/HttpErrors';
import { omit } from 'lodash';
// import FormLocation from "../db/models/formLocation.model";
import FormEnum from '../db/models/enum.model';
import { IFindOptions } from 'sequelize-typescript/lib/interfaces/IFindOptions';
import { Sequelize, } from 'sequelize-typescript';
import { IEnumSetCreateModel, IEnumSetUpdateModel, IEnumsOutputModel } from '../../common/contracts/enums';
import ResourcePack from '../db/models/resource-pack.model';
import ResourcePackDocument from '../db/models/resource-pack-documents.model';
import FormDocument from '../db/models/formDocument.model';

export class EnumController {

  public static async getEnums(req: JwtRequest) {

    const findOpts: IFindOptions<FormEnum> = {
      limit: 20,
      offset: 0,
      attributes: ['name', [Sequelize.fn('COUNT', Sequelize.col('name')), 'itemsCount']],
      where: {
        isArchived: false,
      },
      group: ['name'],
      order: [
        ['name','ASC']
      ]
    };

    const enums: FormEnum[] | null = await FormEnum.findAll<FormEnum>(findOpts);

    return enums;
  }

  public static async getEnumsByName(req: JwtRequest): Promise<IEnumsOutputModel[]> {
    const enumName: string = req.params.enumName;

    const findOpts = {
      attributes: {
        exclude: ['isArchived', 'order']
      },
      where: {
        isArchived: false,
        name: enumName,
      },
      order: [
        // ['order', 'ASC']
        ['value', 'ASC']
      ],
      include: [{
        model: ResourcePack, include: [
          { model: ResourcePackDocument, include: [ FormDocument ] }
        ]
      }]
    };

    const enums: FormEnum[] = await FormEnum.findAll<FormEnum>(findOpts);

    if (!enums || enums.length === 0) {
      throw new NotFoundError('Enum set with this name doesn\'t exist');
    }

    return enums.map(el => el.toJSON());
  }

  public static async createEnumSet(req: JwtRequest) {

    const payload = req.body as IEnumSetCreateModel;

    const existCount = await FormEnum.count({
      where: {
        isArchived: false,
        name: payload.name,
      }
    });

    if (existCount > 0) {
      throw new BadRequestError('Enum set with this name already exists');
    }

    const records = payload.values.map((value, index) => ({
      name: payload.name,
      value: value.value,
      resourcePackId: value.resourcePack ? Number(value.resourcePack) : undefined,
      order: index,
      formLocationId: null,
    }));

    const bulkResults = await FormEnum.bulkCreate(records);

    return { status: 'ok', insertedRecords: bulkResults.length };

  }

  public static async updateEnumSet(req: JwtRequest) {

    const payload = req.body as IEnumSetUpdateModel;

    const existingSet = await FormEnum.findAll({
      where: {
        isArchived: false,
        name: payload.name,
      },
      order: [['order', 'ASC']],
    });

    if (!existingSet || existingSet.length === 0) {
      throw new NotFoundError('Enum set with this name doesn\'t exist');
    }

    const orderedEntries = payload.values.map((entry, index) => ({
      id: entry.id,
      name: payload.name,
      value: entry.value,
      order: index,
      resourcePackId: entry.resourcePack ? Number(entry.resourcePack) : undefined,
      formLocationId: null,
    }));

    const newRecords = orderedEntries
      .filter(entry => entry.id === 'new')
      .map(entry => omit(entry, ['id']));

    const idsToRemove: number[] = existingSet
      .filter(exEntry => !orderedEntries.some(val => val.id === exEntry.id))
      .map(entry => entry.id);

    const bulkInserts = await FormEnum.bulkCreate(newRecords);

    /**
     * archive removed enum entries
     */
    const [recordsArchived] = await FormEnum.update({
      isArchived: true,
    }, {
      where: {
        id: {
          in: idsToRemove,
        }
      }
    });

    let updatesCount = 0;

    for (const entry of orderedEntries) {
      if (entry.id !== 'new') {
        const existingEntry = existingSet.find(exEntry => exEntry.id === entry.id);
        if (!existingEntry) {
          throw new BadRequestError('Invalid enum id provided for enums set update');
        } else {
          if (
            existingEntry.value !== entry.value ||
            existingEntry.order !== entry.order ||
            existingEntry.formLocationId !== entry.formLocationId ||
            existingEntry.resourcePackId !== entry.resourcePackId
          ) {
            await FormEnum.update(omit(entry, ['id']), {
              where: {
                id: entry.id,
              }
            });
            updatesCount++;
          }
        }
      }
    }

    return {
      status: 'ok',
      inserted: bulkInserts.length,
      removed: recordsArchived,
      updated: updatesCount,
    };

  }
}
