import { NotFoundError, ServerError } from '../../model/HttpErrors';
import FormCategory from '../db/models/category.model';
import { ICreateCategoryInputModel } from '../../common/contracts/category';
import { IUpdateLocationInputModel } from '../../common/contracts/location';

export class CategoryController {

  public static async getCategories(req: JwtRequest) {

    const categories: FormCategory[] | null = await FormCategory.findAll<FormCategory>({
      limit: 100, // pagination is not supported here
      offset: 0,
      order: [['name', 'ASC']],
    });

    return categories;
  }

  public static async createCategory(req: JwtRequest) {

    const { name } = req.body as ICreateCategoryInputModel;

    try {
      return await FormCategory.create({name}, {fields: ['name']});
    } catch (e) {
      console.error('Exception in CategoryController.createCategory', e);
      throw new ServerError();
    }

  }

  public static async updateCategory(req: JwtRequest) {

    const { id, name } = req.body as IUpdateLocationInputModel;

    const group: FormCategory | null = await FormCategory.findByPrimary<FormCategory>(id);

    if (!group) {
      throw new NotFoundError('Group is not found');
    }

    try {
      const [ recordsUpdated ] = await FormCategory.update({name}, {where: {id: id}});
      return {
        status: 'ok',
        recordsUpdated
      };
    } catch (e) {
      console.error('Exception in CategoryController.updateCategory', e);
      throw new ServerError();
    }

  }
}
