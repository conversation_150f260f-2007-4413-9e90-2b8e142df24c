import { Table, Column, Model, DataType, Sequelize, Unique, <PERSON><PERSON>ey, HasMany, BelongsTo } from 'sequelize-typescript';
import DocumentCollection from './documentCollection.model';
import UserGroup from './group.model';
import DocumentTag from './documentTag.model';
import DocumentMeta from './documentMeta.model';

export enum DocumentType {
  POLICY = 'POLICY',
  PROCEDURE = 'PROCEDURE',
  FORM = 'FORM',
  AUDIT = 'AUDIT'
};

@Table({
  tableName: 'documents'
})
class Document extends Model<Document> {

  @Unique
  @Column
  name: string;

  @Column({
    type: DataType.ENUM('POLICY', 'PROCEDURE', 'FORM', 'AUDIT'),
  })
  type: DocumentType;

  @Column({
    type: DataType.DATE(3),
    allowNull: true
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true
  })
  nextReviewAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)'),
    onUpdate: 'CURRENT_TIMESTAMP(3)'
  })
  updatedAt: Date;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isArchived: boolean;

  @ForeignKey(() => DocumentCollection)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  documentCollectionId: number;

  @BelongsTo(() => DocumentCollection)
  documentCollection: DocumentCollection;

  @ForeignKey(() => UserGroup)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  departmentId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  standard: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  documentCode: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  documentIdentity: string;

  @HasMany(() => DocumentTag)
  documentTag: DocumentTag;

  @HasMany(() => DocumentMeta)
  documentMeta: DocumentMeta;

}

export default Document;


