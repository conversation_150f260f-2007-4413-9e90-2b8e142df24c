import * as t from 'io-ts';
import {constructType} from './extend';
import { DateType } from './date';
import { DocumentOutputModel } from './documentDRM'
import { DateFromQueryParam, SortOrderValues } from './common';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { FormsSortedField } from './form';

export const DocumentInstanceType = constructType({
  id: t.number,
  documentId: t.number,
  path: t.string,
  version: t.number,
  createdAt: DateType,
  aprovedAt: DateType,
  aprovedBy:t.string
});
export const DocumentInstanceOutputType = constructType({
  id: t.number,
  documentId: t.number,
  path: t.string,
  version: t.number,
  createdAt: DateType,
  aprovedAt: DateType,
  aprovedBy:t.string,
  document: DocumentOutputModel,
});

export const DocumentInstanceQueryParams = constructType({
  skip: t.union([IntFromString, t.number]),
  limit: t.union([IntFromString, t.number]),
  sortBy: FormsSortedField,
  order: SortOrderValues,
}, {

  lastUpdatedAtStart: DateFromQueryParam,
  lastUpdatedAtEnd: DateFromQueryParam,
  search: t.string,
  category: t.union([IntFromString, t.number]),

  locationId: t.string,
  groupId: t.string,
  documentId: t.string,
  standard: t.string,
  tags: t.string,
  metaTags: t.string,
  metaTagValue: t.string,
  includeArchived: t.boolean
});

export interface IDocumentInstanceType extends t.TypeOf<typeof DocumentInstanceType> {}
export interface IDocumentInstanceOutputType extends t.TypeOf<typeof DocumentInstanceOutputType> {}
export interface IDocumentInstanceParams extends t.TypeOf<typeof DocumentInstanceQueryParams> {}

export function constructDocumentInstanceType<T>(dateType: t.Type<T>) {
  return t.type({
    id: t.number,
    documentId: t.number,
    path: t.string,
    version:t.number,
    createdAt: DateType,
    aprovedAt: DateType,
    aprovedBy:t.string
  });
}
