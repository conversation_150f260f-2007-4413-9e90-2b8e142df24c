version: 2
jobs:
  deploy-dev:
    working_directory: ~/app
    docker:
      - image: circleci/node:dubnium
    steps:
      - checkout
      - run:
          name: Check Node Version
          command: node --version
      - restore_cache:
          keys:
          - dependency-cache-{{ checksum "package.json" }}
          # fallback to using the latest cache if no exact match is found
          - dependency-cache
      - run:
          name: Clear Locks
          command: rm -f package-lock.json
      - run:
          name: Install npm
          command: npm install
      - save_cache:
          key: dependency-cache-{{ checksum "package.json" }}
          paths:
            - node_modules
      - run:
          name: Slap Wrist
          command: echo Perform Some Tests!
      - run:
          name: Clear Build Directory (If Exists)
          command: rm -rf ./build
      - run:
          name: Fix Type Errors
          command: npm i @types/lodash@ts2.5
      - run:
          name: Build Web-App
          command: BUILD_NUMBER=$CIRCLE_BUILD_NUM npm run ngBuildProd
      - run:
          name: Build Server
          command: npm run tscBuild
      - run:
          name: Zip the App
          command: zip -r quality.zip ./build
      - run:
          name: Copy the Web-App to the Server
          command: scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null quality.zip www-user@************:/var/www/zip
      - run:
          name: Remove the build file
          command: rm quality.zip
      - run:
          name: Build and Deploy
          command: ./scripts/ec2-remote-deploy.sh ************ *****************:sparkbusinesstechnology/bethany-aged-care.git master development
          no_output_timeout: 30m

  deploy-prod:
    working_directory: ~/lambda
    docker:
      - image: circleci/node:dubnium
    steps:
    - checkout
    - run:
        name: Check NPM Version
        command: npm -v
    - run:
        name: Check Node Version
        command: node --version
    - restore_cache:
        keys:
        - dependency-cache-{{ checksum "package.json" }}
        # fallback to using the latest cache if no exact match is found
        - dependency-cache-
    - run:
        name: Clear Locks
        command: rm -f package-lock.json
    - run:
        name: Install npm
        command: npm install && npm update
    - run:
        name: Check TSC Version
        command: npx tsc -v
    - save_cache:
        key: dependency-cache-{{ checksum "package.json" }}
        paths:
        - node_modules
    - run:
        name: Slap Wrist
        command: echo Perform Some Tests!
    - run:
        name: Clear Build Directory (If Exists)
        command: rm -rf ./build
    - run:
        name: Build Web-App
        command: npm run ngBuildProd
    - run:
        name: Zip the Web-App
        command: zip -r app.zip ./build/app
    - run:
        name: Copy the Web-App to the Server
        command: scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -P 10022 app.zip <EMAIL>:/var/www/bethany/zip
    - run:
        name: Build Server
        command: npm run build
    - run:
        name: Install pm2
        command: sudo npm install pm2 -g
    - run:
        name: Deploy
        command: pm2 deploy ecosystem.config.js production update --force
        no_output_timeout: 30m
workflows:
  version: 2
  build-and-deploy:
    jobs:
      - deploy-dev:
          filters:
            branches:
              only:
                - master
      - deploy-prod:
          filters:
            branches:
              only:
                - production