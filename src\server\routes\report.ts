import * as express from 'express';

import { validateQueryParams, validateRouteParams } from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';
import { ReportController } from '../controller/report.controller';
import { preFillDefaultQueryParams } from '../middleware/preFillMiddleware';
import { FormsQueryParams } from '../../common/contracts/form';

const router = express.Router();

/**
 * Allow access to admins only
 */

/**
 Record routes
 */

router.get('/report/dashboardCsv',
  preFillDefaultQueryParams({ skip: '0', limit: '1000000', sortBy: 'dueAt', order: 'asc' }),
  validateQueryParams(FormsQueryParams),
  ReportController.exportFormsToCsv);

router.get('/report/drmDashboardCsv',
  preFillDefaultQueryParams({ skip: '0', limit: '1000000', sortBy: 'dueAt', order: 'asc' }),
  validateQueryParams(FormsQueryParams),
  ReportController.exportDRMFormsToCsv);

router.get("/report/downloadRiskCSV", ReportController.exportRiskFormsToCSV)

router.get('/report/:id', validateRouteParams(GetByIdParams), ReportController.createReport);

export default router;
