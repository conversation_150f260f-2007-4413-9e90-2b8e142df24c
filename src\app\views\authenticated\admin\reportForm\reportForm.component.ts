import { Component, OnInit, ViewChild } from '@angular/core';

import { FormService } from "../../../../service/FormService";
import { ActivatedRoute, Params, Router } from "@angular/router";
import { Session } from "../../../../service/util/Session";
import has = Reflect.has;
import { ChildFormTreeNode, IFormOutputModel } from "../../../../../common/contracts/form";
import { switchMap } from "rxjs/operators";
import { of } from "rxjs";
import { ComponentCanDeactivate } from "../../../shared/canDeactivate/ComponentCanDeactivate";
import { ReportFormStage0Component } from "./stage0/reportFormStage0.component";
import { ReportFormStage1Component } from "./stage1/reportFormStage1.component";
import { ReportFormStage2Component } from "./stage2/reportFormStage2.component";
import { ReportFormStage3Component } from "./stage3/reportFormStage3.component";
import { ErrorHandlerService } from "../../../../service/ErrorHandlerService";


@Component({
	selector: 'app-admin',
	styleUrls: ['./reportForm.component.scss'],
	templateUrl: './reportForm.component.html',
})
export class ReportFormComponent extends ComponentCanDeactivate implements OnInit {

	public targetStage: number;
	public showForm: boolean = false;

	public formData: IFormOutputModel | null = null;
	public childForms: ChildFormTreeNode[] | null;

	@ViewChild('reportStage0') reportStage0Form: ReportFormStage0Component;
	@ViewChild('reportStage1') reportStage1Form: ReportFormStage1Component;
	@ViewChild('reportStage2') reportStage2Form: ReportFormStage2Component;
	@ViewChild('reportStage3') reportStage3Form: ReportFormStage3Component;

	constructor(
		public formService: FormService,
		public activatedRoute: ActivatedRoute,
		public session: Session,
		private router: Router,
		private errorHandler: ErrorHandlerService,
	) {
		super();
	}

	canDeactivate(): boolean {
		if (!this.showForm) return true;

		if (this.formData && this.formData.isDeleted) {
			return true;
		}

		if (this.targetStage === 0)
			return this.reportStage0Form.canDeactivate();

		if (this.targetStage === 1)
			return this.reportStage1Form.canDeactivate();

		if (this.targetStage === 2)
			return this.reportStage2Form.canDeactivate();

		if (this.targetStage === 3)
			return this.reportStage3Form.canDeactivate();

		return true;
	}

	ngOnInit() {
		this.session.lockInputRx(
			this.activatedRoute.params.pipe(switchMap((params: Params) => {
				if (!has(params, "reportId")) {
					this.showForm = true;
					this.targetStage = 0;
					return of(null);
				} else {
					return this.formService.getFormById(params["reportId"]);
				}
			}))
		)


			.subscribe((existingForm: IFormOutputModel | null) => {

				if (!existingForm) {
					return;
				}

				if (existingForm.category.name === 'Risk Task') {
					this.router.navigate(['/risk-assessment-task', existingForm.id], { replaceUrl: true });
					return;
				}

				/**
				 *  fix for 'Parent Form' link, if form's category is 'Follow-Up' redirect to /followUps/:reportId
				 **/
				if (existingForm.category.name === 'Follow-Up') {
					this.router.navigate(['/followUp', existingForm.id], { replaceUrl: true });
					return;
				}

				/**
				 *  fix for 'Parent Form' link, if form's category is 'Report' redirect to /report/:reportId
				 **/
				if (existingForm.category.name === 'Audit') {
					if (existingForm.isTemplate) {
						this.router.navigate(['/admin/templates/audit', existingForm.id], { replaceUrl: true });
						return;
					}
					this.router.navigate(['/audit', existingForm.id], { replaceUrl: true });
					return;
				}

				/**
				 *  fix for 'Parent Form' link, if form's category is 'DRM-Task' redirect to /followUps/:reportId
				 **/
				if (existingForm.category.name === 'DRM-Task') {
					this.router.navigate(['/document-review-task', existingForm.id], { replaceUrl: true });
					return;
				}

				this.formData = existingForm;

				if (this.formData.records.length === 0) {
					this.errorHandler.raiseError("Record did not contain valid data. Please contact a system administrator for assistance.", "Invalid Form Data");
				}

				this.targetStage = this.formData["stage"] || 0;
				this.showForm = true;

				if (this.formData.childForms && this.formData.childForms.length) {
					this.childForms = this.formData.childForms;
				}

			}, (err) => {
				console.error("Cannot get form by id", err);
				this.errorHandler.handleHttpError(err);
			});
	}
}
