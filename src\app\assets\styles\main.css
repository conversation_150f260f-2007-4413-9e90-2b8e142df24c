/*
@import url('https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700,900');
@import url('https://fonts.googleapis.com/css?family=Lato');
 */

@import url('main/transparency.css');

/* These are CSS Properties that apply to ALL pages */

/* Generic Page Structure */
body, html {
    height: 100%;
    width: 100%;
    overflow: hidden;

    color: #333;
    font-family: 'Roboto', 'Lato', sans-serif;
}

html { height: 100%; overflow:auto; }
body {
    height: 100%
}

div.splash {
    min-width: 320px;
    min-height: 320px;
}

a.btn {
    text-decoration: none;
}

/* Override of default BG Colours */
*, body {
    background-color: transparent;
}

input:focus {
    box-shadow: 0 0 5px #82BEFD !important;
}

input:focus.border-success {
    box-shadow: 0 0 5px #30A64A !important;
}

input:focus.border-danger {
    box-shadow: 0 0 5px #DA3849 !important;
}

/* Main Classes */
h1 {
    font-size: 2.8rem;
}

h1.text-xl {
    font-size: 3rem;
}

h1.text-xxl {
    font-size: 4rem;
}

/* Large Background Image */
div.splash {
    z-index: -1;

    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    min-width: 100%;
    min-height: 100%;

    /*background: url(../images/sydecoe.jpg) no-repeat center center fixed;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover;*/

    background: #f1f1f1;
}

/* Generic Helper Classes */
.overflow-hidden {
    overflow: hidden;
}

.border-grey {
    border-color: #777 !important;
}

.border-light-grey {
    border-color: #aaa !important;
}

.header {
    z-index: 1050;
}

.item-content {
    text-decoration: none !important;
}

/* Generic - Bootstrap Extensions */
.border.border-dashed {
    border-style: dashed !important;
}

.border-left.border-dashed {
    border-left-style: dashed !important;
}

.border-right.border-dashed {
    border-right-style: dashed !important;
}

.border-top.border-dashed {
    border-top-style: dashed !important;
}

.border-bottom.border-dashed {
    border-bottom-style: dashed !important;
}

@media (min-width: 576px) {
    /*sm*/
}
@media (min-width: 768px) {
    /*MD*/
}
@media (min-width: 992px) {
    .border-lg {
        border: 1px solid #dee2e6 !important;
    }
    .border-lg-right {
        border-right: 1px solid #dee2e6 !important;
    }
    .border-lg-right.border-dashed {
        border-right-style: dashed !important;
    }
}
@media (min-width: 1200px) {
    /*xl*/
}
@media (min-width: 1900px) {
    /*xxl*/
}

@media (min-width: 3000px) {
    /*xxxl*/
}

.bg-default {
    background-color: #E7E7E7 !important;
}

.bg-teal {
    background-color: #19a0b7 !important;
}

.bg-grey {
    background-color: #eee !important;
}

a.bg-default:hover, a.bg-default:focus,
button.bg-default:hover,
button.bg-default:focus {
    background-color: #B3B4B5 !important;
}

/* Font */

.font-weight-heavy {
    font-weight: 500!important;
}

.text-orange {
    color: #ed7d31!important;
}

.btn-orange {
    color: #ffffff;
    background-color: #ed7d31;
    border-color: #E57B42;
}

.btn-orange:hover {
    color: #ffffff;
    background-color: #FF8840;
    border-color: #FF8840;
}

.btn-orange:focus, .btn-orange.focus {
    box-shadow: 0 0 0 0.2rem rgba(217, 115, 54, 0.5);
}

.btn-orange.disabled, .btn-orange:disabled {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-orange:not(:disabled):not(.disabled):active, .btn-orange:not(:disabled):not(.disabled).active,
.show > .btn-orange.dropdown-toggle {
    color: #ffffff;
    background-color: #D97336;
    border-color: #FF8840;
}

.btn-warning:not(:disabled):not(.disabled):active:focus, .btn-warning:not(:disabled):not(.disabled).active:focus,
.show > .btn-warning.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.hover-cursor:hover {
    cursor: pointer;
    cursor: hand;
}

/* Select2 Plugin */

.select2-form-control {
    padding: 0.15rem 0 !important;
}

.select2-form-control .select2-container {
    width: 100% !important;
    border: 0 !important;
}

.select2-form-control .select2-selection {
    border: 0 none;
}

/* Border-Width - Based on the mw- static output in bootstrap 4 */
.line {
    content: '';
    padding:0!important;
    height: 0px!important;
}

.bw-1 {
    border-width: 0.025rem !important;
}

.bw-2 {
    border-width: 0.05rem !important;
}

.bw-3 {
    border-width: 0.1rem !important;
}

.bw-4 {
    border-width: 0.15rem !important;
}

.bw-5 {
    border-width: 03rem !important;
}

.line.bw-1 {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-width: 0.05rem !important;
}

.line.bw-2 {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-width: 0.1rem !important;
}

.line.bw-3 {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-width: 0.2rem !important;
}

.line.bw-4 {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-width: 0.3rem !important;
}

.line.bw-5 {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-width: 0.6rem !important;
}

/* Cross Form Css */
.logoImg {
    max-width: 250px;
    max-height: 100%;
}

/* Recolouring Btn-Secondary */
.btn-secondary {
    color: #fff;
    background-color: #818a91;
    border-color: #737d85;
}

.btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}

.btn-secondary:focus, .btn-secondary.focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-secondary.disabled, .btn-secondary:disabled {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active,
.show > .btn-secondary.dropdown-toggle {
    color: #fff;
    background-color: #0A549A;
    border-color: #4e555b;
}

.btn-secondary:disabled:active, .btn-secondary:disabled.active,
.btn-secondary.disabled:active, .btn-secondary.disabled.active {
    color: #fff;
    background-color: #073866;
    border-color: #4e555b;
 }
.btn-secondary:not(:disabled):not(.disabled):active:focus, .btn-secondary:not(:disabled):not(.disabled).active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}
