// This file can be replaced during build by using the `fileReplacements` array.
// `ng build --prod` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

// Argon2 Password:
// <EMAIL>

// Hashed:
// $argon2d$v=19$m=4096,t=3,p=1$E3NYtOcgR7GGlyfwz3/1OSqeu0Vb0oKymwF4w9pqJVM$H5EhoVR/r7PYDgV2EVpCIQ6oN+3LwkpA9zAG3M3VamU

export const environment = {
  production: false,
  apiPrefix: '',
  defaultAuthEmail : '<EMAIL>',
  defaultAuthPassword: 'password',
  environmentName: "local",
  logLevel: "silly",
  timeZone: "Australia/Brisbane"
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/dist/zone-error';  // Included with Angular CLI.
