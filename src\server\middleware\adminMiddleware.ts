import { Response, NextFunction } from 'express';
import { ForbiddenError } from '../../model/HttpErrors';

export const adminMiddleware = (req: JwtRequest, res: Response, next: NextFunction) => {
  if (req.tokenData && req.tokenData.user && req.tokenData.user.role === 'administrator' ) {
    next();
  } else {
    res.status(ForbiddenError.errorCode).json({
      error: 'Admin role is required to perform the action',
      code: ForbiddenError.errorCode,
    });
  }
};
