import UserGroup from '../db/models/group.model';
import { BadRequestError, NotFoundError, ServerError } from '../../model/HttpErrors';
import {
  ICreateUserGroupInputModel,
  IGroupsQueryParams,
  IUpdateUserGroupInputModel
} from '../../common/contracts/groups';
import { IGetByIdParams } from '../../common/contracts/common';
import { Sequelize } from 'sequelize-typescript';

export class GroupsController {

  public static async getGroups(req: JwtRequest & Query<IGroupsQueryParams>) {

    const queryParams = req.query as IGroupsQueryParams;

    const attributes: any[] = ['id', 'groupName', 'createdAt', 'updatedAt'];

    if (queryParams.countUsers) {
      /**
       * add subQuery to count users in locations
       */
      attributes.push([Sequelize.literal(
        `(SELECT COUNT(*)
          FROM user_to_group LEFT JOIN users ON users.id = user_to_group.userId
          WHERE user_to_group.groupId = UserGroup.id AND users.isArchived = 0)`
      ), 'usersCount']);
    }

    const {rows, count} = await UserGroup.findAndCountAll<UserGroup>({
      limit: queryParams.limit,
      offset: queryParams.skip,
      where: {
        isArchived: false,
      },
      attributes,
      order: [ [ Sequelize.literal(queryParams.sortBy) as any as string, queryParams.order.toUpperCase() ] ],
    });

    return {
      items: rows,
      totalCount: count,
    };
  }

  public static async getGroupById(req: JwtRequest) {

    const { id: groupId } = req.params as unknown as IGetByIdParams;

    const group = await UserGroup.findOne({
      where: {
        id: groupId,
        isArchived: false,
      },
      attributes: {
        exclude: ['isArchived'],
      }
    });

    if (!group) {
      throw new NotFoundError('Group is not found.');
    }

    return group;
  }

  public static async createGroup(req: any, res: any) {

    const { groupName } = req.body as ICreateUserGroupInputModel;

    try {

      return await UserGroup.create({groupName}, {fields: ['groupName']});

    } catch (e) {
      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('Group with this name already exists.');
      } else {
        console.log('createGroup exception', e);
        throw new ServerError();
      }
    }

  }

  public static async updateGroup(req: any, res: any) {

    const { id, groupName } = req.body as IUpdateUserGroupInputModel;

    const group: UserGroup | null = await UserGroup.findByPrimary<UserGroup>(id);

    if (!group || group.isArchived) {
      throw new NotFoundError('Group is not found');
    }

    try {
      const [ recordsUpdated ] = await UserGroup.update({groupName}, {where: {id: id}});
      return { status: 'ok', recordsUpdated };
    } catch (e) {
      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('Group with this name already exists.');
      } else {
        console.log('updateGroup exception', e);
        throw new ServerError();
      }
    }

  }

  public static async archiveGroup(req: JwtRequest) {

    const { id: groupId } = req.params as unknown as IGetByIdParams;

    const group: UserGroup | null = await UserGroup.findByPrimary(groupId);

    if (!group || group.isArchived) {
      throw new NotFoundError('Group is not found.');
    }

    const [ recordsArchived ] = await UserGroup.update({
      isArchived: true,
    }, {
      where: {id: groupId}
    });

    return {
      archived: recordsArchived,
    };

  }

}
