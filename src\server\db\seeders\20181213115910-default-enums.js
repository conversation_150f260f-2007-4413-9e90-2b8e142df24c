'use strict';

/* global queryInterface, queryInterface.bulkInsert, module, Sequelize */

module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.bulkInsert('enums', [{
      name: 'ReportFormType',
      value: 'Comment',
      order: 0
    },{
      name: 'ReportFormType',
      value: 'Complaint',
      order: 1
    },{
      name: 'ReportFormType',
      value: 'Dissatisfaction with response to previous Report Form',
      order: 2
    },{
      name: 'ReportFormType',
      value: 'Compliment',
      order: 3
    },{
      name: 'ReportFormType',
      value: 'Hazard',
      order: 4
    },{
      name: 'ReportFormType',
      value: 'Non-Conformance',
      order: 5
    },{
      name: 'ReportFormType',
      value: 'Proposal for Improvement',
      order: 6
    },{
      name: 'ReportFormType',
      value: 'Equipment/Resources Need',
      order: 7
    },{
      name: 'ReportFormType',
      value: 'Feedback',
      order: 8
    },{
      name: 'ReportFormType',
      value: 'Other',
      order: 9
    },{
      name: 'OriginatorType',
      value: 'Care Resident',
      order: 0
    },{
      name: 'OriginatorType',
      value: 'Family Member',
      order: 1
    },{
      name: 'OriginatorType',
      value: 'Staff Member',
      order: 2
    }], {});
  },
  
  down: (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('enums', null, {});
  }
};