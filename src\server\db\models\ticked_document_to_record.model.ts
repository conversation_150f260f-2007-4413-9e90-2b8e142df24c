import { Table, Column, Model, ForeignKey } from 'sequelize-typescript';
import FormDocument from './formDocument.model';
import FormRecord from './formRecord.model';


@Table({
  tableName: 'ticked_document_to_record',
  timestamps: false,
})
class TickedDocumentToRecord extends Model<TickedDocumentToRecord> {

  @ForeignKey(() => FormDocument)
  @Column
  documentId: number;

  @ForeignKey(() => FormRecord)
  @Column
  reportId: number;

}

export default TickedDocumentToRecord;
