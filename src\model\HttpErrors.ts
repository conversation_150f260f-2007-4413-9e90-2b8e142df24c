export class HttpError extends <PERSON>rror {
  static errorCode = 500;
  public errors: string[] | undefined = [];

  constructor(message?: string, errors?: string[]) {
    super(message);
    this.errors = errors;
  }
}

export class BadRequestError extends HttpError {
  static errorCode = 400;
}

export class UnauthorizedError extends HttpError {
  static errorCode = 401;
}

export class PaymentRequiredError extends HttpError {
  static errorCode = 402;
}

export class BadDataError extends HttpError {
	static errorCode = 422;
}

export class ForbiddenError extends HttpError {
  static errorCode = 403;
}

export class NotFoundError extends HttpError {
  static errorCode = 404;
}

export class NotAllowedError extends HttpError {
  static errorCode = 405;
}

export class NotAcceptableError extends HttpError {
  static errorCode = 406;
}

/*
 * This may be used when there is nothing wrong (no access issue, no auth issue, no server issue, the code is implemented)
 * however the state of something else, such as the config, prevents this request from proceeding. It may be resolved by
 * adjusting the state of the resource.
 */
export class ConflictError extends HttpError {
  static errorCode = 409;
}

export class ServerError extends HttpError {
  static errorCode = 500;
}
