set -o xtrace

#!/usr/bin/env bash
ROOT_DIR=/var/www
SRC_DIR=$ROOT_DIR/source

# Set the working directory
cd $SRC_DIR

# Remove the package.lock generated by other systems
rm package-lock.json

# Remove the Build Directory forcing complete rebuild
rm -rf ./build

# Install packages
npm install

# Perform the build
npm run dbMigrate
#npm run ngBuildStage

#Unzip the Angular App and remove the archive
unzip ../zip/app.zip -d ./
rm ../zip/app.zip

npm run tscBuild