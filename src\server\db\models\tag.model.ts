import { Table, Column, Model, DataType, Sequelize, Unique } from 'sequelize-typescript';

@Table({
  tableName: 'tags'
})
class Tag extends Model<Tag> {

  @Unique
  @Column
  name: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isMeta: boolean;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)'),
    onUpdate: 'CURRENT_TIMESTAMP(3)'
  })
  updatedAt: Date;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isArchived: boolean;

}

export default Tag;


