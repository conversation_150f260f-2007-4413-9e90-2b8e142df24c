import * as React from 'react';
import { StyleSheet, Text, View } from "@react-pdf/renderer";

const styles = StyleSheet.create({
  labelText: {
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
  },
  followUpsBlock: {
    flexDirection: 'col',
    padding: 10,
    borderWidth: 1,
    borderColor: '#999999',
    borderStyle: 'solid',
    marginTop: 8,
    marginBottom: 12,
  },
  followUpTitle: {
    fontSize: 14,
    marginBottom: 10,
  },
  followUpEntry: {
    flexDirection: 'row',
    marginTop: 4,
    marginBottom: 4,
    borderBottomWidth: 1,
    borderBottomColor: '#999999',
    borderBottomStyle: 'dashed',
  },
  followUpDepartment: {
    flexDirection: 'col',
    flex: 1,
  },
  followUpDescription: {
    flexDirection: 'col',
    flex: 2,
  },
  followUpDueDate: {
    flexDirection: 'col',
    flex: 1,
  },
  plainValue: {
    fontSize: 11,
  },
});

interface IFollowUpEntry {
  userGroupId: string,
  description: string,
  dueDate: string,
}

interface IFollowUpEntryWithName {
  userGroupName: string,
  description: string,
  dueDate: string,
}

const FolowUpEntry = ({entry}: {entry: IFollowUpEntryWithName}) => (
  <View style={styles.followUpEntry} wrap={false}>
    <View style={styles.followUpDepartment}>
      <Text style={styles.labelText}>Department:</Text>
      <Text style={styles.plainValue}>{entry.userGroupName}</Text>
    </View>
    {/*
    Removed description from follow ups as per client request
      <View style={styles.followUpDescription}>
        <Text style={styles.labelText}>Description:</Text>
        <Text style={styles.plainValue}>{entry.description}</Text>
      </View>
    */}
    <View style={styles.followUpDueDate}>
      <Text style={styles.labelText}>Due Date:</Text>
      <Text style={styles.plainValue}>{entry.dueDate}</Text>
    </View>
  </View>
);

const FollowUps = ({followUpsJson, followUpGroupMap} : {followUpsJson: string | null, followUpGroupMap: { [n: number]: string}}) => {

  let followUps: IFollowUpEntryWithName[] | null = null;

  if (followUpsJson) {
    const fups = JSON.parse(followUpsJson) as IFollowUpEntry[];
    if (!fups || !Array.isArray(fups)) {
      console.log('Invalid follow up json data');
      return <Text>Invalid Follow-Ups JSON</Text>;
    } else {
      followUps = fups.map(fu => ({
        userGroupName: followUpGroupMap[parseInt(fu.userGroupId, 10)],
        description: fu.description,
        dueDate: fu.dueDate,
      })).filter(fu => fu.dueDate || fu.description || fu.userGroupName);
    }
  }

  if (!followUps || followUps.length === 0) {
    return null;
  }

  return (
    <View style={styles.followUpsBlock}>
      <Text style={styles.followUpTitle}>Follow Up</Text>
      {followUps && followUps.map((fu, i) => <FolowUpEntry key={i} entry={fu}/>)}
    </View>
  );
};

export default FollowUps;
