module.exports = {
  apps : [{
    name: 'bethany-aged-care',
    // Cannot be set per env, something to be cautious of
    script: './current/build/index.js',

    // Options reference: https://pm2.io/doc/en/runtime/reference/ecosystem-file/
    args: 'one two',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production'
    },
    "log_date_format" : "YYYY-MM-DD HH:mm Z"
  }],
  deploy : {
    production : {
      user : 'www-user',
      host : 'vmh20816.hosting24.com.au',
      port: '10022',
      ref  : 'origin/production',
      repo : 'git@bitbucket:sparkbusinesstechnology/bethany-aged-care.git',
      // App will be build in ./source from this path
      path : '/var/www/bethany',
      "ssh_options": "StrictHostKeyChecking=no",
      'pre-setup' : '',
      'post-setup' : '',
      'pre-deploy' : 'cd /var/www/bethany/source && sh ./scripts/checkout.prod.sh && sh ./scripts/build.prod.sh',
      'post-deploy' : 'cd /var/www/bethany/source && sh ./scripts/deploy.prod.sh'
    }
  }
};
