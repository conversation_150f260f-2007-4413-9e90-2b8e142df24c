import { Response, NextFunction } from 'express';
import { ForbiddenError } from '../../model/HttpErrors';

export const adminManagerMiddleware = (req: JwtRequest, res: Response, next: NextFunction) => {
  if (req.tokenData && req.tokenData.user &&
    (req.tokenData.user.role === 'administrator' || req.tokenData.user.role === 'manager')) {
    next();
  } else {
    res.status(ForbiddenError.errorCode).json({
      error: 'Admin or manager role is required to perform the action',
      code: ForbiddenError.errorCode,
    });
  }
};