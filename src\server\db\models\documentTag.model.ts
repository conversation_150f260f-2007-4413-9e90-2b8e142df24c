import { Table, Column, Model, DataType, Sequelize, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import Document from './document.model';
import Tag from './tag.model';


@Table({
  tableName: 'document_tag'
})
class DocumentTag extends Model<DocumentTag> {

  @ForeignKey(() => Document)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  documentId: number;

  @BelongsTo(() => Document)
  document: Document;

  @ForeignKey(() => Tag)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  tagId: number;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)'),
    onUpdate: 'CURRENT_TIMESTAMP(3)'
  })
  updatedAt: Date;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    allowNull: false,
  })
  isArchived: boolean;
}

export default DocumentTag;


