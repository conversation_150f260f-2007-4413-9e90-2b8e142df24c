import * as t from 'io-ts';
import {ResourcePackOutputModel} from './resource-pack';

const EnumsOutputModel = t.exact(t.type({
  id: t.number,
  name: t.string,
  value: t.string,
  formLocationId: t.number,
  resourcePackId: t.union([t.number, t.null]),
  resourcePack: t.union([
    t.undefined,
    ResourcePackOutputModel
  ])
}));

export interface IEnumsOutputModel extends t.TypeOf<typeof EnumsOutputModel> {}

const EnumsListOutputModel = t.array(t.exact(t.type({
  name: t.string,
  itemsCount: t.number
})));

export type IEnumsListOutputModel = t.TypeOf<typeof EnumsListOutputModel>;

export const EnumSetCreateModel = t.exact(t.type({
  name: t.refinement(t.string, s => s.trim().length > 0, 'Enum name should not be empty'),
  values: t.refinement(t.array(t.exact(t.type({
    id: t.literal('new'),
    value: t.refinement(t.string,  s => s.trim().length > 0, 'Enum value should not be empty'),
    resourcePack: t.union([
      t.refinement(t.string, r => !isNaN(Number.parseInt(r)) && Number.parseInt(r) > 0, 'Resource pack should number and greater than 0'),
      t.null,
      t.undefined
    ])
  }))), arr => arr.length > 0, 'Should have at least one value'),

}));

export interface IEnumSetCreateModel  extends t.TypeOf<typeof EnumSetCreateModel> {}

export const EnumSetUpdateModel = t.exact(t.type({
  name: t.refinement(t.string, s => s.trim().length > 0, 'Enum name should not be empty'),
  values: t.array(t.type({
    id: t.union([t.literal('new'), t.number]),
    value: t.refinement(t.string,  s => s.trim().length > 0, 'Enum value should not be empty'),
    resourcePack: t.union([
      t.refinement(t.string, r => !isNaN(Number.parseInt(r)) && Number.parseInt(r) > 0, 'Resource pack should number and greater than 0'),
      t.null,
      t.undefined
    ])
  }))
}));

export interface IEnumSetUpdateModel  extends t.TypeOf<typeof EnumSetUpdateModel> {}
