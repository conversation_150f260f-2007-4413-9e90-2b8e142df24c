{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"BethanyAgedCareInterface": {"root": "src/app", "sourceRoot": "src/app", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"styleext": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "build/app", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/app/tsconfig.json", "assets": ["src/app/assets/favicon.ico", "src/app/assets"], "styles": ["node_modules/bootstrap-xxl/dist/css/bootstrap.min.css", "node_modules/bootstrap-xxl/dist/css/bootstrap-grid.min.css", "node_modules/font-awesome/css/font-awesome.min.css", "node_modules/select2/dist/css/select2.min.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/select2/dist/js/select2.full.min.js"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/app/environments/environment.ts", "with": "src/app/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": true, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "stage": {"fileReplacements": [{"replace": "src/app/environments/environment.ts", "with": "src/app/environments/environment.stage.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": true, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "BethanyAgedCareInterface:build"}, "configurations": {"production": {"browserTarget": "BethanyAgedCareInterface:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "BethanyAgedCareInterface:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/app/karma.conf.js", "styles": ["node_modules/bootstrap-xxl/dist/css/bootstrap.min.css", "node_modules/bootstrap-xxl/dist/css/bootstrap-grid.min.css", "node_modules/font-awesome/css/font-awesome.min.css", "node_modules/select2/dist/css/select2.min.css"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/select2/dist/js/select2.full.min.js"], "assets": ["src/favicon.ico", "src/assets"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "BethanyAgedCareInterface-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "BethanyAgedCareInterface:serve"}, "configurations": {"production": {"devServerTarget": "BethanyAgedCareInterface:serve:production"}}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "BethanyAgedCareInterface"}