import { Component, OnInit, Input, ViewChild, Output, EventEmitter } from '@angular/core';
import { Session } from "../../service/util/Session";
import { FormField } from "../../model/Form";
import { Select2Component } from 'ng2-select2';
import { ErrorHandlerService } from "../../service/ErrorHandlerService";
import { CurrentUserService } from '../../service/currentUser/CurrentUserService';
import { logger } from "../../service/util/Logger";
import { UsersService } from '../../service/admin/UsersService';

type ExIdTextPair = IdTextPair & { selected?: boolean };

@Component({
	selector: "user-select2",
	template: `<select2
    #select2Component
    class="form-control select2-form-control {{disabled?'bg-grey':''}} {{!disabled && field && field.iifIsValid('border-success','border-danger')}}"
    [data]="userSelectData"
    [options]="userSelectOptions"
    (valueChanged)="valueChanged($event.data)"
    [value]="defaultValue"
    [disabled]="disabled"
    ></select2>`
})
export class UserSelect2Component implements OnInit {

	private _value: string = '';

	@Input('value')
	get value() {
		return this._value;
	}

	/**
	 * By preventing assigning non-unique values during the set
	 * we can prevent a loop of observable subscribers
	 */
	set value(newValue: string) {
		this._value = newValue;

		if (!this.field && !this.multiple) {
			this.defaultValue = this._value;
		}
	}

	// Implies a read only state
	@Input()
	disabled: boolean = false;

	@Input()
	placeholder: string;

	@Input()
	multiple: boolean = false;

	public defaultValue: string = '';

	@Input()
	field: FormField<any>;

	public userSelectOptions: Select2Options;
	public userSelectData: ExIdTextPair[] = [];

	// Reference firstNameInput variable inside Component
	@ViewChild('select2Component') select2ComponentRef: Select2Component;

	@Output()
	change: EventEmitter<ExIdTextPair[]> = new EventEmitter<IdTextPair[]>();

	constructor(
		private usersService: UsersService,
		public session: Session,
		private errorHandler: ErrorHandlerService,
		public currentUserService: CurrentUserService
	) {
	}

	ngOnInit() {

		this.userSelectOptions = { allowClear: !this.multiple, placeholder: this.placeholder || "Select User", multiple: this.multiple};

		/*          
		this.usersService.getUsers({
            limit: params.data.limit,
            skip: params.data.skip,
            sortBy: 'name',
            order: 'asc',
            search: params.data.search || undefined
		})
		*/

		this.session.lockInputRx(
			this.usersService.getUsers({
				limit: 1000,
				skip: 0,
				sortBy: 'name',
				order: 'asc',
				search:''
			})
		)
			.subscribe( data => {
				let newSelectOptions: ExIdTextPair[] = [{ id: "", text: "" }];

				data.items.forEach( user => newSelectOptions.push({ id: String(user.id), text: `${user.firstName} ${user.lastName}` }));

				this.userSelectData = newSelectOptions;

				//Force the change detection to cycle again to prevent race
				if (this.field) {
					if (String(this.field.value) !== this.defaultValue)
						this.defaultValue = String(this.field.value);
				} else {
					//If the default value was manually set we need to re-trigger the process
					if (this._value !== '') {
						const valueArr: string[] | undefined = this.multiple && this._value && this._value.length ? this._value.split(",") : undefined;
						if (!this.multiple) { this.defaultValue = this.value; }
						const options = this.userSelectData.filter(o => (this.multiple ? (
							valueArr ? valueArr.find(selectedValue => selectedValue === o.id) : false
						) : o.id === this._value));
						if (options && options.length) {
							options.map(opt => opt.selected = true);
						}
					}
				}
			}, err => this.errorHandler.handleHttpError(err));
	}

	valueChanged(selectedOpts: ExIdTextPair[]) {
		if (selectedOpts.length === 0 || (
			selectedOpts.length === 1 && selectedOpts[0].id.length === 0 && selectedOpts[0].text.length === 0
		)) {
			if (this.field)
				this.field.value = null;
		}

		if (selectedOpts.length > 1 && !this.multiple)
			throw ("Selected options unexpectedly contained multiple results");

		if (selectedOpts.length === 1 && !this.multiple) {
			if (this.field) {
				this.field.value = selectedOpts[0].id;
			} else {
				if (this._value !== selectedOpts[0].id) { // if the value has been changed - emit event
					this._value = selectedOpts[0].id;
					this.change.emit(selectedOpts);
					return;
				}
			}
		}

		if (this.multiple) {
			const actualSelected = selectedOpts.filter(opt => opt.id && opt.id.length);
			const newValue = actualSelected.map(opt => opt.id).join(",");


			if (this.field) {
				console.log("Setting Field Value ", newValue);
				this.field.value = newValue;
			} else {
				logger.silly(`Checking internalValue change Value[(${typeof newValue}) ${newValue}] !== _value[(${typeof this._value}) ${this._value}]`);
				if (this._value !== newValue) { // if the value has been changed - emit event
					logger.silly("userGroupSelectComponent: Updating Internal Value");
					this._value = newValue;
					this.change.emit(actualSelected);
				}
			}
		}
	}
}
