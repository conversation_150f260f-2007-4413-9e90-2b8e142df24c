import * as express from 'express';
import { responseWrap } from '../utils/responseWrap';
import { validateBodyParams } from '../middleware/validationMidleware';
// import { adminMiddleware } from "../middleware/adminMiddleware";
import { SettingsController } from '../controller/settings.controller';
import { UpdateSettingsParams } from '../../common/contracts/settings';

const router = express.Router();

/**
 * get system settings
 */
// Admin middleware disabled ofr now given user needs to be able to retrieve "isEmailEnabled"
router.get('/settings'/*, adminMiddleware*/, responseWrap(SettingsController.getSettings));

/**
 * update system settings
 */
router.put('/settings', validateBodyParams(UpdateSettingsParams), responseWrap(SettingsController.updateSettings));


export default router;
