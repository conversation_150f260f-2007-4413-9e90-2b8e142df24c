'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    const tableDefinition = await queryInterface.describeTable('forms_record');
    if (tableDefinition['isComplete']) {
      return;
    }

    await queryInterface.addColumn('forms_record', 'isComplete', {
      type:Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('forms_record', 'isComplete');
  }
};
