import * as formidable from 'formidable';
import * as path from 'path';
import * as express from 'express';
import { BadRequestError, ForbiddenError, NotFoundError, ServerError } from '../../model/HttpErrors';
import FormDocument from '../db/models/formDocument.model';
import { IDocumentType } from '../../common/contracts/document';
import { IGetByIdParams } from '../../common/contracts/common';
import { promisify } from 'util';
import { Files } from 'formidable';
import { unlink } from 'fs';
import DocumentToRecord from '../db/models/document_to_record.model';
import logger from '../utils/Logger';


export class DocumentController {
	public static async upload(req: JwtRequest): Promise<IDocumentType> {
		const signature = "DocumentController.upload: ";
		logger.info(signature + "Uploading Documents");

		const form = new formidable.IncomingForm();

		form.encoding = 'utf-8';

		const uploadPath = process.env.UPLOADS || '../uploads';
		form.uploadDir = path.isAbsolute(uploadPath) ? uploadPath : path.resolve(process.env.PWD as string, uploadPath);

		const files: Files = await new Promise((resolve, reject) => {
			form.parse(req, (err, fields, files: Files) => {
				if (err) {
					return reject('Cannot parse multipart form');
				}
				resolve(files);
			});
		}) as any as Files;

		if (!files.document) {
			throw new BadRequestError('Document is not found in multipart form.');
		}

		if (Array.isArray(files.document)) {
			throw new BadRequestError('Presented document was unexpectedly an array.');
		}

		return DocumentController.createDocument(files.document, req.tokenData.user.id);
	}

	private static async createDocument(document: formidable.File, userId: number): Promise<any> {
		const formDocument = await FormDocument.create({
			fileName: document.name,
			mimeType: document.type,
			path: document.path,
			size: document.size,
			userId: userId
		},
			{ fields: ['fileName', 'mimeType', 'path', 'size', 'userId'] }
		);

		return {
			id: formDocument.id,
			fileName: formDocument.fileName,
			size: formDocument.size,
			mimeType: formDocument.mimeType
		};
	}

	public static async download(req: JwtRequest, res: express.Response) {
		const { id: documentId } = req.params as unknown as IGetByIdParams;

		if (isNaN(documentId)) {
			throw new ServerError();
		}

		const document = await FormDocument.findByPrimary(documentId);

		if (!document) {
			res.status(404).end();
			return;
		}

		res.sendFile(document.path, {
			headers: {
				'Content-Type': document.mimeType,
			}
		});
	}

	public static async delete(req: JwtRequest) {

		const { id: documentId } = req.params as unknown as IGetByIdParams;

		const document = await FormDocument.findByPrimary(documentId);

		if (!document) {
			throw new NotFoundError('Document is not found.');
		}

		if (document.userId !== req.tokenData.user.id) {
			throw new ForbiddenError('Document can be deleted only by it\'s owner.');
		}

		const recordsCount = await DocumentToRecord.count({
			where: {
				documentId,
			}
		});

		if (recordsCount > 0) {
			throw new BadRequestError('Document is already attached to a record and cannot be deleted.');
		}

		try {

			await Promise.all([

				FormDocument.destroy({ where: { id: documentId } }),

				promisify(unlink)(document.path),

			]);

			return { status: 'ok' };

		} catch (err) {
			console.error('Exception in DocumentController.delete', err);
			throw new ServerError();
		}

	}

}
