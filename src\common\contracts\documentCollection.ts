import * as t from 'io-ts';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { PaginationResponse } from './pagination';
import { SortOrderValues } from './common';
import { constructType } from './extend';

export function constructDocumentCollectionType<T>(dateType: t.Type<T>) {
  return t.type({
    id: t.number,
    name: t.string,
    reviewSchedule: t.string,
    createdAt: dateType,
    updatedAt: dateType,
  });
}

export const DocumentCollectionOutputModel = constructDocumentCollectionType(t.string);

export interface IDocumentCollectionOutputModel extends t.TypeOf<typeof DocumentCollectionOutputModel> {}

export type DocumentCollectionPaginationResponse = PaginationResponse<IDocumentCollectionOutputModel>;

export const UpdateDocumentCollectionInputModel = t.exact(t.type({
  id: t.refinement(t.number, id => id > 0, 'EnumId is invalid'),
  name: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Name should not be empty or it\'s too long '),
  reviewSchedule: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Name should not be empty or it\'s too long '),
}));

export interface IUpdateDocumentCollectionInputModel extends t.TypeOf<typeof UpdateDocumentCollectionInputModel> {}

export const CreateDocumentCollectionInputModel = t.exact(t.type({
  name: t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Name should not be empty or it\'s too long '),
  reviewSchedule:t.refinement(t.string, v => (v.length > 0 || v.length > 128), 'Name should not be empty or it\'s too long '),
}));

export interface ICreateDocumentCollectionInputModel extends t.TypeOf<typeof CreateDocumentCollectionInputModel> {}

// See comment in /contracts/common.ts for explanation for this ignore
// @ts-ignore
export const DocumentCollectionSortedField = t.union(['createdAt', 'updatedAt', 'name'].map(f => t.literal(f)));

export type IDocumentCollectionSortedField = t.TypeOf<typeof DocumentCollectionSortedField>;

export const DocumentCollectionQueryParams = constructType({
  skip: IntFromString,
  limit: IntFromString,
  sortBy: DocumentCollectionSortedField,
  order: SortOrderValues
}, {
  // countUsers: t.refinement(IntFromString, v => v === 1 || v === 0, 'valid countUsers parameter' ),
});

export interface IDocumentCollectionQueryParams extends t.TypeOf<typeof DocumentCollectionQueryParams> {}

export const DocumentImportOutput = t.type({
  message: t.string,
  data: t.type({
    newRecords: t.number,
    updatedRecords: t.number
  })
});

export interface IDocumentImportOutput extends t.TypeOf<typeof DocumentImportOutput> {}

