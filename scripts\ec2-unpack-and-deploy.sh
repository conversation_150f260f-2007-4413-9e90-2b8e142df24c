#!/usr/bin/env bash
set -o xtrace
set -e

# The working directory
ROOT_DIR=/var/www/quality
# The directory where the code will be placed during the build
SRC_DIR=$ROOT_DIR/source
# The directory holding each of the releases
REL_DIR=$ROOT_DIR/releases
# The git repo that the source code should be fetched from
GIT_REPO=$1
# The Branch or Ref for the repo that the app will run
GIT_REF=$2

# Create the directory if it doesn't exist
mkdir -p $ROOT_DIR;
mkdir -p $SRC_DIR;
mkdir -p $REL_DIR;

# Set the working directory
cd $SRC_DIR;

# Show Verbose GIT output (Debugging config issues)
# export GIT_SSH_COMMAND="ssh -vvv"

# Ensure the repo has been initalised
git status || git clone -b $GIT_REF --single-branch $GIT_REPO $SRC_DIR

# Hard Update of the repo
git fetch
git reset --hard origin/$GIT_REF

# Update the Env Config defaulting to the existing file if one exists
if [ ! -f $ROOT_DIR/.env ]; then
	cp -rf $SRC_DIR/.env.dist $SRC_DIR/.env
else
	cp -rf $ROOT_DIR/.env $SRC_DIR/.env
fi

# Remove the Build Directory forcing complete rebuild
rm -rf ./build

# Install packages
npm update --production

#Unzip the Build Application and remove the archive
unzip $ROOT_DIR/../zip/quality.zip -d ./
rm $ROOT_DIR/../zip/quality.zip

# Error if the build failed
if [ ! -d "./build" ]; then
	echo "Error! Building Application Failed"
	exit 1
fi

# Perform Migrations
npm run dbMigrate

# Create a unique name for the release
DATE=`date +%Y%m%d%H%M%S%N`

# Create the unique release directory
mkdir $REL_DIR/$DATE;

# Copy the contents of build to the release directory
cp -r $SRC_DIR/build $REL_DIR/$DATE;
cp -r $SRC_DIR/node_modules $REL_DIR/$DATE;

# Ensure we have pm2 ready to go before symlink Updates
# npm i pm2 -g

# Create or Overwrite the a symlink for the current release number
ln -sfn $REL_DIR/$DATE $ROOT_DIR/current
ln -sfn $REL_DIR/$DATE/node_modules $ROOT_DIR/node_modules

# Reset working directory
cd $ROOT_DIR

# Copy the ecoSystem File to the Root Dir
\cp -rf $SRC_DIR/ecosystem.ec2.config.js $ROOT_DIR/ecosystem.ec2.config.js
if [ ! -f $ROOT_DIR/.env ]; then
    \cp -rf $SRC_DIR/.env $ROOT_DIR/.env
fi

# Start or restart the application
pm2 startOrRestart ecosystem.ec2.config.js --env $3

# Perform Cleanup

# Set the working Directory
cd $REL_DIR

# Delete anything beyond the last 5th deployment
ls -dt */ | tail -n +6 | xargs rm -rf

# Reload PM2
pm2 restart all