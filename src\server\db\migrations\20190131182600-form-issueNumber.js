'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    const tableDefinition = await queryInterface.describeTable('forms');

    if (tableDefinition['issueNumber']) {
      return;
    }

    await queryInterface.addColumn('forms', 'issueNumber', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: null,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('forms', 'issueNumber');
  }
};