import { Readable } from 'stream';
import * as moment from 'moment';
import 'moment-timezone';
import { omit, find } from 'lodash';
import { reporter } from 'io-ts-reporters';
import Form from '../db/models/form.model';
import {
	FormOutputValidationModel, IDuplicateFormParams, IFormDeleteParams, IFormOutputValidationModel,
	IFormParams,
	IFormsQueryParams,
	IFormUpdateParams, ISendFormReportParams, IFormScheduleParams, IFormRevertParams
} from '../../common/contracts/form';
import FormRecordProperty from '../db/models/formRecordProperty.model';
import Property from '../db/models/property.model';
import FormRecord from '../db/models/formRecord.model';
import FormCategory from '../db/models/category.model';
import UserGroup from '../db/models/group.model';
import User from '../db/models/user.model';
import FormDocument from '../db/models/formDocument.model';
import { ForbiddenError, HttpError, NotFoundError, ServerError } from '../../model/HttpErrors';
import { IGetByIdParams } from '../../common/contracts/common';
import UserToLocation from '../db/models/user_to_location.model';
import FormSchedule from '../db/models/form-schedule.model';
import { Sequelize } from 'sequelize-typescript';
import FormLocation from '../db/models/formLocation.model';
import { MailService } from '../service/mail.service';
import { ReportService } from '../service/report.service';
import UserToGroup from '../db/models/user_to_group.model';
import has = Reflect.has;
import { createReadStream } from 'fs';
import { systemSettingsService } from '../service/settings.service';
import * as SqlString from 'sqlstring';
import { FormService } from '../service/form.service';
// @ts-ignore
import { RecordService } from '../service/record.service';
import logger from '../utils/Logger';
// import * as util from 'util';
import user from '../db/models/user.model';
import Document from '../db/models/document.model'
import { timeZone } from '../../common/constants';
import RiskToForm from '../db/models/risk_to_form.model';
import Risks from '../db/models/risk.model';
import { Risk } from '../../common/Risk';


const className = "FormController";

export class FormController {
	public static async getForms(req: JwtRequest & Query<IFormsQueryParams>) {
		const signature = "FormController.getForms: ";
		const queryParams: IFormsQueryParams = { ...req.query }; // clone query params
		let category: FormCategory | null = null;
		if (queryParams.category) {
			category = await FormCategory.findByPk(queryParams.category);
		}
		/*
			Be careful when using joins on HasMany properties!
	
			The limit affects outputs when the joins are present!
	
			this is because the underlying sequelize script does an inner join on the many properties
			causing limit:20 to potentially only return 1 unique record!
	
			Instead, break it into multiple queries
		*/
		const isAdminOrManager = req.tokenData.user.role === 'administrator' || req.tokenData.user.role === 'manager';
		const isAdmin = req.tokenData.user.role === 'administrator';
		const whereClause: [{ [key: string]: any }] = [{
			'summary': {
				[Sequelize.Op.not]: null, // prevent fetching invalid or incomplete forms
			}
		}];

		/** Archived constraints */
		whereClause[0].isDeleted = false;

		if (queryParams.showAll === 'active') {
			whereClause[0]['archivedAt'] = null;
		} else if (queryParams.showAll === 'complete') {
			whereClause[0]['archivedAt'] = {
				[Sequelize.Op.not]: null,
			};
		} else if (queryParams.showAll === 'deleted') {
			if (isAdmin) {
				whereClause[0]['isDeleted'] = true;
			} else {
				// Non Admins may not view archived forms
				throw new ForbiddenError('Access to archived forms is denied.');
			}
		}
		/** End Archive Constraints */

		/** Template Constraints */
		if (queryParams.isTemplate) {
			if (isAdmin) {
				whereClause[0]['isTemplate'] = queryParams.isTemplate;
			} else {
				// Non Admins may not view form templates
				throw new ForbiddenError('Access to form templates is denied.');
			}
		} else {
			whereClause[0]['isTemplate'] = false;
		}
		/** End Template Constraints */

		/**
		 * Location Limitations
		 * All users will be constrained by locations assigned to them, including Admins
		 * All users with no locations assigned will have access to all locations
		 * All users may constrain locations which they see data from
		 */

		/** Satisfies: All users may constrain locations which they see data from */
		// Location should not be considered in case of DRM-Task category
		if (!(category && category.name === 'DRM-Task') && queryParams.locationId && queryParams.locationId.length) {
			logger.silly("Constraining by locationId: " + queryParams.locationId);
			const locationIds = queryParams.locationId.split(",").map(id => Number(id));
			if (locationIds.length > 1) {
        whereClause.push({ formLocationId : { [Sequelize.Op.in]: locationIds } });
			} else {
        whereClause.push({ formLocationId : locationIds[0] });
			}
		} else {
			logger.silly("Not filtering forms by LocationId");
		}

		/** Satisfies: All users will be costrained by locations assigned to them, including Admins */
		/** Satisfies: All users with no locations assigned will have access to all locations */
		const locations = await UserToLocation.findAll<UserToLocation>({
			where: { userId: req.tokenData.user.id },
			attributes: ['locationId']
		});

		if (!(category && category.name === 'DRM-Task') && locations.length) {
			logger.silly(`Restricting Locations. Found [${locations.length}] Locations`);
			const newConstraint = locations.length > 1 ?
				{ [Sequelize.Op.in]: locations.map(location => location.locationId) }
				: locations[0].locationId;

        whereClause.push({
          [Sequelize.Op.or]: [
            { formLocationId: newConstraint },
            { assignedUserId: req.tokenData.user.id }
          ]
        });
		}

		/** End Location Limitations */

		/**
		 * GroupID, UserID Limitations Limitations
		 * GroupId Filter will always default to departments assigned to user
		 * GroupId Blank filter (Not null) will imply an intentionally blank filter
		 * 
		 * When
		 *  Any query is performed
		 *    Admin: Nothing
		 *    Manager: Constrain to assignedToSelf, OR departments assigned
		 *    User: Constrain to assignedToSelf
		 **/

		if (!isAdmin) {
			if (!isAdminOrManager) {
				whereClause[0].assignedUserId = req.tokenData.user.id;
			} else {
				const groups = await UserToGroup.findAll<UserToGroup>({
					where: { userId: req.tokenData.user.id },
					attributes: ['groupId']
				});

				const newVal = groups.length > 1 ?
					{ [Sequelize.Op.in]: groups.map(ug => ug.groupId) }
					: groups[0].groupId;

				whereClause.push({
					[Sequelize.Op.or]: [
						{ assignedUserId: req.tokenData.user.id },
						{ userGroupId: newVal }
					]
				})
			}
		}

		/**
		 * When
		 *  User is filtered
		 *    department is filtered
		 *      Admin: Filter by User and Department
		 *        If user is self, also include where ( in department, and assignedUserId is null )
		 *      Manager: Filter by User and department.
		 *        If user is self, also include where ( in department, and assignedUserId is null )
		 *      User: do nothing
		 *    department is not filtered
		 *      Admin: Filter by User
		 *      Manager: Filter by User
		 *        If user is self, also include where ( assignedUserId is null )
		 *      User: do nothing
		 *  Department is filtered, user is not filtered:
		 *    Admin: Filter by department
		 *    Manager: Filter by department
		 *    User: do nothing
		 */
		let groupFilter: any | null = null;
		if (queryParams.groupId && queryParams.groupId.length) {
			const groupIds = queryParams.groupId.split(",").map(id => Number(id));
			groupFilter = groupIds.length > 1 ?
				{ [Sequelize.Op.in]: groupIds }
				: groupIds[0];
		}

		if (isAdminOrManager) {
			if (queryParams.assignedUserId) {
				let assignedUserId: number | undefined = queryParams.assignedUserId;

				if (assignedUserId) {
					const assignedUser = await User.findOne({
						where: { id: assignedUserId }
					});

					if (!assignedUser) {
						throw new ServerError('Cannot find assigned user');
					}
				}

				if (groupFilter) {
					if (assignedUserId === req.tokenData.user.id) {
						whereClause.push({
							[Sequelize.Op.or]: [
								{ assignedUserId: assignedUserId },
								{ userGroupId: groupFilter, assignedUserId: null }
							]
						});
					} else {
						whereClause.push({
							assignedUserId: assignedUserId,
							userGroupId: groupFilter
						});
					}
				} else {
					// Admins and Managers have the right to constrain by this
					whereClause[0].assignedUserId = assignedUserId;
				}
			} else if (queryParams.groupId && queryParams.groupId.length) {
				whereClause[0].userGroupId = groupFilter;
			}
		}

		/**
		 * filter by created date
		 */
		if (queryParams.createdAtEnd) {
			queryParams.createdAtEnd = new Date(queryParams.createdAtEnd.getTime()); // to avoid date mutation need to clone original date
			queryParams.createdAtEnd.setDate(queryParams.createdAtEnd.getDate() + 1);  // increment date in order to include end date in the period
		}

		if (queryParams.createdAtStart && queryParams.createdAtEnd) {
			whereClause[0]['createdAt'] = {
				[Sequelize.Op.between]: [queryParams.createdAtStart, queryParams.createdAtEnd],
			};
		} else if (queryParams.createdAtStart) {
			whereClause[0]['createdAt'] = {
				[Sequelize.Op.gte]: queryParams.createdAtStart,
			};
		} else if (queryParams.createdAtEnd) {
			whereClause[0]['createdAt'] = {
				[Sequelize.Op.lte]: queryParams.createdAtEnd,
			};
		}

		/**
		 * filter by due date
		 */
		if (queryParams.dueAtEnd) {
			queryParams.dueAtEnd = new Date(queryParams.dueAtEnd.getTime()); // to avoid date mutation need to clone original date
			queryParams.dueAtEnd.setDate(queryParams.dueAtEnd.getDate() + 1);  // increment date in order to include end date in the period
		}

		if (queryParams.dueAtStart && queryParams.dueAtEnd) {
			whereClause[0]['dueAt'] = {
				[Sequelize.Op.between]: [queryParams.dueAtStart, queryParams.dueAtEnd],
			};
		} else if (queryParams.dueAtStart) {
			whereClause[0]['dueAt'] = {
				[Sequelize.Op.gte]: queryParams.dueAtStart,
			};
		} else if (queryParams.dueAtEnd) {
			whereClause[0]['dueAt'] = {
				[Sequelize.Op.lte]: queryParams.dueAtEnd,
			};
		}

		if (queryParams.dueFilter) {
			switch (queryParams.dueFilter) {
				case 'overdue':
					whereClause.push(
						{ 'dueAt': { [Sequelize.Op.lte]: Sequelize.literal('NOW()'), } }
					);
					break;
				case 'alert':
					whereClause.push({
						[Sequelize.Op.and]: Sequelize.literal('NOW() BETWEEN alertAt AND dueAt')
					});
					break;
			}
		}

		if (queryParams.category) {

			whereClause[0]['categoryId'] = queryParams.category;
		} else if (queryParams.excludeCategories && queryParams.excludeCategories.length) {
			const categoriesToExclude = queryParams.excludeCategories.split(',').map(id => Number(id));

			whereClause[0]['categoryId'] = {
				[Sequelize.Op.notIn]: categoriesToExclude
			}
		}

		let sortParam: string = queryParams.sortBy;
		if (sortParam === 'category') {
			sortParam = 'categoryId';
		}

		let orderParams: any[] = [[sortParam, queryParams.order.toUpperCase()]]; // default sorting params
		let extraAttributes: any[] = [];
		if (queryParams.sortBy === 'location') {
			orderParams = [[Sequelize.literal('locationName'), queryParams.order.toUpperCase()]];
			// this sub-query is required for correct ordering by location name
			extraAttributes = [[Sequelize.literal('(SELECT name FROM form_locations WHERE form_locations.id = `Form`.formLocationId)'), 'locationName']];
		} else if (queryParams.sortBy === 'assignedTo') {
			orderParams = [
				[Sequelize.literal('assignedName'), queryParams.order.toUpperCase()],
			];
			extraAttributes = [
				// this sub-query is required for correct ordering by assigned user or group
				[Sequelize.literal(`(SELECT
          IF (Form.assignedUserId IS NULL,
            (SELECT IF(Form.userGroupId IS NULL, '', (SELECT groupName FROM \`groups\` WHERE id = Form.userGroupId))),
            (SELECT CONCAT(firstName, ' ', lastName) FROM users WHERE users.id = Form.assignedUserId)
          ))
          `), 'assignedName'],
			];
		} else if (queryParams.sortBy === 'documentIdentity') {
			orderParams = [
				[Sequelize.literal('documentIdentity'), queryParams.order.toUpperCase()],
			];
			extraAttributes = [
				// this sub-query is required for correct ordering by assigned user or group
				[Sequelize.literal(`(        	
					SELECT documentIdentity
					FROM forms_record fr 
					INNER JOIN forms_record_property frp 
					ON frp.formRecordId = fr.id 
					INNER JOIN properties p
					ON frp.propertyId = p.id 
					INNER JOIN documents d 
					ON stringData = d.id 
					WHERE p.name = 'documentId'
					AND stringData REGEXP '^[0-9]+$' = 1
					AND fr.formId = Form.id
				)`), 'documentIdentity'],
			];
		}


		if (queryParams.search) {
			whereClause[0]['summary'] = {
				[Sequelize.Op.like]: `%${queryParams.search}%`,
			};
		}


		if (queryParams.originatorName) {

			logger.silly(signature + `Filtering by originator name ${queryParams.originatorName}`);

			/*
				Make use of escape functionality but ensure we support the like function correctly
			 */
			const inputString = SqlString.escape(queryParams.originatorName);
			const likeString = '\'%' + inputString.replace(/(^')|('$)/g, '') + '%\'';

			whereClause.push({
				id: {
					[Sequelize.Op.in]: Sequelize.literal(`(
						SELECT formid
						FROM forms_record_property
						INNER JOIN forms_record
						ON forms_record_property.formRecordId = forms_record.id
						WHERE
						(
							propertyID = (
							SELECT id
							FROM properties
							WHERE \`name\` = 'OriginatorName'
							)
							AND stringData like ${likeString}
						))
					`)
				}
			});
		}

		if (queryParams.reportFormType) {
			whereClause[0]['id'] = {
				[Sequelize.Op.in]: Sequelize.literal(`(
					SELECT formid
					FROM forms_record_property
					INNER JOIN forms_record
					ON forms_record_property.formRecordId = forms_record.id
					WHERE
					(
						propertyID = (
						SELECT id
						FROM properties
						WHERE \`name\` = 'ReportFormType'
						)
						AND enumId = ${SqlString.escape(queryParams.reportFormType)}
					))
				`)
			};
		}


		if (queryParams.documentId) {
			whereClause[0]['id'] = {
				[Sequelize.Op.in]: Sequelize.literal(`(
					SELECT formid
					FROM forms_record_property
					INNER JOIN forms_record
					ON forms_record_property.formRecordId = forms_record.id
					WHERE
					(
						propertyID = (
						SELECT id
						FROM properties
						WHERE \`name\` = 'documentId'
						)
						AND stringData = ${queryParams.documentId}     
					))
				`)
			};
		}
		if (queryParams.riskTypeId) {
			whereClause[0]['id'] = {
				[Sequelize.Op.in]: Sequelize.literal(`(
					SELECT formid
					FROM forms_record_property
					INNER JOIN forms_record
					ON forms_record_property.formRecordId = forms_record.id
					WHERE
					(
						propertyID = (
						SELECT id
						FROM properties
						WHERE \`name\` = 'riskType'
						)
						AND intData = ${queryParams.riskTypeId}     
					))
				`)
			};
		}
		if (queryParams.riskId) {
			whereClause[0]['id'] = {
				[Sequelize.Op.in]: Sequelize.literal(`(
					SELECT formid
					FROM risk_to_form
					WHERE riskId = ${queryParams.riskId}
				)`)
			};
		}
		const { rows: forms, count: totalCount }: { rows: Form[], count: number } = await Form.findAndCountAll<Form>({
			where: { [Sequelize.Op.and]: whereClause },
			distinct: true,
			attributes: {
				include: [
					[Sequelize.literal('`Form`.`dueAt` < NOW() AND `Form`.`archivedAt` IS NULL'), 'isOverdue'],
					[Sequelize.literal('NOW() BETWEEN `Form`.`alertAt` AND `Form`.`dueAt` AND `Form`.`archivedAt` IS NULL'), 'isAlertOverdue'],
					...extraAttributes,
				]
			},
			include: [
				{
					model: UserGroup,
					as: 'userGroup',
					required: false,
					attributes: {
						exclude: ['isArchived', 'createdAt', 'updatedAt']
					},
				},
				{
					model: FormCategory,
					required: true,
				},
				{
					model: FormLocation,
					required: false,
					attributes: {
						exclude: ['isArchived']
					},
				},
				{
					model: User,
					as: 'assignedUser',
					required: false,
					attributes: ['id', 'username', 'firstName', 'lastName'],
				},
				// records are not in use in dashboard anymore, consider to exclude association below to optimize the query
				{
					model: FormRecord,
					as: 'records',
					required: false,
					include: [{
						model: FormRecordProperty,
						required: false,
						include: [
							{
								model: Property,
								required: true
							},
						]
					}],
				},
				{
					model: FormSchedule,
					required: false,
					as: 'schedule'
				}
			],
			order: orderParams,
			limit: queryParams.limit,
			offset: queryParams.skip,
		});

		/**
		 * Since forms and document model are not directly related
		 * we need to manually find the documentId from form record
		 * properties and include the document in the result.
		 */
		let formsWithDocumentAttached: any = [];
		for (let form of forms) {
			let formRecords = form.records;
			let formProperties: any = [];
			formRecords.forEach(fr => {
				formProperties.push(fr.properties);


			})
			formProperties = [].concat.apply([], formProperties);
			let documentId = find(formProperties, ['property.name', 'documentId']);
			let document: any;
			if (documentId) {
				document = await Document.findOne({
					where: {
						id: documentId.stringData
					},
					raw: true
				});
			}
			if (!document) {
				document = {
					id: '',
					name: 'Unknown/Deleted document.'
				}
			}

			// We have to pass "plain: true" to manipulate the form object 
			let newFormObject: any = form.get({
				plain: true
			});
			newFormObject.document = document;
			formsWithDocumentAttached.push(newFormObject);
		}

		/**
		 * calculate counts for overdue, alert and remaining tasks
		 * omit some filters that may affect correct calculations
		 * please notice that date filters for createdAt and dueAt don't affect counts in top card filters (overdue, requires attention, remaining)
		 **/
		whereClause[0] = Object.assign(
			omit(whereClause[0], ['archivedAt', 'dueAt', 'createdAt', 'isDeleted', Sequelize.Op.and]),
			{
				archivedAt: null,
				isDeleted: false
			}
		);
		const countWhereClause = whereClause;

		/*
		logger.silly("Counting Forms By");
		console.log(util.inspect(countWhereClause, false, null, true));
		*/

		const [overdueTasks, alertTasks, remainingTasks] = await Promise.all([

			/**
			 * count overdue forms
			 */
			Form.count({
				where: {
					[Sequelize.Op.and]: [
						{
							dueAt: {
								[Sequelize.Op.lte]: Sequelize.literal('NOW()')
							}
						},
						...countWhereClause
					]
				}
			}),

			/**
			 * count forms that require attention form
			 */

			Form.count({
				where: {
					[Sequelize.Op.and]: [
						{
							[Sequelize.Op.and]: [
								Sequelize.literal('NOW() BETWEEN `Form`.`alertAt` AND `Form`.`dueAt`')
							]
						},
						...countWhereClause
					]
				}
			}),

			/**
			 * count all active forms - remaining tasks
			 */
			Form.count({
				where: {
					[Sequelize.Op.and]: [...countWhereClause]
				}
			}),
		]);

		return {
			items: (formsWithDocumentAttached || []),
			totalCount: totalCount,
			overdueTasks,
			alertTasks,
			remainingTasks,
		};
	}

	public static async getFormById(req: JwtRequest) {

		const { id: formId } = req.params as unknown as IGetByIdParams;
		const signature = className + `.getFormById: Form[${formId}] `;

		const isAdmin = req.tokenData.user.role === 'administrator';
		const isAdminOrManager = isAdmin || req.tokenData.user.role === 'manager';

		const form = await Form.findByPrimary<Form>(formId, {
			attributes: {
				include: [
					[Sequelize.literal('`Form`.`dueAt` < NOW()'), 'isOverdue'],
					[Sequelize.literal('NOW() BETWEEN `Form`.`alertAt` AND `Form`.`dueAt`'), 'isAlertOverdue'],
				]
			},
			include: [
				{
					model: UserGroup,
					required: false
				},
				{
					model: FormCategory,
					required: true
				},
				{
					model: FormLocation,
					required: false
				},
				{
					model: FormRecord,
					required: false,
					include: [{
						model: FormRecordProperty,

						required: true,
						include: [{
							model: Property,
							required: true,
						}],
					}, {
						model: FormDocument,
						as: 'documents',
						required: false,
						through: {
							attributes: []
						},
						attributes: {
							exclude: ['path', 'userId'],
						},
					}, {
						model: FormDocument,
						as: 'tickedDocuments',
						required: false,
						through: {
							attributes: []
						},
						attributes: ['id'],
					},
					{
						model: User,
						as: 'createdByUser',
						required: false,
						attributes: ['id', 'firstName', 'lastName']
					}],
				},
				{
					model: User,
					as: 'assignedUser',
					required: false,
					attributes: ['id', 'username', 'firstName', 'lastName'],
				}
			]
		});

		if (!form) {
			throw new NotFoundError('The form is not found');
		}

		/**
		 * Check if a user has access to this form for non-admins
		 */

		if (!isAdminOrManager) {

			if (form.assignedUserId && form.assignedUserId !== req.tokenData.user.id) {
				logger.error(signature + `User[${req.tokenData.user.id}] is not assigned user and is not admin or managerUser`);
				throw new ForbiddenError('User cannot access this form.');
			}

			if (!isAdmin && form.isDeleted) {
				throw new ForbiddenError('User cannot access deleted form.');
			}

			if (form.assignedUserId === null && form.formLocationId !== null && req.tokenData.user.role === 'user') {
				// check if user has same location as formLocationId
				const userToLocation = await UserToLocation.findOne({
					where: {
						userId: req.tokenData.user.id,
						locationId: form.formLocationId,
					},
				});

				if (!userToLocation) {
					// Check if the user perhaps has no locations
					const locations = await UserToLocation.findAll<UserToLocation>({
						where: {
							userId: req.tokenData.user.id,
						},
						attributes: ['locationId']
					});

					if (locations.length != 0) {
						throw new ForbiddenError('User cannot access forms in other locations.');
					}
				}
			}
		}

		if (form.parentFormId) {
			let parentForm = await Form.unscoped().findByPrimary(form.parentFormId);

			if (parentForm && parentForm.parentFormId) {
				for (let i = 0; i < 1000; i++) {
					if (parentForm && parentForm.parentFormId) {
						const nextParentForm = await Form.unscoped().findByPrimary(parentForm.parentFormId);

						if (nextParentForm && !nextParentForm.isTemplate) {
							parentForm = nextParentForm;
						}
					} else {
						break;
					}
				}
			}

			form.ancestorFormId = parentForm ? parentForm.id : form.parentFormId;
		}

		const resultObject = form.toJSON();

		if (form.archivedAt || form.isTemplate) { // if the form is complete or a template then add child forms in final output
			resultObject['childForms'] = await FormController.getChildFormsTree(formId);
		}

		const validationResult = FormOutputValidationModel.decode(resultObject);

		if (validationResult.isRight()) {
			const result = validationResult.value;

			// Manually sort the sequences
			result.records = result.records.sort((a, b) => a.sequence > b.sequence ? 1 : -1);

			return result;
		} else {
			const report = reporter(validationResult);
			console.error('Output validation failed on ', resultObject);
			console.log('validation report - ', report.join(', '));
			throw new ServerError();
		}
	}

	public static async createForm(req: JwtRequest) {
		// creating a new form
		try {
			let params = req.body as IFormParams;
			return await FormService.createForm(params, req.tokenData.user.id);
		} catch (e) {
			console.error('Exception in FormController.createForm', e);
			if (e instanceof HttpError) {
				throw e;
			} else {
				throw new ServerError();
			}
		}
	}

	public static async runTemplate(req: JwtRequest) {
		try {
			const { id } = req.params;

			const template = await Form.findByPrimary(id, {
				include: [{
					model: FormSchedule,
					required: false
				}]
			});

			if (!template)
				throw new NotFoundError("Template Not Found");

			try {
				await FormService.runTemplate(template);

				template.executionSuccess = true;
			} catch (e) {
				template.executionSuccess = false;
				template.executionMessage = e.message;
			}

			return template;
		} catch (e) {
			console.error('Exception in FormController.runSchedules', e);
			if (e instanceof HttpError) {
				throw e;
			} else {
				throw new ServerError();
			}
		}
	}

	public static async runSchedules(req: JwtRequest) {
		// creating a new form
		try {
			return await FormService.processFormSchedules();
		} catch (e) {
			console.error('Exception in FormController.runSchedules', e);
			if (e instanceof HttpError) {
				throw e;
			} else {
				throw new ServerError();
			}
		}
	}

	public static async updateFormSchedule(req: JwtRequest) {
		const {
			id,
			schedule
		} = req.body as IFormScheduleParams;

		const form = await Form.findByPrimary<Form>(id);

		if (!form || form.isDeleted) {
			throw new NotFoundError('Form is not found');
		}

		let formSchedule: FormSchedule | null = null;

		if (form.formScheduleId) {
			formSchedule = await FormSchedule.unscoped().scope('all').findOne({
				where: {
					id: form.formScheduleId
				}
			});
		}

		if (!schedule) {
			if (formSchedule) {
				await formSchedule.destroy();
				return formSchedule.reload();
			}

			return { success: true };
		}

		if (formSchedule) {
			formSchedule.setDataValue("deletedAt", null);
			Object.assign(formSchedule, schedule);
			formSchedule.processedTo = new Date();
			await formSchedule.save();
		} else {
			formSchedule = await FormSchedule.create(schedule);

			await form.update({
				formScheduleId: formSchedule.id
			});
		}

		return formSchedule.reload();
	}

	public static async updateForm(req: JwtRequest) {

		// creating a new form
		const {
			id,
			stage,
			formLocationId,
			userGroupId,
			assignedUserId,
			notifyOnComplete,
			dueAt,
			alertAt,
			summary
		} = req.body as IFormUpdateParams;

		const updateProperties: { [k: string]: any } = {
			id,
			stage,
			formLocationId,
			userGroupId,
		};

		if (summary) {
			updateProperties['summary'] = summary;
		}

		// The reason has is being used in place of if... is null is an acceptable input
		if (has(req.body, 'assignedUserId')) {
			updateProperties.assignedUserId = assignedUserId;
		}

		if (notifyOnComplete) {
			updateProperties.notifyOnComplete = notifyOnComplete;
		}

		if (notifyOnComplete) {
			updateProperties.notifyOnComplete = notifyOnComplete;
		}

		// Passing null here is a valid input
		if (has(req.body, 'dueAt')) {
			updateProperties.dueAt = dueAt ? new Date(dueAt) : null;
		}

		if (has(req.body, 'alertAt')) {
			updateProperties.alertAt = alertAt ? new Date(alertAt) : null;
		}
		try {
			const [recordsUpdated] = await Form.update(updateProperties, { where: { id: id } });

			return { status: 'ok', recordsUpdated };
		} catch (e) {
			console.error('Exception in FormController.updateForm', e);
			throw new ServerError();
		}
	}

	public static async finalizeForm(req: JwtRequest) {
		const signature = className + ".finalizeForm: ";

		const { id: formId } = req.params as unknown as IGetByIdParams;
		logger.silly(signature + `Finalizing Form[${formId}]`);

		const form: IFormOutputValidationModel = await FormController.getFormById(req);

		if (!form || form.archivedAt) {
			throw new NotFoundError('Form is not found.');
		}

		if (form.notifyOnComplete && await systemSettingsService.getSettingsValue('isEmailsEnabled')) {

			const pdfStream = await ReportService.generateReportToStream(form);

			const attachments = [
				{
					filename: `${form.category.name.toLowerCase()}-${moment().tz(timeZone).format('YYYY-MMM-DD')}.pdf`,
					contentType: 'application/pdf',
					content: pdfStream as Readable,
				}
			];

			/*
				Sort the stageRecords from last to first, to ensure that the stage2 record found
				is definitely the final submission
			 */
			const sortedRecords = form.records.sort((a, b) => (b.sequence < a.sequence ? -1 : 1));
			const stage2record = sortedRecords.find(record => record.stage === 2 && record.isComplete);

			if (stage2record && stage2record!.tickedDocuments && stage2record!.tickedDocuments!.length) {
				const attachedDocuments = await FormDocument.findAll({
					where: {
						id: {
							[Sequelize.Op.in]: stage2record!.tickedDocuments!.map(doc => doc.id),
						},
					}
				});

				for (const tickedDocument of attachedDocuments) {
					attachments.push({
						filename: tickedDocument.fileName,
						contentType: tickedDocument.mimeType,
						content: createReadStream(tickedDocument.path),
					});
				}
			}

			try {

				await MailService.sendEmail({
					to: form.notifyOnComplete as string,
					from: process.env.EMAIL_FROM || '<EMAIL>',
					subject: 'A response to your Feedback Form from Bethany Christian Care',
					template: 'final-report',
					context: {},
					attachments: attachments,
				});

			} catch (err) {
				console.error('Exception while sending email', err);
			}
		}

		// Assign the form back to the original department on completion, defaulting to blank
		let userGroupId: number | null = null;

		const submissions = form.records.filter(record => record.isComplete);
		const firstSubmission = submissions.find(record => record.isComplete && record.stage === 0);

		if (firstSubmission) {
			// Find the first userGroup it was assignedTo
			const firstUserGroupProperty = RecordService.getFieldInRecord(firstSubmission, 'userGroupId');

			if (firstUserGroupProperty && firstUserGroupProperty.intData) {
				userGroupId = firstUserGroupProperty.intData;
			}

			/**
			 * If the form being finalized is a risk assessment, create a new risk assessment
			 */
			if (form.category.name === 'Risk Task') {
				const formToRisk = await RiskToForm.findOne({ where: { formId: form.id } });

				const lastStage1Submission = submissions
					.sort((recordA, recordB) => recordB.sequence < recordA.sequence ? -1 : 1)
					.find(record => record.stage === 1);

				const lastStage2Submission = submissions
					.sort((recordA, recordB) => recordB.sequence < recordA.sequence ? -1 : 1)
					.find(record => record.stage === 2);

				/**
				 * Possible Scenarios:
				 * 1. 1 Stage - No Change
				 * 2. 1 Stage - Post Mitigation Level
				 * 3. 1 Stage - Risk Removed
				 * 4. 2 stage - Risk Removed
				 */

				const riskLevelChangedProperty = RecordService.getFieldInRecord(lastStage1Submission, 'riskLevelChanged');
				const riskLevelChanged = !!(riskLevelChangedProperty && riskLevelChangedProperty.intData);
				const riskRemovedProperty = RecordService.getFieldInRecord(lastStage2Submission || lastStage1Submission, 'riskRemoved');
				const riskRemoved = !!(riskRemovedProperty && riskRemovedProperty.intData);
				const knownRiskLevel = riskLevelChanged && !riskRemoved ? RecordService.getFieldInRecord(lastStage1Submission, 'postMitigationRiskLevel') : RecordService.getFieldInRecord(lastStage1Submission, 'riskLevel');
				const riskLevelEnum = Risk.getEnumById(knownRiskLevel && knownRiskLevel.intData, Risk.Risk);
				const riskLevelKey = Risk.getEnumKeyById(knownRiskLevel && knownRiskLevel.intData, Risk.Risk);
				const riskLevelString = riskRemoved ? 'N/A' : riskLevelKey ? riskLevelKey : 'Unknown';
				const riskStatusString = riskRemoved ? 'Removed' : 'Open';
				const dateOfNextAssessment = riskRemoved ? null : moment().tz(timeZone).startOf('day').add(riskLevelEnum ? riskLevelEnum.nextAssessmentMonthOffset : 1, 'month');

				let risk: Risks | null = formToRisk ? await Risks.unscoped().findById(formToRisk.riskId) : null;

				if (!risk) {
					const firstResidentProperty = RecordService.getFieldInRecord(firstSubmission, 'residentName');
					const residentName = firstResidentProperty && firstResidentProperty.stringData || '';

					risk = await Risks.create({
						residentName,
						summary: form.summary,
						groupId: userGroupId,
						locationId: form.formLocationId,

						riskLevel: riskLevelString,
						riskStatus: riskStatusString,
						dateOfLastAssessment: new Date(),
						dateOfNextAssessment
					});

					await RiskToForm.create({
						formId: form.id,
						riskId: risk.id
					});
				} else {
					await risk.update({
						riskLevel: riskLevelString,
						riskStatus: riskStatusString,
						dateOfLastAssessment: new Date(),
						dateOfNextAssessment
					});
				}
			}
		}

		const [recordsArchived] = await Form.update({
			archivedAt: new Date(),
			assignedUserId: null,
			userGroupId
		}, {
			where: { id: formId }
		});

		return {
			archived: recordsArchived,
		};
	}

	public static async sendReport(req: JwtRequest) {

		const params = req.body as ISendFormReportParams;

		const form: IFormOutputValidationModel =
			await FormController.getFormById({ ...req, params: { id: params.id } } as unknown as JwtRequest);

		if (!form || !form.archivedAt || form.isDeleted) {
			throw new NotFoundError('Form is not found.');
		}

		if (await systemSettingsService.getSettingsValue('isEmailsEnabled')) {

			const pdfStream = await ReportService.generateReportToStream(form);

			const attachments = [
				{
					filename: `${form.category.name.toLowerCase()}-${moment().tz(timeZone).format('YYYY-MMM-DD')}.pdf`,
					contentType: 'application/pdf',
					content: pdfStream as Readable,
				}
			];

			/*
				Sort the stageRecords from last to first, to ensure that the stage2 record found
				is definitely the final submission
			 */
			const sortedRecords = form.records.sort((a, b) => (b.sequence < a.sequence ? -1 : 1));
			const stage2record = sortedRecords.find(record => record.stage === 2 && record.isComplete);

			if (stage2record && stage2record!.tickedDocuments && stage2record!.tickedDocuments!.length) {
				const attachedDocuments = await FormDocument.findAll({
					where: {
						id: {
							[Sequelize.Op.in]: stage2record!.tickedDocuments!.map(doc => doc.id),
						},
					}
				});

				for (const tickedDocument of attachedDocuments) {
					attachments.push({
						filename: tickedDocument.fileName,
						contentType: tickedDocument.mimeType,
						content: createReadStream(tickedDocument.path),
					});
				}
			}

			try {

				await MailService.sendEmail({
					to: params.emails,
					from: process.env.EMAIL_FROM || '<EMAIL>',
					subject: 'A response to your Feedback Form from Bethany Christian Care',
					template: 'final-report',
					context: {},
					attachments: attachments,
				});

			} catch (err) {
				console.error('Exception while sending email', err);
			}
		}

	}

	public static async deleteForm(req: JwtRequest) {
		const { id: formId, deleteReason } = req.body as IFormDeleteParams;

		const form = await Form.findByPrimary(formId);

		if (!form || form.isDeleted) {
			throw new NotFoundError('Form is not found');
		}

		const [recordsDeleted] = await Form.update({
			isDeleted: true,
			deleteReason,
			archivedAt: new Date(),
		}, {
			where: { id: formId }
		});

		return {
			deleted: recordsDeleted,
		};
	}

	public static async revertForm(req: JwtRequest) {
		const { id: formId } = req.body as IFormRevertParams;

		const form = await Form.findByPrimary(formId);

		if (!form || form.isDeleted) {
			throw new NotFoundError('Form is not found');
		}

		let lastRecord: FormRecord[] = [];

		if (!form.archivedAt) {
			lastRecord = await FormRecord.findAll({
				where: { formId: form.id },
				order: [['createdAt', 'desc']],
				limit: 1
			});

			if (lastRecord.length) {
				await lastRecord[0].destroy();
			}
		}

		lastRecord = await FormRecord.findAll({
			where: { formId: form.id },
			order: [['createdAt', 'desc']],
			limit: 1
		});

		form.assignedUserId = req.tokenData.user.id;
		form.setDataValue('archivedAt', null);

		if (lastRecord.length) {
			lastRecord[0].isComplete = false;
			form.stage = lastRecord[0].stage;

			await Promise.all([lastRecord[0].save(), form.save()]);
		} else {
			form.stage = 0;
			await form.save();
		}

		return { success: true };
	}

	public static async duplicateForm(req: JwtRequest) {

		const { id: formId, propertiesToClear } = req.body as IDuplicateFormParams;

		const form = await Form.findByPrimary(formId);

		if (!form || form.isDeleted) {
			throw new NotFoundError('Form is not found');
		}

		try {
			return await FormService.duplicateForm({
				form,
				propertiesToClear,
				createdByUserId: req.tokenData.user.id
			});
		} catch (e) {
			console.error('Exception in FormController.createForm', e);
			if (e instanceof HttpError) {
				throw e;
			} else {
				throw new ServerError();
			}
		}
	}

	private static async getChildFormsTree(parentFormId: number) {
		const signature = className + `.getChildFormsTree: FormId[${parentFormId}] `;

		const childForms = await Form.findAll({
			attributes: ['id', 'summary', 'archivedAt', 'categoryId'],
			where: {
				$or: [{
					parentFormId: parentFormId,
				}, {
					templateFormId: parentFormId
				}],
				isDeleted: false,
			},
			order: [['createdAt', 'asc']],
			logging: true
		});

		logger.silly(signature + `Found Total[${childForms.length}] child forms`);

		const childFormsJSON: {
			archivedAt: string | null,
			id: number,
			childForms?: any[]
		}[] = childForms.map(form => ({
			...omit(form.toJSON(), ['isAlertOverdue', 'isOverdue', 'archivedAt']),
			archivedAt: form.archivedAt ? form.archivedAt.toISOString() : null,
			id: form.id as number
		}));

		for (const childForm of childFormsJSON) {
			if (childForm.archivedAt) {
				childForm.childForms = await FormController.getChildFormsTree(childForm.id);
			}
		}

		return childFormsJSON;
	}

	/**
	 * Method to get the forms associated with a document without the
	 * default GroupId filtering to departments assigned to user in
	 * getForms method
	 **/
	public static async getFormsFromDocumentId(req: any) {

		const { id } = req.params as any;

		const whereClause: [{ [key: string]: any }] = [{
			'summary': {
				[Sequelize.Op.not]: null,
			}
		}];

		whereClause[0]['id'] = {
			[Sequelize.Op.in]: Sequelize.literal(
				`(
        SELECT formid
        FROM forms_record_property
        INNER JOIN forms_record
        ON forms_record_property.formRecordId = forms_record.id
        WHERE
        (
          propertyID = (
            SELECT id
            FROM properties
            WHERE \`name\` = 'documentId'
          )
          AND stringData = ${id}
          AND archivedAt IS NULL   
        )
      )`
			)
		};

		const form = await Form.findAll({
			where: {
				[Sequelize.Op.and]: whereClause
			},
			distinct: true,
			attributes: {
				include: [
					[Sequelize.literal('`Form`.`dueAt` < NOW() AND `Form`.`archivedAt` IS NULL'), 'isOverdue'],
					[Sequelize.literal('NOW() BETWEEN `Form`.`alertAt` AND `Form`.`dueAt` AND `Form`.`archivedAt` IS NULL'), 'isAlertOverdue'],
				]
			}, include: [
				{
					model: FormRecord,
					as: 'records',
					required: false,
					include: [{
						model: FormRecordProperty,
						required: false,
						include: [
							{
								model: Property,
								required: true
							},
						]
					}, {
						model: FormDocument,
						as: 'documents',
						required: true,
						through: {
							attributes: []
						},
						attributes: {
							exclude: ['path', 'userId'],
						},
					}],
				},
			]
		});

		return form;
	}

	public static async sendReviewFormNotifications(req: any) {
		const { userGroupId, stage, createdUserId, documentId, completedBy } = req.body as any;
		let createdAdmin: User | null = null,
			completedUser: User | null = null,
			document: Document | null = null;

		if (createdUserId) {
			createdAdmin = await User.findByPk(createdUserId);
		}

		if (completedBy) {
			completedUser = await User.findByPk(completedBy);
		}

		if (documentId) {
			document = await Document.findByPk(Number(documentId));
		}

		if (stage === 1) {
			const userToGroup = await UserToGroup.findAll({
				where: {
					groupId: userGroupId
				}
			});

			const users = await user.findAll({
				where: {
					id: {
						[Sequelize.Op.in]: userToGroup.map(usr => usr.userId)
					}
				}
			});
			const departmentHeads = users.filter(usr => usr.role === 'manager');

			let mailTo = departmentHeads.map(dh => dh.username).join();

			try {

				await MailService.sendEmail({
					to: mailTo,
					from: process.env.EMAIL_FROM || '<EMAIL>',
					subject: 'Document Review Module - Bethany Christian Care',
					template: 'document-review',
					context: {
						documentId: document ? document.name : documentId,
						completedBy: completedUser ? completedUser.lastName : 'the respective department',
						reviewStage: 'Stage 0'
					},
				});
				return { success: true, status: 'ok' }
			} catch (err) {
				console.error('Exception while sending email', err);
				return { success: false, status: 'email not sent' }
			}
		} else if (stage === 2) {
			;
			//Send email to sr.management heads and createdBy usr
			const seniorManagementGroupId = await UserGroup.findOne({
				where: {
					groupName: 'Senior Management'
				}
			});

			if (!seniorManagementGroupId) {
				throw new ServerError('Cannot find Senior Management department. Review notification not sent.');
			}

			const seniorManagementUserToGroup = await UserToGroup.findAll({
				where: {
					groupId: seniorManagementGroupId.id
				}
			});

			if (!seniorManagementUserToGroup) {
				throw new ServerError('Cannot find Senior Management department. Review notification not sent.');
			}

			const seniorManagementUsers = await user.findAll({
				where: {
					id: {
						[Sequelize.Op.in]: seniorManagementUserToGroup.map(usr => usr.userId)
					}
				}
			});

			if (!seniorManagementUsers) {
				throw new ServerError('Cannot fetch details from Senior Management department. Review notification not sent.');
			}

			const seniorManagementDepartmentHeads = seniorManagementUsers.filter(usr => usr.role === 'manager');

			let mailTo = seniorManagementDepartmentHeads.map(usr => usr.username).join();
			let createdAdminEmail = createdAdmin ? createdAdmin.username : null;

			// Scheduled review tasks will not have createdBy property so we only append it to mail list if the form has such proprty
			if (createdAdminEmail) {
				mailTo = mailTo + ',' + createdAdminEmail;
			}

			try {

				await MailService.sendEmail({
					to: mailTo,
					from: process.env.EMAIL_FROM || '<EMAIL>',
					subject: 'Document Review Module - Bethany Christian Care',
					template: 'document-review',
					context: {
						documentId: document ? document.name : documentId,
						completedBy: completedUser ? completedUser.lastName : 'the respective department',
						reviewStage: 'Document review'
					},
				});
				return { success: true, status: 'ok' }
			} catch (err) {
				console.error('Exception while sending email', err);
				return { success: false, status: 'email not sent' }
			}
		} else if (stage === 3) {
			//Send email to admin department head and createdBy user
			const adminGroupId = await UserGroup.findOne({
				where: {
					groupName: 'Admin'
				}
			});

			if (!adminGroupId) {
				throw new ServerError('Cannot fetch details from Admin department. Review notification not sent.');
			}

			const adminUserToGroup = await UserToGroup.findAll({
				where: {
					groupId: adminGroupId.id
				}
			});

			if (!adminUserToGroup) {
				throw new ServerError('Cannot fetch details from Admin department. Review notification not sent.');
			}

			const admins = await user.findAll({
				where: {
					id: {
						[Sequelize.Op.in]: adminUserToGroup.map(usr => usr.userId)
					},
					isArchived: 0
				}
			});

			const adminDepartmentHeads = admins.filter(usr => usr.role === 'manager');

			let mailTo = adminDepartmentHeads.map(usr => usr.username).join();
			let createdAdminEmail = createdAdmin ? createdAdmin.username : null;

			// Scheduled review tasks will not have createdBy property so we only append it to mail list if the form has such proprty
			if (createdAdminEmail) {
				mailTo = mailTo + ',' + createdAdminEmail;
			}
			console.log(mailTo);
			try {

				await MailService.sendEmail({
					to: mailTo,
					from: process.env.EMAIL_FROM || '<EMAIL>',
					subject: 'Document Review Module - Bethany Christian Care',
					template: 'document-review',
					context: {
						documentId: document ? document.name : documentId,
						completedBy: completedUser ? completedUser.lastName : 'the respective department',
						reviewStage: 'Senior Management review'
					},
				});
				return { success: true, status: 'ok' }
			} catch (err) {
				console.error('Exception while sending email', err);
				return { success: false, status: 'email not sent' }
			}
		} else if (stage === 4) {
			const mailTo = createdAdmin ? createdAdmin.username : null;
			if (!mailTo) {
				// This is a scheduled review we do not neeed to send review notification.
				return { success: true, status: 'ok' }
			} else {
				try {

					await MailService.sendEmail({
						to: mailTo,
						from: process.env.EMAIL_FROM || '<EMAIL>',
						subject: 'Document Review Module - Bethany Christian Care',
						template: 'document-review',
						context: {
							documentId: document ? document.name : documentId,
							completedBy: completedUser ? completedUser.lastName : 'the respective department',
							reviewStage: 'Final Admin review'
						},
					});
					return { success: true, status: 'ok' }
				} catch (err) {
					console.error('Exception while sending email', err);
					return { success: false, status: 'email not sent' }
				}
			}

		} else if (stage === 5) {
			const mailTo = createdAdmin ? createdAdmin.username : null;
			if (!mailTo) {
				// This is a scheduled review we do not neeed to send review notification.
				return { success: true, status: 'ok' }
			} else {
				try {

					await MailService.sendEmail({
						to: mailTo,
						from: process.env.EMAIL_FROM || '<EMAIL>',
						subject: 'Document Review Module - Bethany Christian Care',
						template: 'document-review',
						context: {
							documentId: document ? document.name : documentId,
							completedBy: completedUser ? completedUser.lastName : 'the respective department',
							reviewStage: 'Admin processing'
						},
					});
					return { success: true, status: 'ok' }
				} catch (err) {
					console.error('Exception while sending email', err);
					return { success: false, status: 'email not sent' }
				}
			}

		} else {
			return { success: false, status: 'email not sent' }
		}
	}
}
