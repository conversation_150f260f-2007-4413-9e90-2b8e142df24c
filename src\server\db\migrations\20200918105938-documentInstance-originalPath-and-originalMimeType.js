'use strict';

module.exports = {
	up: async (queryInterface, Sequelize) => {

		const tableDefinition = await queryInterface.describeTable('document_instance');

		if (!tableDefinition['originalPath']) {
			await queryInterface.addColumn('document_instance', 'originalPath', {
				type: Sequelize.STRING,
				allowNull: false,
			})
		}

		if (!tableDefinition['originalMimeType']) {
			await queryInterface.addColumn('document_instance', 'originalMimeType', {
				type: Sequelize.STRING,
				allowNull: false,
			})
		}
	},

	down: (queryInterface, Sequelize) => {
		return Promise.all([
			queryInterface.removeColumn('document_instance', 'originalPath'),
			queryInterface.removeColumn('document_instance', 'originalMimeType')
		])
	}
};
