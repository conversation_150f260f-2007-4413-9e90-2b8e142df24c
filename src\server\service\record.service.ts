import { IFormOutputModel, IFormRecordParams } from '../../common/contracts/form';
import Form from '../db/models/form.model';
import { NotFoundError, ServerError } from '../../model/HttpErrors';
import FormRecord from '../db/models/formRecord.model';
import Property from '../db/models/property.model';
import FormRecordProperty from '../db/models/formRecordProperty.model';
import { isNil } from 'lodash';
import FormDocument from '../db/models/formDocument.model';
import FormCategory from '../db/models/category.model';
import { DocumentDRMService } from './documentDRM.service';
import { IRecordPropertyType } from '../../common/contracts/recordProperty';
import logger from '../utils/Logger';

const className = "RecordService";

class RecordServiceClass {
	public className = "RecordServiceClass";
	public async createRecord(data: IFormRecordParams, createdById: number | null) {
		const signature = className + ".createRecord: ";

		// creating a new form
		const {
			formId,
			properties,
			stage,
			documents,
			isComplete,
		} = data;

		// Get the form, ensuring it exists
		const form = await Form.findByPrimary<Form>(formId);

		if (!form) {
			logger.error(signature + `Could not find Form[${formId}] for new record`);
			throw new NotFoundError('The form is not found');
		}

		const createParams: Partial<FormRecord> = {
			formId,
			createdById,
			stage,
			isComplete
		};

		// Find an existing record (if one exists) and use that to indicate the new sequence
		const existingRecord: FormRecord[] = await FormRecord.findAll<FormRecord>({
			limit: 1,
			where: {
				formId
			},
			order: [
				['sequence', 'DESC']
			]
		});

		if (existingRecord.length && existingRecord.length > 0) {
			createParams.sequence = existingRecord[0].sequence + 1;
			logger.debug(signature + `Found existing records in Form[${formId}]. New Record will bet set to Sequence[${createParams.sequence}]`);
		} else {
			createParams.sequence = 1;
			logger.silly(signature + `Did not find existing record in Form[${formId}]. New Record will bet set to Sequence[${createParams.sequence}]`);
		}

		const record = await FormRecord.create(createParams);
		logger.silly(signature + `Created Record[${record.id}] for Form[${formId}]`);

		// add many to many relation for record and documents
		await Promise.all([
			(record as any).setDocuments(documents.map(doc => doc.id)),
			(record as any).setTickedDocuments(documents.filter(doc => doc.isTicked).map(doc => doc.id)),
		]);

		// Now create all the properties and attach them to the record

		if (createParams.sequence === 1 || (createParams.isComplete && createParams.stage === 0)) {
			// Check that we have a userGroupId property, if not, get it from the form
			const userGroupIdProperty = properties.find(property => property.name === 'userGroupId');
			if (!userGroupIdProperty && form.userGroupId) {
				properties.push({
					name: 'userGroupId',
					intData: form.userGroupId
				} as any);
			}
		}

		// Check and if needed, insert the property names
		const upsertPromises: Promise<Property>[] = properties.map(property =>
			(Property.findOrCreate<Property>({
				where: {
					name: property.name
				}
			}).then((result: [Property, Boolean]) => result[0])) as any as Promise<Property>);

		const upsertResults: Property[] = await Promise.all(upsertPromises);

		// Now push those id's into txhe properties array
		const formRecordPropertyPayloads: Array<Partial<FormRecordProperty>> = properties.map(property => {
			const propertyId = upsertResults.find((prop: Property) => prop.name === property.name);

			if (!propertyId) {
				throw new ServerError(`Could not find property[${property.name}] in database. This error should never occur`);
			}

			const createData: Partial<FormRecordProperty> = {
				formRecordId: record.id,
				propertyId: propertyId.id
			};

			['stringData', 'intData', 'jsonData', 'enumId', 'dateData'].forEach(dt => {
				if (!isNil(property[dt])) {
					createData[dt] = property[dt];
				}
			});

			return createData;

		});

		await FormRecordProperty.bulkCreate(formRecordPropertyPayloads);

		/**
		 * if this record is on stage 0 - update summary in the forms table, if the form doesn't already have a summary
		 */
		if (stage === 0 && (!form.summary || form.summary.length === 0)) {
			let category: FormCategory | null = null;
			if (form.categoryId) {
				category = await FormCategory.findByPk(form.categoryId);
			}

			if (category && category.name === 'DRM-Task') {
				const docProp = properties.find(prop => prop.name === 'documentId');

				if (docProp && docProp.stringData && docProp.stringData.length) {
					await DocumentDRMService.updateAllFormSummaries(Number(docProp.stringData));
				}
			} else {
				const summaryProperty = properties.find(prop => prop.name === 'summary');
				if (summaryProperty) {
					await Form.update(
						{ summary: summaryProperty.stringData },
						{ where: { id: formId } }
					);
				} else if (!form.summary || !form.summary.length) {
					console.log('Cannot find summary property in the record to update summary of the form');
				}
			}
		}

		return record;
	}

	public async getFirstRecordForDuplication(formId: number): Promise<FormRecord | null> {
		return new Promise((resolve, reject) => {
			FormRecord.findAll({
				where: {
					formId: formId,
					stage: 0
				},
				order: [
					['sequence', 'DESC']
				],
				include: [{
					model: FormRecordProperty,
					required: true,
					include: [{
						model: Property,
						required: true,
					}],
				}, {
					model: FormDocument,
					as: 'documents',
					required: false,
					through: {
						attributes: []
					},
					attributes: {
						exclude: ['path', 'userId'],
					},
				}, {
					model: FormDocument,
					as: 'tickedDocuments',
					required: false,
					through: {
						attributes: []
					},
					attributes: ['id'],
				}]
			})
				.then(targetRecords => {
					if (!targetRecords || !targetRecords.length) {
						resolve(null);
					}

					resolve(targetRecords[0]);
				}).catch(err => {
					reject(err);
				});
		});
	}

	public lastStage(form: IFormOutputModel, stage: number = 1, sequence?: number): typeof form.records[number] | undefined {
		return form.records
			.filter(record => {
				if (typeof sequence === 'number' && typeof stage === 'number')
					return record.sequence === sequence && record.stage === stage
				else if (typeof sequence === 'number')
					return record.sequence === sequence
				else if (typeof stage === 'number')
					return record.stage === stage
				return true;
			})
			.sort((a, b) => a.sequence > b.sequence ? 1 : -1)
			.pop();
	}

	/**
	 * @description Safely fetches a property from the supplied record by fieldName
	 * @param {IRecordWithDateOutputModel | null} record 
	 * @param {string | null} field 
	 * @returns {IRecordPropertyType | null}
	 */
	public getFieldInRecord(record: { id: number, properties: IRecordPropertyType[] } | FormRecord | null | undefined, field: string | null | undefined): IRecordPropertyType | FormRecordProperty | null {
		const signature = this.className + ".getFieldInRecord: ";

		if (!record) {
			logger.info(signature + `Returning null due to falsey record`);
			return null;
		}

		if (!field) {
			logger.info(signature + `Returning null due to falsey fieldName`);
			return null;
		}

		if (!record.properties) {
			logger.info(signature + `Returning null due to falsey record.properties on Record[${record.id || 'New Record'}]`);
			return null;
		}

		const lCaseField = field.toLowerCase();
		const property = (<IRecordPropertyType[]>record.properties).find(recordProperty => {
			if (!recordProperty.property) { return false; }
			const propertyName = recordProperty.property.name.toLowerCase();
			if (propertyName === lCaseField) { return true; }
			return false;
		});

		logger.info(signature + `${property ? 'Found' : 'Did not find'} recordProperty by Name[${field}] in Record[${record.id || 'New Record'}]`);
		return property || null;
	}

	/**
	 * @description Returns all of the formRecords that belong to any given form
	 * @param {number} formId 
	 * @returns {FormRecord[]}
	 */
	public async getAllRecordsForForm(formId: number): Promise<{ rows: FormRecord[], count: number }> {
		const result = await FormRecord.findAndCountAll({
			where: { formId },
			include: [{
				model: FormRecordProperty,
				required: false,
				include: [
					{
						model: Property,
						required: true
					},
				]
			}, {
				model: FormDocument,
				as: 'documents'
			}, {
				model: FormDocument,
				as: 'tickedDocuments'
			}]
		});

		return result;
	}
}

export const RecordService = new RecordServiceClass();
