import SettingsModel from '../db/models/settings.model';
import { IUpdateSettingsParams } from '../../common/contracts/settings';
import { ACTUAL_SETTINGS_KEY, systemSettingsService } from '../service/settings.service';
import { FormService } from '../service/form.service';

export class SettingsController {

  public static async getSettings(req: JwtRequest) {
    return systemSettingsService.getSettings();
  }

  public static async updateSettings(req: JwtRequest) {

    const settings: IUpdateSettingsParams = req.body as IUpdateSettingsParams;

    if( settings.processTemplates ) {
      settings.processTemplates = undefined;

      await FormService.processFormSchedules();
    }

	if( settings.processDocumentReviews ) {
		settings.processDocumentReviews = undefined;

		await FormService.processDocumentReviewSchedules();
	}

    const recordsUpdated = await SettingsModel.update(settings, {
      where: {
        key: ACTUAL_SETTINGS_KEY,
      }
    });

    return {status: 'ok', recordsUpdated};

  }

}
