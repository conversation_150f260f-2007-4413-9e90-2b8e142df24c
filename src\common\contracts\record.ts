import * as t from 'io-ts';
import { constructDocumentType } from './document';
import { RecordPropertyType } from './recordProperty';
import { DateType } from './date';
import { ISODateRegEx } from '../constants';

export function constructRecordType<T>(dateType: t.Type<T>) {
  return t.type({
    id: t.number,
    formId: t.number,
    createdById: t.union([t.number, t.null]),
    stage: t.number,
    sequence: t.number,
    createdAt: dateType,
    documents: t.array(constructDocumentType<T>(dateType)),
    tickedDocuments: t.union([
      t.undefined,
      t.array(t.interface({id: t.number})),
    ]),
    properties: t.array(RecordPropertyType),
    isComplete: t.boolean,
    createdByUser: t.union([
		t.interface({
			id: t.number,
			firstName: t.string,
			lastName: t.string,
		}),
		t.null
	])
  });
}

const RecordWithDateOutputModel = constructRecordType(DateType);

export interface IRecordWithDateOutputModel extends t.TypeOf<typeof RecordWithDateOutputModel> {}

export const UpdateRecordDateParams = t.interface({
  recordId: t.number,
  date: t.refinement(t.string, v => ISODateRegEx.test(v), 'date in ISO format'),
});

export interface IUpdateRecordDateParams extends t.TypeOf<typeof UpdateRecordDateParams> { }
