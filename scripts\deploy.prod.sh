set -o xtrace

#!/usr/bin/env bash
ROOT_DIR=/var/www/bethany
SRC_DIR=$ROOT_DIR/source
REL_DIR=$ROOT_DIR/releases

# Create the directory if it doesn't exist
mkdir -p $ROOT_DIR;
mkdir -p SRC_DIR;
mkdir -p $REL_DIR;

# Create a unique name for the release
DATE=`date +%Y%m%d%H%M%S%N`

# Create the unique release directory
mkdir $REL_DIR/$DATE;

# Copy the contents of build to the release directory
cp -r $SRC_DIR/build $REL_DIR/$DATE;
cp -r $SRC_DIR/node_modules $REL_DIR/$DATE;

# Create or Overwrite the a symlink for the current release number
ln -sfn $REL_DIR/$DATE $ROOT_DIR/current
ln -sfn $REL_DIR/$DATE/node_modules $ROOT_DIR/node_modules

# Reset working directory
cd $ROOT_DIR

# Copy the ecoSystem File to the Root Dir
cp $SRC_DIR/ecosystem.config.js $ROOT_DIR/ecosystem.config.js

# Ensure we have pm2 ready to go
npm i pm2 -g

# Start or restart the application
pm2 startOrRestart ecosystem.config.js --env production

#Perform Cleanup

# Set the working Directory
cd $REL_DIR

# Delete anything beyond the last 5th deployment
ls -dt */ | tail -n +6 | xargs rm -rf