'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDefinition = await queryInterface.describeTable('documents');

    if (tableDefinition['standard']) {
      return;
    }

    return queryInterface.addColumn(
      'documents', // table name
      'standard', // new field name
      {
        type: Sequelize.INTEGER,
      },
    )
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('documents', 'standard')
  }
};
