import * as React from 'react';
import { Readable } from 'stream';
import ReactPDF from "@react-pdf/renderer";
import { Sequelize } from 'sequelize-typescript';
import { FormsPaginationResponse, IFormOutputValidationModel, IFormsQueryParams } from "../../common/contracts/form";
import { EnumController } from '../controller/enum.controller';
import FormLocation from '../db/models/formLocation.model';
import UserGroup from '../db/models/group.model';
import FormGroup from '../db/models/group.model';
import User from '../db/models/user.model';
import { IRecordWithDateOutputModel } from '../../common/contracts/record';
import { ReadOnlyFormsReport } from '../reports/report-forms.report';
import { FollowUpFormsReport } from '../reports/follow-up-forms.report';
import { ServerError } from "../../model/HttpErrors";
import { formatDate, replaceNewLineWithSpace } from "../reports/dashboard-pdf.report";
import FormCategory from "../db/models/category.model";
import { AuditFormsReport } from '../reports/audit-forms.report';
import { RecordService } from './record.service';
import { UserGroupService } from './userGroup.service';
import { EnumService } from './enum.service';
import Risks from '../db/models/risk.model';
import { riskTypeMap } from '../../app/model/RiskType';

export class ReportService {

  public static async generateReportToStream(form: IFormOutputValidationModel): Promise<NodeJS.ReadableStream> {

    const [originatorTypes, reportFormTypes, auditFormTypes, location] = await Promise.all([
      EnumController.getEnumsByName({ params: { enumName: 'OriginatorType' } } as unknown as JwtRequest),
      EnumController.getEnumsByName({ params: { enumName: 'ReportFormType' } } as unknown as JwtRequest),
      EnumController.getEnumsByName({ params: { enumName: 'AuditFormType' } } as unknown as JwtRequest),
      form.formLocationId ? FormLocation.findByPrimary(form.formLocationId) : Promise.resolve(null),
    ]);

    const relevantRecords: IRecordWithDateOutputModel[] = form.records.filter(record => {
      if (!record.isComplete) return false;

      // If this record was just to reassign it to somebody else, it should not be in the final view
      if (record.properties && record.properties.length) {
        for (let i = 0; i < record.properties.length; i++) {

          let prop = record.properties[i];
          if (prop.property.name === 'reassignToUserId' && prop.intData)
            return false;
        }
      }

      return true;
    });

    /*
      Sort the completed records in reverse
     */
    relevantRecords.sort((a, b) => a.sequence > b.sequence ? -1 : 1);

    /*
      Take the first (last) submission for each stage by each user
     */
    let completeRecords = relevantRecords.filter(
      (value: IRecordWithDateOutputModel, index: number, self: IRecordWithDateOutputModel[]): boolean => {
        let existing = (self.find(record => record.stage === value.stage && record.createdById === value.createdById));

        if (existing)
          return existing.sequence === value.sequence;

        return true;
      }
    );

    // Sort the completed records back into the correct order
    completeRecords.sort((a, b) => a.sequence > b.sequence ? 1 : -1);

    if (form.category.name === 'Report') {
      /**
       * prepare data for reports in advance
       */
      const followUpGroupIdsMap = ReportService.getFollowUpsGroupIdsMap(completeRecords, [1, 2]);
      const reassignedUserIdsMap = ReportService.getReassignUserIdsMap(completeRecords, [1, 2]);

      /**
       * get names of all user groups for follow-ups and fill names of reassigned users
       **/
      const [followUpGroupMap, reassignedUserMap] = await Promise.all([
        ReportService.getGroupNamesMap(followUpGroupIdsMap),
        ReportService.getUserNamesMap(reassignedUserIdsMap),
      ]);

      /**
       * if stage 2 has follow-ups we need to push them into stage 1 follow ups
       */

      ReportService.takeFollowUpsFromLaterStages(completeRecords, 1, 2);

      /**
       * render PDF to output stream
       */
      const pdfStream = ReactPDF.renderToStream(<ReadOnlyFormsReport
        formData={form}
        originatorTypes={originatorTypes || []}
        reportFormTypes={reportFormTypes || []}
        location={location}
        records={completeRecords}
        followUpGroupMap={followUpGroupMap}
        reassignedUserMap={reassignedUserMap}
      />);

      return pdfStream;

    } else if (form.category.name === 'Follow-Up') {

      const reassignedUserIdsMap = ReportService.getReassignUserIdsMap(completeRecords, [1, 2]);
      const followUpGroupIdsMap = ReportService.getFollowUpsGroupIdsMap(completeRecords, [0, 1]);

      /**
       * get names of all user groups for follow-ups and fill names of reassigned users
       **/
      const [followUpGroupMap, reassignedUserMap] = await Promise.all([
        ReportService.getGroupNamesMap(followUpGroupIdsMap),
        ReportService.getUserNamesMap(reassignedUserIdsMap),
      ]);

      ReportService.takeFollowUpsFromLaterStages(completeRecords, 0, 1); // TODO: check if this is correct logic

      const pdfStream = ReactPDF.renderToStream(<FollowUpFormsReport
        formData={form}
        records={completeRecords}
        location={location}
        reportFormTypes={reportFormTypes || []}
        followUpGroupMap={followUpGroupMap}
        reassignedUserMap={reassignedUserMap}
      />);

      return pdfStream;
    } else if (form.category.name === 'Audit') {
      const reassignedUserIdsMap = ReportService.getReassignUserIdsMap(completeRecords, [1, 2]);
      const followUpGroupIdsMap = ReportService.getFollowUpsGroupIdsMap(completeRecords, [0, 1]);
      const userGroupIdsMap = ReportService.getGroupIdsMap(completeRecords, 'department', [0, 1]);

      /**
       * get names of all user groups for follow-ups and fill names of reassigned users
       **/
      const [followUpGroupMap, reassignedUserMap, userGroupMap] = await Promise.all([
        ReportService.getGroupNamesMap(followUpGroupIdsMap),
        ReportService.getUserNamesMap(reassignedUserIdsMap),
        ReportService.getGroupNamesMap(userGroupIdsMap),
      ]);

      ReportService.takeFollowUpsFromLaterStages(completeRecords, 0, 1); // TODO: check if this is correct logic

      const pdfStream = ReactPDF.renderToStream(<AuditFormsReport
        formData={form}
        records={completeRecords}
        location={location}
        auditFormTypes={auditFormTypes || []}
        followUpGroupMap={followUpGroupMap}
        reassignedUserMap={reassignedUserMap}
        userGroupMap={userGroupMap}
      />);

      return pdfStream;

    } else {
      console.log('Unknown report type' + form.category.name);
      throw new ServerError();
    }

  }

  /**
   * this function takes 'followUps' property from record with stage equals 'fromStage' and copies it to records with 'targetStage'
   * @param records - array of records
   * @param targetStage - int
   * @param fromStage - int
   */
  private static takeFollowUpsFromLaterStages(records: IRecordWithDateOutputModel[], targetStage: number, fromStage: number) {
    let futureStageRecords = records.filter(record => record.stage === fromStage);

    if (futureStageRecords && futureStageRecords.length > 0) {
      let followUpRecord;
      for (let futureStageRecord of futureStageRecords) {
        followUpRecord = futureStageRecord.properties.find(recordProperty => recordProperty.property.name === "followUps");
        if (followUpRecord) {
          const targetRecords = records.filter(rec => rec.stage === targetStage && rec.sequence < futureStageRecord.sequence);
          for (const targetRecord of targetRecords) {
            if (targetRecord) {
              const furProperty = targetRecord.properties.find(prop => prop.property.name === 'followUps');
              if (furProperty) {
                furProperty.jsonData = followUpRecord.jsonData;
              } else {
                // @ts-ignore
                targetRecord.properties.push({
                  id: 0,
                  property: { id: 0, name: 'followUps' },
                  jsonData: followUpRecord.jsonData,
                });
              }
            }
          }
          break;
        }
      }
    }
  }

  private static getFollowUpsGroupIdsMap(records: IRecordWithDateOutputModel[], stages: number[]): { [n: number]: true } {
    const resultMap: { [n: number]: true } = {};
    for (let record of records) {
      if (stages.includes(record.stage)) { //prefetch additional data for stage 1 and 2
        const followUpsProperty = record.properties.find(prop => prop.property.name === 'followUps');
        if (followUpsProperty && followUpsProperty.jsonData) {
          const followUps = JSON.parse(followUpsProperty.jsonData);
          if (!followUps || !Array.isArray(followUps)) {
            console.log('Invalid follow up json data');
          } else {
            followUps.map(fu => fu.userGroupId && parseInt(fu.userGroupId, 10)).forEach(id => {
              if (id) {
                resultMap[id] = true;
              }
            });
          }
        }
      }
    }
    return resultMap;
  }

  private static getGroupIdsMap(records: IRecordWithDateOutputModel[], propertyName: string = 'userGroupId', stages: number[]): { [n: number]: true } {
    const resultMap: { [n: number]: true } = {};
    for (let record of records) {
      if (stages.includes(record.stage)) {
        const prop = record.properties.find(prop => prop.property.name === propertyName);
        if (prop && prop.intData) {
          resultMap[prop.intData] = true;
        }
      }
    }
    return resultMap;
  }

  private static getReassignUserIdsMap(records: IRecordWithDateOutputModel[], stages: number[]): { [n: number]: true } {
    const resultMap: { [n: number]: true } = {};
    for (let record of records) {
      if (stages.includes(record.stage)) {
        const reassignToUserProperty = record.properties.find(prop => prop.property.name === 'reassignToUserId');
        if (reassignToUserProperty && reassignToUserProperty.intData) {
          resultMap[reassignToUserProperty.intData] = true;
        }
      }
    }
    return resultMap;
  }

  private static async getUserNamesMap(userIdsMap: { [n: number]: true }): Promise<{ [n: number]: string }> {
    const userIds = Object.keys(userIdsMap).map(id => parseInt(id, 10));
    const resultMap: { [n: number]: string } = {};
    if (userIds.length) {
      const users = await User.findAll({
        where: {
          id: {
            [Sequelize.Op.in]: [userIds]
          }
        },
        attributes: ['id', 'firstName', 'lastName'],
      });
      users.forEach(user => {
        resultMap[user.id] = user.firstName + ' ' + user.lastName;
      });
    }
    return resultMap;
  }

  private static async getGroupNamesMap(groupIdsMap: { [n: number]: true }): Promise<{ [n: number]: string }> {
    const groupIds = Object.keys(groupIdsMap).map(id => parseInt(id, 10));
    const resultMap: { [n: number]: string } = {};
    if (groupIds.length) {
      const groups = await UserGroup.findAll({
        where: {
          id: {
            [Sequelize.Op.in]: [groupIds]
          }
        },
        attributes: ['id', 'groupName'],
      });
      for (let group of groups) {
        resultMap[group.id] = group.groupName;
      }
    }
    return resultMap;
  }

  private static getFormUrl = (categoryName: string, id: string | number) => {
    if (categoryName === 'Risk') {
      return `${process.env.FRONT_HOST}/risks/${id}`;
    }

    if (categoryName === 'Report' || categoryName === 'Feedback') {
      return `${process.env.FRONT_HOST}/report/${id}`;
    }
    
    if (categoryName === 'Follow-Up') {
      return `${process.env.FRONT_HOST}/followUp/${id}`;
    }
    
    if (categoryName === 'Audit') {
      return `${process.env.FRONT_HOST}/audit/${id}`;
    }
    
    if (categoryName === 'Risk Task') {
      return `${process.env.FRONT_HOST}/risk-assessment-task/${id}`;
    }

    if(categoryName === 'DRM-Task') {
      return `${process.env.FRONT_HOST}/document-review-task/${id}`;
    }
    
    console.error(`Unknown Form Type ${categoryName}. Unable to resolve target url`);
    return '';
  }

  public static async generateDashboardCsvToStream(queryParams: IFormsQueryParams, data: FormsPaginationResponse, creatorUserId: number): Promise<NodeJS.ReadableStream> {

    const csvStream = new Readable({
      read() { }
    });

    let filterAssignedUser: string | null = null;
    if (queryParams.assignedUserId) {
      const assignedUser = await User.findByPrimary(queryParams.assignedUserId, {
        attributes: ['firstName', 'lastName'],
      });
      if (assignedUser) {
        filterAssignedUser = assignedUser.firstName + ' ' + assignedUser.lastName;
      }
    }

    const creatorUser = await User.findByPrimary(creatorUserId, {
      attributes: ['firstName', 'lastName', 'role'],
    });
    if (!creatorUser) {
      throw new ServerError('Cannot get current user info');
    }
    const creatorUserName: string = creatorUser.firstName + ' ' + creatorUser.lastName;
    const isAdminOrManager: boolean = creatorUser.role === 'administrator' || creatorUser.role === 'manager';

    let filterLocationName: string | null = null;
    if (queryParams.locationId) {
      const location = await FormLocation.findByPrimary(queryParams.locationId, {
        attributes: ['name'],
      });
      if (location) {
        filterLocationName = location.name;
      }
    }

    let filterGroupName: string | null = null;
    if (queryParams.groupId) {
      const group = await FormGroup.findByPrimary(queryParams.groupId, {
        attributes: ['groupName'],
      });
      if (group) {
        filterGroupName = group.groupName;
      }
    }

    let categoryName: string | null = null;
    if (queryParams.category) {
      const category = await FormCategory.findByPrimary(queryParams.category, {
        attributes: ['name'],
      });
      if (category) {
        categoryName = category.name + 's'; // plural
      }
    }

    csvStream.push(`"Created at ${formatDate(new Date(), 'DD-MM-YY, HH:mm')}",,,,,\n`);
    csvStream.push(`"Welcome ${creatorUserName}",,,,,\n\n`);
    csvStream.push(`"Overdue: ${data.overdueTasks}","Requires Attention: ${data.alertTasks}","Remaining: ${data.remainingTasks}",,,\n\n`);
    csvStream.push(`"Search: ${queryParams.search || '-'}","Category: ${categoryName || 'All Categories'}",,,,\n`);
    csvStream.push(`"Assigned User: ${filterAssignedUser || '-'}","Location: ${filterLocationName || '-'}","Department: ${filterGroupName || '-'}",,,\n`);

    let dateFilter = '';
    if (queryParams.createdAtStart || queryParams.createdAtEnd) {
      dateFilter = `Filter by created date: from ${queryParams.createdAtStart ? formatDate(queryParams.createdAtStart) : '-'
        } to ${queryParams.createdAtEnd ? formatDate(queryParams.createdAtEnd) : '-'}`;
    } else if (queryParams.dueAtStart || queryParams.dueAtEnd) {
      dateFilter = `Filter by due date: from ${queryParams.dueAtStart ? formatDate(queryParams.dueAtStart) : '-'
        } to ${queryParams.dueAtEnd ? formatDate(queryParams.dueAtEnd) : '-'}`;
    }

    if (dateFilter) {
      csvStream.push(`"${dateFilter}",`);
    }

    if (isAdminOrManager) {
      csvStream.push(`"Show only: ${queryParams.showAll || 'active'}",,,,${!dateFilter ? ',' : ''}\n\n`);
    } else {
      csvStream.push(`${dateFilter ? ',,,,\n\n' : '\n'}`);
    }

    const headerRow = `"Issue number","Form Type","Feedback Type","Summary","Location","Assigned To","Date Created","Due Date","Completed Date","Deleted Date","Originator Type","Originator Name","Department","Unit number","Email Address","Deleted Reason","Link"`;

    csvStream.push(`${headerRow}\n\n`);

    const dateText = (date: Date | string | null) => {
      if (date) {
        return formatDate(date, "DD-MM-YY");
      }

      return "";
    }

    /** Rapidly load every record */
    // const firstRecords = await Promise.all( data.items.map( formEntry => RecordService.getFirstRecordForDuplication(formEntry.id) ) );

    console.log(`Writing Records[${data.items.length}] to CSV`);

    for (let i = 0; i < data.items.length; i++) {
      const formEntry = data.items[i];

      const formData = {
        "Form Type": formEntry.category.name,
        "Feedback Type": "",
        Summary: formEntry.summary,
        Location: formEntry.formLocation && formEntry.formLocation.name || '',
        "Assigned To": "",
        "Originator Name": "",
        "Department": "",
        "Originator Type": "",
        "Unit number": "",
        "Date Created": dateText(formEntry.createdAt),
        "Due Date": dateText(formEntry.dueAt),
        "Issue number": formEntry.issueNumber,
        "Email Address": "",
        "Completed Date": dateText(formEntry.archivedAt),
        "Deleted Date": formEntry.isDeleted ? dateText(formEntry.archivedAt) : '',
        "Deleted Reason": formEntry.deleteReason || '',
        "Link": this.getFormUrl(formEntry.category.name, formEntry.id )
      };


      // Get last stage1 record
      const lastStage0 = RecordService.lastStage(formEntry, 0, 1);
      if (lastStage0) {
        const feedbackType = RecordService.getFieldInRecord(lastStage0, "reportFormType");
        if (feedbackType) {
          const feedbackTypeEnum = await EnumService.getEnumById(feedbackType.enumId);
          if (feedbackTypeEnum) {
            formData['Feedback Type'] = feedbackTypeEnum.value;
          }
        }

        const originalDepartmentId = RecordService.getFieldInRecord(lastStage0, 'userGroupId');
        if (originalDepartmentId) {
          const group = await UserGroupService.getUserGroupById(originalDepartmentId.intData);
          if (group) {
            formData.Department = group.groupName;
          }
        }

        const originatorField = RecordService.getFieldInRecord(lastStage0, 'originatorName');
        if (originatorField) {
          formData['Originator Name'] = originatorField.stringData || '';
        }

        const residentField = RecordService.getFieldInRecord(lastStage0, 'residentName');
        if (residentField && residentField.stringData && residentField.stringData.length) {
          if (formData['Originator Name'].length > 0) {
            formData['Originator Name'] += " (" + residentField.stringData + ")";
          } else {
            formData['Originator Name'] = "Unknown Originator (" + residentField.stringData + ")";
          }
        }


        if (formEntry.assignedUser) {
          formData['Assigned To'] = formEntry.assignedUser.firstName;
          if (formData['Assigned To'].length > 0)
            formData['Assigned To'] += " ";
          formData['Assigned To'] = formEntry.assignedUser.lastName;
        } else if (formEntry.userGroup) {
          formData['Assigned To'] = formEntry.userGroup.groupName + ' Department';
        }

        const originatorTypeField = RecordService.getFieldInRecord(lastStage0, 'originatorTypeId');
        if (originatorTypeField) {
          const originatorEnum = await EnumService.getEnumById(originatorTypeField.enumId);
          if (originatorEnum) {
            formData['Originator Type'] = originatorEnum.value;
          }
        }

        const residentRoomNumberField = RecordService.getFieldInRecord(lastStage0, 'residentRoomNumber');
        if (residentRoomNumberField) {
          formData['Unit number'] = residentRoomNumberField.stringData || '';
        }

        const residentEmail = RecordService.getFieldInRecord(lastStage0, 'residentEmail');
        if (residentEmail) {
          formData['Email Address'] = residentEmail.stringData || '';
        }
      }

      csvStream.push(`${headerRow.split(",").map(quotedHeader => {
        const headerKey = quotedHeader.replace(/(^")|("$)/g, '');

        if (Object.prototype.hasOwnProperty.call(formData, headerKey)) {
          return "\"" + replaceNewLineWithSpace(formData[headerKey]).replace(/"/g, '""') + "\"";
        } else {
          return "\"\"";
        }
      }).join(",")}\n`);
    }

    if (data.items.length === 0) {
      csvStream.push(`"No Results"`);
    }

    csvStream.push(null);

    return csvStream;

  }

  public static async generateRiskCsvToStream(queryParams: IFormsQueryParams, data: { items: Risks[], totalCount: number }, creatorUserId: number): Promise<NodeJS.ReadableStream> {

    const csvStream = new Readable({
      read() { }
    });

    const headerRow = `"RiskId","Name","Risk Type","Location","Resident Name","Risk Level","Risk Status","Group Name","Next Assessment","Last Assessment","Initial Assessment","Link"`;

    csvStream.push(`${headerRow}\n\n`);
    const dateText = (date: Date | string | null) => {
      if (date) {
        return formatDate(date, "DD-MM-YY");
      }

      return "";
    }

    /** Rapidly load every record */
    // const firstRecords = await Promise.all( data.items.map( formEntry => RecordService.getFirstRecordForDuplication(formEntry.id) ) );

    console.log(`Writing Records[${data.items.length}] to CSV`);
    for (let i = 0; i < data.items.length; i++) {
      const formEntry = JSON.parse(JSON.stringify(data.items[i]));
      const formData = {
        "RiskId": String(formEntry.id)||'N/A',
        "Name": formEntry.summary,
        "Risk Type": riskTypeMap[formEntry['riskTypeId']],
        Location: formEntry.form_locations.name,
        "Resident Name": formEntry.residentName,
        "Risk Level": formEntry.riskLevel,
        "Risk Status": formEntry.riskStatus,
        "Next Assessment": dateText(formEntry.dateOfNextAssessment),
        "Last Assessment": dateText(formEntry.dateOfLastAssessment),
        "Group Name": formEntry.groups.groupName,
        "Initial Assessment": dateText(formEntry.createdAt),
        "Link": this.getFormUrl('Risk', formEntry.id )
      };

      console.log(formData);

      csvStream.push(`${headerRow.split(",").map(quotedHeader => {

        const headerKey = quotedHeader.replace(/(^")|("$)/g, '');

        if (Object.prototype.hasOwnProperty.call(formData, headerKey)) {
          return "\"" + replaceNewLineWithSpace(formData[headerKey] || '').replace(/"/g, '""') + "\"";
        } else {
          return "\"\"";
        }
      }).join(",")}\n`);
    }

    if (data.items.length === 0) {
      csvStream.push(`"No Results"`);
    }

    csvStream.push(null);

    return csvStream;

  }



  public static async generateDRMDashboardCsvToStream(queryParams: IFormsQueryParams, data: FormsPaginationResponse, creatorUserId: number): Promise<NodeJS.ReadableStream> {

    const csvStream = new Readable({
      read() { }
    });

    let filterAssignedUser: string | null = null;
    if (queryParams.assignedUserId) {
      const assignedUser = await User.findByPrimary(queryParams.assignedUserId, {
        attributes: ['firstName', 'lastName'],
      });
      if (assignedUser) {
        filterAssignedUser = assignedUser.firstName + ' ' + assignedUser.lastName;
      }
    }

    const creatorUser = await User.findByPrimary(creatorUserId, {
      attributes: ['firstName', 'lastName', 'role'],
    });
    if (!creatorUser) {
      throw new ServerError('Cannot get current user info');
    }
    const creatorUserName: string = creatorUser.firstName + ' ' + creatorUser.lastName;
    const isAdminOrManager: boolean = creatorUser.role === 'administrator' || creatorUser.role === 'manager';

    let filterGroupName: string | null = null;
    if (queryParams.groupId) {
      const group = await FormGroup.findByPrimary(queryParams.groupId, {
        attributes: ['groupName'],
      });
      if (group) {
        filterGroupName = group.groupName;
      }
    }

    csvStream.push(`"Created at ${formatDate(new Date(), 'DD-MM-YY, HH:mm')}",,,,,\n`);
    csvStream.push(`"Welcome ${creatorUserName}",,,,,\n\n`);
    csvStream.push(`"Overdue: ${data.overdueTasks}","Requires Attention: ${data.alertTasks}","Remaining: ${data.remainingTasks}",,,\n\n`);
    csvStream.push(`"Search: ${queryParams.search || '-'}",,,,,\n`);
    csvStream.push(`"Assigned User: ${filterAssignedUser || '-'}","","Department: ${filterGroupName || '-'}",,,\n`);

    let dateFilter = '';
    if (queryParams.createdAtStart || queryParams.createdAtEnd) {
      dateFilter = `Filter by created date: from ${queryParams.createdAtStart ? formatDate(queryParams.createdAtStart) : '-'
        } to ${queryParams.createdAtEnd ? formatDate(queryParams.createdAtEnd) : '-'}`;
    } else if (queryParams.dueAtStart || queryParams.dueAtEnd) {
      dateFilter = `Filter by due date: from ${queryParams.dueAtStart ? formatDate(queryParams.dueAtStart) : '-'
        } to ${queryParams.dueAtEnd ? formatDate(queryParams.dueAtEnd) : '-'}`;
    }

    if (dateFilter) {
      csvStream.push(`"${dateFilter}",`);
    }

    if (isAdminOrManager) {
      csvStream.push(`"Show only: ${queryParams.showAll || 'active'}",,,,${!dateFilter ? ',' : ''}\n\n`);
    } else {
      csvStream.push(`${dateFilter ? ',,,,\n\n' : '\n'}`);
    }

    const headerRow = `"Form Type","Form","Document","Assigned To","Date Created","Department","Due Date","Completed Date","Deleted Date","Deleted Reason","Link"`;

    csvStream.push(`${headerRow}\n\n`);

    const dateText = (date: Date | string | null) => {
      if (date) {
        return formatDate(date, "DD-MM-YY");
      }

      return "";
    }

    /** Rapidly load every record */
    // const firstRecords = await Promise.all( data.items.map( formEntry => RecordService.getFirstRecordForDuplication(formEntry.id) ) );

    console.log(`Writing Records[${data.items.length}] to CSV`);

    for (let i = 0; i < data.items.length; i++) {
      const formEntry = data.items[i];

      const formData = {
        "Form Type": formEntry.category.name,
        "Form": formEntry.summary,
        "Document": formEntry.document && formEntry.document.documentCode || '',
        "Assigned To": "",
        "Date Created": dateText(formEntry.createdAt),
        "Due Date": dateText(formEntry.dueAt),
        "Completed Date": dateText(formEntry.archivedAt),
        "Deleted Date": formEntry.isDeleted ? dateText(formEntry.archivedAt) : '',
        "Deleted Reason": formEntry.deleteReason || '',
        "Department": "",
        "Link": this.getFormUrl(formEntry.category.name, formEntry.id )
      };


      // Get last stage1 record
      const lastStage0 = RecordService.lastStage(formEntry, 0, 1);
      if (lastStage0) {
        const originalDepartmentId = RecordService.getFieldInRecord(lastStage0, 'userGroupId');
        if (originalDepartmentId) {
          const group = await UserGroupService.getUserGroupById(originalDepartmentId.intData);
          if (group) {
            formData.Department = group.groupName;
          }
        }

        if (formEntry.assignedUser) {
          formData['Assigned To'] = formEntry.assignedUser.firstName;
          if (formData['Assigned To'].length > 0)
            formData['Assigned To'] += " ";
          formData['Assigned To'] = formEntry.assignedUser.lastName;
        } else if (formEntry.userGroup) {
          formData['Assigned To'] = formEntry.userGroup.groupName + ' Department';
        }
      }

      if( !formData.Department && formEntry.userGroupId ) {
        const group = await UserGroupService.getUserGroupById(formEntry.userGroupId);
        if (group) {
          formData.Department = group.groupName;
        }

        if (formEntry.assignedUser) {
          formData['Assigned To'] = formEntry.assignedUser.firstName;
          if (formData['Assigned To'].length > 0)
            formData['Assigned To'] += " ";
          formData['Assigned To'] = formEntry.assignedUser.lastName;
        } else if (formEntry.userGroup) {
          formData['Assigned To'] = formEntry.userGroup.groupName + ' Department';
        }
        
      }

      csvStream.push(`${headerRow.split(",").map(quotedHeader => {
        const headerKey = quotedHeader.replace(/(^")|("$)/g, '');
        if (Object.prototype.hasOwnProperty.call(formData, headerKey)) {
          return "\"" + replaceNewLineWithSpace(formData[headerKey]).replace(/"/g, '""') + "\"";
        } else {
          return "\"\"";
        }
      }).join(",")}\n`);
    }

    if (data.items.length === 0) {
      csvStream.push(`"No Results"`);
    }

    csvStream.push(null);

    return csvStream;

  }

}

