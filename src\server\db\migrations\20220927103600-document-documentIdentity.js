'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if table exists first
      const tableExists = await queryInterface.showAllTables().then(tables =>
        tables.includes('documents')
      );

      if (!tableExists) {
        console.log('Table "documents" does not exist, skipping migration');
        return;
      }

      const tableDefinition = await queryInterface.describeTable('documents');

      if (!tableDefinition['documentIdentity']) {
        await queryInterface.addColumn('documents', 'documentIdentity', {
          type: Sequelize.STRING,
          allowNull: true,
          defaultValue: null
        });
      }

      const [data] = await queryInterface.sequelize.query('SELECT id, "documentCode" FROM documents');

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const documentIdentity = (row.documentCode || '').replace(/^[^-]+-/, '');
        console.log('Updating Document', row.id, 'With Identity', documentIdentity);

        await queryInterface.bulkUpdate('documents', {
          documentIdentity: documentIdentity
        }, {
          id: row.id
        });
      }
    } catch (error) {
      console.log('Error in migration:', error.message);
      // Don't throw error if column already exists
      if (error.message && error.message.includes('already exists')) {
        console.log('Column already exists, continuing...');
        return;
      }
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('documents', 'documentIdentity');
  }
};
