'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('forms_record_property', 'stringData', {
      type: Sequelize.STRING(16000),
      defaultValue: null,
      allowNull: true,
    })
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('forms_record_property', 'stringData', {
      type: Sequelize.STRING(255),
      defaultValue: null,
      allowNull: true,
    })
  }
};
