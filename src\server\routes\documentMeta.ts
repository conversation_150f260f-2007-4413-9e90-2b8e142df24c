import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { DocumentMetaController } from '../controller/documentMeta.controller';
import {
  validateBodyParams,
  validateRouteParams
} from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';
import { CreateDocumentMetaInputModel, SyncDocumentMetaInputModel, UpdateDocumentMetaInputModel } from '../../common/contracts/documentMeta';
import { docEditMiddleware } from '../middleware/adminRoleGroupMiddleware';

const router = express.Router();

/**
 * Allow access to admins only
 */

/**
 Tag routes
 */
router.get('/document-meta/document/:id', validateRouteParams(GetByIdParams), responseWrap(DocumentMetaController.getDocumentMetasByDocumentId));

// Use Sync instead where possible
router.post('/document-meta', docEditMiddleware, validateBodyParams(CreateDocumentMetaInputModel), responseWrap(DocumentMetaController.createDocumentMeta));
// Use Sync instead where possible
router.put('/document-meta', docEditMiddleware, validateBodyParams(UpdateDocumentMetaInputModel), responseWrap(DocumentMetaController.updateDocumentMeta));
// Use Sync instead where possible
router.delete('/document-meta/:id', docEditMiddleware, validateRouteParams(GetByIdParams), responseWrap(DocumentMetaController.archiveDocumentMeta));

router.put('/document-meta/document/:id', 
  docEditMiddleware, 
  validateRouteParams(GetByIdParams),
  validateBodyParams(SyncDocumentMetaInputModel), 
  responseWrap(DocumentMetaController.syncDocumentMeta)
);

export default router;
