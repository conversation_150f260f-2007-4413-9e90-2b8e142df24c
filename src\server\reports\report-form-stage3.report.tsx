import * as React from 'react';
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import * as moment from 'moment';
import 'moment-timezone';
import { IRecordWithDateOutputModel } from "../../common/contracts/record";
import { IRecordPropertyType } from "../../common/contracts/recordProperty";
import Footer from "./components/footer";
import { timeZone } from '../../common/constants';

const styles = StyleSheet.create({
  wrapper: {
    marginTop: 18,
  },
  header: {
    backgroundColor: '#19a0b7',
    padding: 10,
    paddingTop: 12,
    marginTop: 10,
    marginBottom: 10,
    fontSize: 32,
    lineHeight: 0.8,
    flexDirection: 'col',
    justifyContent: 'center',
    color: '#FFFFFF',
  },
  firstRow: {
    flexDirection: 'row',
    marginTop: 10,
    marginBottom: 14,
  },
  firstRowItem: {
    flex: 1,
    flexDirection: 'row',
    fontSize: 11,
  },
  labelText: {
    color: '#555555',
    fontSize: 11,
    marginBottom: 4,
    marginRight: 5,
  },
});

const FirstRow = ({children}) => (
  <View style={styles.firstRow}>
    {children}
  </View>
);

const FirstRowItem = ({name, value}) => {
  return (<View style={styles.firstRowItem}>
    <Text style={styles.labelText}>{name}:</Text>
    <Text>{value}</Text>
  </View>);
};

export class ReportFormStage3 extends React.Component<{
  recordData: IRecordWithDateOutputModel,
}> {
  render() {

    const propertyMap: {[k: string]: IRecordPropertyType}  = {};

    this.props.recordData.properties.forEach(prop => {
      propertyMap[prop.property.name] = prop;
    });

    const updatedByUser = this.props.recordData.createdByUser ? this.props.recordData.createdByUser.firstName + ' ' + this.props.recordData.createdByUser.lastName : '';
    const updatedAt = moment(this.props.recordData.createdAt).tz(timeZone).format('DD-MM-YYYY');

    return (
      <View style={styles.wrapper}>
        <View style={styles.header} wrap={false}>
          <Text>ADMINISTRATIVE COMPLETION</Text>
        </View>
        <FirstRow>
          <FirstRowItem
            name="Administration Finalised"
            value={propertyMap['adminComplete'] ? (propertyMap['adminComplete'].intData === 1 ? 'Yes' : 'No') : ''}
          />
        </FirstRow>
        <Footer updatedByUser={updatedByUser} updatedAt={updatedAt} />
      </View>
    );
  }
}
