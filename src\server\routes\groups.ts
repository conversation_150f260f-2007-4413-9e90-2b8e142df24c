import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { GroupsController } from '../controller/groups.controller';
import { adminMiddleware } from '../middleware/adminMiddleware';
import {
  validateBodyParams,
  validateQueryParams,
  validateRouteParams
} from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';
import { CreateUserGroupInputModel, GroupsQueryParams, UpdateUserGroupInputModel } from '../../common/contracts/groups';
import { preFillDefaultQueryParams } from '../middleware/preFillMiddleware';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/groups', adminMiddleware);

/**
 Group routes
 */
router.get('/groups',
  preFillDefaultQueryParams({skip: '0', limit: '10', sortBy: 'groupName', order: 'asc'}),
  validateQueryParams(GroupsQueryParams),
  responseWrap(GroupsController.getGroups));

router.get('/groups/:id', validateRouteParams(GetByIdParams), responseWrap(GroupsController.getGroupById));
router.post('/groups', adminMiddleware, validateBodyParams(CreateUserGroupInputModel), responseWrap(GroupsController.createGroup));
router.put('/groups', adminMiddleware, validateBodyParams(UpdateUserGroupInputModel), responseWrap(GroupsController.updateGroup));
router.delete('/groups/:id', adminMiddleware, validateRouteParams(GetByIdParams), responseWrap(GroupsController.archiveGroup));

export default router;
