import { Table, Column, Model, ForeignKey } from 'sequelize-typescript';
import User from './user.model';
import FormLocation from './formLocation.model';


@Table({
  tableName: 'user_to_location',
  timestamps: false,
})
class UserToLocation extends Model<UserToLocation> {

  @ForeignKey(() => User)
  @Column
  userId: number;

  @ForeignKey(() => FormLocation)
  @Column
  locationId: number;

}

export default UserToLocation;
