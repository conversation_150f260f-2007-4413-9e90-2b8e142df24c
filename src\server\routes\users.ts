import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { adminMiddleware } from '../middleware/adminMiddleware';
import { UsersController } from '../controller/users.controller';
import { validateBodyParams, validateQueryParams, validateRouteParams } from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';
import { CreateUserInputModel, UpdateUserInputModel, UsersQueryParams } from '../../common/contracts/users';
import { preFillDefaultQueryParams } from '../middleware/preFillMiddleware';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/users', adminMiddleware);

/**
 Users routes
 */
router.get('/users',
  preFillDefaultQueryParams({skip: '0', limit: '10', sortBy: 'createdAt', order: 'asc'}),
  validateQueryParams(UsersQueryParams),
  responseWrap(UsersController.getUsers));
router.get('/users/:id', validateRouteParams(GetByIdParams), responseWrap(UsersController.getUserById));
router.post('/users', adminMiddleware, validateBodyParams(CreateUserInputModel), responseWrap(UsersController.createUser));
router.put('/users', adminMiddleware, validateBodyParams(UpdateUserInputModel), responseWrap(UsersController.updateUser));
router.put('/users/:id/restore', adminMiddleware, validateRouteParams(GetByIdParams), responseWrap(UsersController.restoreUser));
router.delete('/users/:id', adminMiddleware, validateRouteParams(GetByIdParams), responseWrap(UsersController.archiveUser));

export default router;
