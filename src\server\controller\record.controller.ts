import { IFormRecordParams } from '../../common/contracts/form';
import FormRecord from '../db/models/formRecord.model';
import { HttpError, NotFoundError, ServerError } from '../../model/HttpErrors';
import { IUpdateRecordDateParams } from '../../common/contracts/record';
import { RecordService } from '../service/record.service';

export class RecordController {

  public static async createRecord(req: JwtRequest) {
    try {
      return await RecordService.createRecord(req.body as IFormRecordParams, req.tokenData.user.id);
    } catch (e) {
      console.error('Exception in RecordController.createRecord:', e);
      if ( e instanceof HttpError ) {
        throw e;
      } else {
        throw new ServerError();
      }
    }
  }

  public static async updateRecordDate(req: JwtRequest) {

    const { recordId, date } = req.body as IUpdateRecordDateParams;

    const record = FormRecord.findByPrimary(recordId);

    if (!record) {
      throw new NotFoundError('Form record is not found.');
    }

    const [ recordsUpdated ]  = await FormRecord.update({
      createdAt: new Date(date),
    }, {
      where: {
        id: recordId,
      }
    });

    return {
      recordsUpdated
    };

  }

}
