import * as argon2 from 'argon2';
import { reporter } from 'io-ts-reporters';
import User from '../db/models/user.model';
import {
  ICreateUserInputModel,
  IUpdateUserInputModel, IUsersQueryParams, IUserWithDateOutputModel,
  UserWithDateOutputModel
} from '../../common/contracts/users';
import { BadRequestError, NotFoundError, ServerError } from '../../model/HttpErrors';
import UserGroup from '../db/models/group.model';
import { IGetByIdParams } from '../../common/contracts/common';
import FormLocation from '../db/models/formLocation.model';
import { Sequelize } from 'sequelize-typescript';

export class UsersController {

  public static async getUsers(req: JwtRequest & Query<IUsersQueryParams>) {
    const queryParams: IUsersQueryParams = { ...req.query }; // clone query params

    const whereStatement: { where: any } = {
      where: {
        isArchived: queryParams.archivedOnly || false
      },
    };

    if (queryParams.includeArchived && !queryParams.archivedOnly) {
      delete whereStatement.where.isArchived;
    }

    if (queryParams.search) {
      const existingWhere = JSON.parse(JSON.stringify(whereStatement.where));

      whereStatement.where = {
        [Sequelize.Op.and]: [
          existingWhere,
          {
            [Sequelize.Op.or]: [
              { lastName: { [Sequelize.Op.like]: '%' + queryParams.search + '%' } },
              { firstName: { [Sequelize.Op.like]: '%' + queryParams.search + '%' } }
            ]
          }]
      };
    }

    const { rows, count } = await User.findAndCountAll<User>({
      limit: queryParams.limit,
      offset: queryParams.skip,
      order: (queryParams.sortBy === 'name' || queryParams.search) ?
        [['lastName', queryParams.order.toUpperCase()],
        ['firstName', queryParams.order.toUpperCase()]]
        :
        [[queryParams.sortBy, queryParams.order.toUpperCase()]],
      attributes: {
        exclude: ['password'],
      },
      ...whereStatement,
    });

    return {
      items: rows,
      totalCount: count,
    };
  }

  public static async getUserById(req: JwtRequest): Promise<IUserWithDateOutputModel> {

    const { id: userId } = req.params as unknown as IGetByIdParams;
    const { allowArchived } = req.query;

    const allowRetrieveArchived = allowArchived ? Boolean(allowArchived) : false;

    const user = await User.findOne({
      where: {
        id: userId
      },
      attributes: {
        exclude: ['password'],
      },
      include: [
        {
          model: UserGroup,
          required: false,
          where: {
            isArchived: false,
          },
          through: {
            attributes: []
          },
          attributes: {
            exclude: ['createdAt', 'updatedAt'],
          },
        },
        {
          model: FormLocation,
          required: false,
          through: {
            attributes: []
          },
        },
      ]
    });

    if (!user || (user.isArchived && !allowRetrieveArchived)) {
      throw new NotFoundError('User is not found.');
    }

    /**
     * Validate user output
     */

    const userJSON = user.toJSON();

    const validationResult = UserWithDateOutputModel.decode(userJSON);

    if (validationResult.isRight()) {
      return validationResult.value;
    } else {
      const report = reporter(validationResult);
      console.error('User output validation failed on ', userJSON);
      console.log('validation report - ', report.join(', '));
      throw new ServerError();
    }

  }

  public static async createUser(req: JwtRequest) {
    const userData = req.body as unknown as ICreateUserInputModel;

    try {

      const password = await argon2.hash(userData.password);

      const payload: any = {
        username: userData.username,
        password,
        role: userData.role,
        phone: userData.phone,
        firstName: userData.firstName,
        lastName: userData.lastName,
      };

      const newUser = await User.create(payload);

      await Promise.all([
        (newUser as any).setGroups(userData.groups),
        (newUser as any).setLocations(userData.locations),
      ]);

      return { status: 'ok', insertId: newUser.id };

    } catch (e) {

      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('User with this e-mail already exists.');
      } else {
        console.log('createUser exception', e);
        throw new ServerError();
      }
    }
  }

  public static async updateUser(req: JwtRequest) {
    const userData = req.body as unknown as IUpdateUserInputModel;

    const user: User | null = await User.findByPrimary<User>(userData.id);

    if (!user || user.isArchived) {
      throw new NotFoundError('User is not found.');
    }

    try {

      const payload: any = {
        username: userData.username,
        role: userData.role,
        phone: userData.phone,
        firstName: userData.firstName,
        lastName: userData.lastName,
      };

      if (userData.password) {
        payload.password = await argon2.hash(userData.password);
      }

      const [recordsUpdated] = await User.update(payload, {
        where: { id: userData.id }
      });

      await Promise.all([
        (user as any).setGroups(userData.groups),
        (user as any).setLocations(userData.locations),
      ]);

      return { status: 'ok', recordsUpdated };

    } catch (e) {

      if (e && e.name === 'SequelizeUniqueConstraintError') {
        throw new BadRequestError('User with this e-mail already exists.');
      } else {
        console.log('updateGroup exception', e);
        throw new ServerError();
      }
    }
  }

  public static async restoreUser(req: JwtRequest) {
    const { id: userId } = req.params as unknown as IGetByIdParams;

    const user: User | null = await User.findByPrimary<User>(userId);

    if (!user) {
      throw new NotFoundError('User is not found.');
    }

    const [recordsRestored] = await User.update({
      isArchived: false,
    }, {
      where: { id: userId }
    });

    return {
      restored: recordsRestored,
    };
  }

  public static async archiveUser(req: JwtRequest) {

    const { id: userId } = req.params as unknown as IGetByIdParams;

    if (userId === req.tokenData.user.id) {
      throw new BadRequestError('A user cannot delete themselves.');
    }

    const user: User | null = await User.findByPrimary<User>(userId);

    if (!user || user.isArchived) {
      throw new NotFoundError('User is not found.');
    }

    const [recordsArchived] = await User.update({
      isArchived: true,
    }, {
      where: { id: userId }
    });

    return {
      archived: recordsArchived,
    };

  }

}
