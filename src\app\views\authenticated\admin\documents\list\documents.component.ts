import { Component, OnInit } from '@angular/core';
import { GroupsService } from "../../../../../service/admin/GroupsService";
import { IGroupsSortedField, IUserGroupOutputModel } from "../../../../../../common/contracts/groups";
import { ActivatedRoute, Router } from "@angular/router";
import { ISortOrderValue } from "../../../../../../common/contracts/common";
import { PersistentStorageService } from "../../../../../service/PersistentStorageService";
import { ErrorHandlerService } from "../../../../../service/ErrorHandlerService";

@Component({
  selector: 'app-groups-component',
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss']
})
export class DocumentsComponent implements OnInit {

  public groups: IUserGroupOutputModel[] = [];
  public totalItems = 0;
  public currentPage = 1;
  public pageSize = this.pss.pageSize;
  public sortBy: IGroupsSortedField = 'groupName';
  public sortOrder: ISortOrderValue = 'asc';

  constructor(private groupsService: GroupsService,
              private router: Router,
              private route: ActivatedRoute,
              private pss: PersistentStorageService,
              private errorHandler: ErrorHandlerService) { }

  ngOnInit() {
    this.handlePageChange();
  }

  public handleGroupClick(groupId: number | string) {
    this.router.navigate([`${groupId}`], {relativeTo: this.route});
  }

  public handlePageChange() {
    this.groupsService.getPagedGroups(
      this.pageSize * (this.currentPage - 1),
      this.pageSize,
      this.sortBy,
      this.sortOrder
    ).subscribe((data) => {
      this.groups = data.items;
      this.totalItems = data.totalCount;
    }, (err) => this.errorHandler.handleHttpError(err));
  }

  public handleSortChange(sortField: IGroupsSortedField) {
    if (sortField === this.sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = sortField;
      this.sortOrder = 'asc';
    }

    this.handlePageChange();
  }

}
