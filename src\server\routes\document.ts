import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import {DocumentController} from '../controller/document.controller';
import { validateRouteParams } from '../middleware/validationMidleware';
import { GetByIdParams } from '../../common/contracts/common';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/document', adminMiddleware);

/**
 Record routes
 */
router.post('/document', responseWrap(DocumentController.upload));
router.get('/document/:id', validateRouteParams(GetByIdParams), DocumentController.download);
router.delete('/document/:id', validateRouteParams(GetByIdParams), responseWrap(DocumentController.delete));

export default router;
