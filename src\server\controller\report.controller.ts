import { Response } from 'express';
import { FormController } from './form.controller';
import { FormsPaginationResponse, IFormOutputValidationModel, IFormsQueryParams } from '../../common/contracts/form';

import { ReportService } from '../service/report.service';
import { RiskController } from './risk.controller';

export class ReportController {

	public static async createReport(req: JwtRequest, res: Response) {

		try {

			const form: IFormOutputValidationModel = await FormController.getFormById(req);

			if (!form) {
				res.status(404).end({ error: 'Form is not found' });
				return;
			}

			const pdfStream = await ReportService.generateReportToStream(form);

			res.setHeader('Content-type', 'application/pdf');

			pdfStream.pipe(res);

		} catch (err) {
			console.log('Exception while generating report', err);
			res.status(500).end();
		}
	}

	public static async exportFormsToCsv(req: JwtRequest & Query<IFormsQueryParams>, res: Response) {
		//@ts-ignore
		req.query.skip = 0;
		//@ts-ignore
		req.query.limit = 1000000;

		try {

			const forms = await FormController.getForms(req);

			const pdfStream = await ReportService.generateDashboardCsvToStream(
				req.query as IFormsQueryParams,
				forms as any as FormsPaginationResponse,
				req.tokenData.user.id,
			);

			res.setHeader('Content-type', 'text/plain');

			pdfStream.pipe(res);

		} catch (err) {
			console.log(err);
			res.status(500).end();
		}

	}
	public static async exportRiskFormsToCSV(req: JwtRequest & Query<IFormsQueryParams>, res: Response) {
		//@ts-ignore
		req.query.skip = 0;
		//@ts-ignore
		req.query.limit = 1000000;
		try {

			const forms = await RiskController.getRisks(req);

			const pdfStream = await ReportService.generateRiskCsvToStream(
				req.query,
				forms,
				req.tokenData.user.id,
			);

			res.setHeader('Content-type', 'text/plain');

			pdfStream.pipe(res);

		} catch (err) {
			console.log(err);
			res.status(500).end();
		}

	}
	public static async exportDRMFormsToCsv(req: JwtRequest & Query<IFormsQueryParams>, res: Response) {
		//@ts-ignore
		req.query.skip = 0;
		//@ts-ignore
		req.query.limit = 1000000;

		try {

			const forms = await FormController.getForms(req);

			const pdfStream = await ReportService.generateDRMDashboardCsvToStream(
				req.query as IFormsQueryParams,
				forms as any as FormsPaginationResponse,
				req.tokenData.user.id,
			);

			res.setHeader('Content-type', 'text/plain');

			pdfStream.pipe(res);

		} catch (err) {
			console.log(err);
			res.status(500).end();
		}

	}

}
