import * as t from 'io-ts';
import { constructType } from './extend';
import { IntFromString } from 'io-ts-types/lib/IntFromString';
import { SortOrderValues } from './common';
import { PaginationResponse } from './pagination';

const LocationOutputModel = t.interface({
  id: t.number,
  name: t.string,
  usersCount: t.union([t.number, t.undefined]),
});

export interface ILocationOutputModel extends t.TypeOf<typeof LocationOutputModel> {}

export type LocationPaginationResponse = PaginationResponse<ILocationOutputModel>;

export const CreateLocationInputModel = t.interface({
  name: t.refinement(t.string, s => (s.length > 0 && s.trim().length > 0), 'not empty name'),
});

export interface ICreateLocationInputModel extends t.TypeOf<typeof CreateLocationInputModel> {}

export const UpdateLocationInputModel = t.interface({
  id: t.number,
  name: t.refinement(t.string, s => (s.length > 0 && s.trim().length > 0), 'not empty name'),
});

export interface IUpdateLocationInputModel extends t.TypeOf<typeof UpdateLocationInputModel> {}


// See comment in /contracts/common.ts for explanation for this ignore
// @ts-ignore
export const LocationSortField = t.union(['name', 'usersCount'].map(f => t.literal(f)));

export type ILocationSortField = t.TypeOf<typeof LocationSortField>;

export const LocationsQueryParams = constructType({
  skip: t.union([IntFromString, t.number]),
  limit: t.union([IntFromString, t.number]),
  sortBy: LocationSortField,
  order: SortOrderValues
}, {
  countUsers: t.refinement(t.union([IntFromString, t.number]), v => v === 1 || v === 0, 'valid countUsers parameter' ),
});

export interface ILocationsQueryParams extends t.TypeOf<typeof LocationsQueryParams> {}
