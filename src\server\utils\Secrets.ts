require('dotenv').config();

export const LOG_LEVEL = process.env.LOG_LEVEL || "debug";
export const ERROR_EMAIL = process.env.ERROR_EMAIL || process.env.FROM_EMAIL || "<EMAIL>";

/**
 * Debug Output of Secrets.
 * CAUTION: Adds Plain Text ENV Properties to Logs. Should never occur in production.
 *
 */
/*
if( !IS_PROD ) {
    const that = this;
    console.log("Secrets");
    console.log(Object.assign({}, ...(Object.keys(that).map( key => ({
        [key]: that[key]
    })))));
}
*/