import * as t from 'io-ts';

export const AuthParams = t.exact(t.type({
  password: t.refinement(t.string, s => s.length >= 6 && s.length < 128, 'Password length is incorrect'),
  username: t.refinement(t.string, s => s.length >= 3 && s.length < 128, 'Username length is incorrect'),
}));

export interface IAuthParams extends t.TypeOf<typeof AuthParams> {}

export const ForgotPassParams = t.exact(t.type({
  email: t.refinement(t.string, s => s.length >= 3 && s.length < 128, 'Username length is incorrect'),
}));

export interface IForgotPassParams extends t.TypeOf<typeof ForgotPassParams> {}

export const ResetPassParams = t.exact(t.type({
  resetToken: t.string,
  username: t.refinement(t.string, s => s.length >= 3 && s.length < 128, 'Username length is incorrect'),
  password: t.refinement(t.string, s => s.length >= 6 && s.length < 128, 'Password length is incorrect'),
}));

export interface IResetPassParams extends t.TypeOf<typeof ResetPassParams> {}
