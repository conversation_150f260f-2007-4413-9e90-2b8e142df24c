import * as express from 'express';
import { responseWrap } from '../utils/responseWrap';
import { CurrentUserController } from '../controller/current.user.controller';
import { validateBodyParams } from '../middleware/validationMidleware';
import { UpdateUserProfileInputModel } from '../../common/contracts/currentUser';

const router = express.Router();

/**
 routes
 */

router.get('/current_user', responseWrap(CurrentUserController.getCurrentUser));

/**
 * update user profile
 */
router.put('/current_user', validateBodyParams(UpdateUserProfileInputModel), responseWrap(CurrentUserController.updateUserProfile));


export default router;
