import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { adminMiddleware } from '../middleware/adminMiddleware';
import {EnumController} from '../controller/enum.controller';
import { validateBodyParams } from '../middleware/validationMidleware';
import { EnumSetCreateModel, EnumSetUpdateModel } from '../../common/contracts/enums';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/enum', adminMiddleware);

/**
 Enum routes
 */

router.get('/enum', adminMiddleware, responseWrap(EnumController.getEnums));
router.get('/enum/:enumName', responseWrap(EnumController.getEnumsByName));
router.post('/enum', adminMiddleware, validateBodyParams(EnumSetCreateModel), responseWrap(EnumController.createEnumSet));
router.put('/enum', adminMiddleware, validateBodyParams(EnumSetUpdateModel), responseWrap(EnumController.updateEnumSet));

export default router;
