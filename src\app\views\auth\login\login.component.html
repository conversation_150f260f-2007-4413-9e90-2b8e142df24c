<div class="row d-flex align-items-center py-4">
  <div class="col-10 offset-1 col-sm-8 offset-sm-2 col-md-6 offset-md-3 col-xl-4 offset-xl-4 bg-white align-self-center border border-light-grey rounded shadow-lg">
    <h3 class="py-1 my-0">Please Sign In</h3>
    <div class="row bg-light border-top border-light-grey">
      <div class="col-12">
        <form class="py-2">
          <div class="alert alert-danger" *ngIf="errorMessage">
            Some things weren't quite right...
            <div *ngIf="errorMessage">
              {{errorMessage}}
            </div>
          </div>
          <div class="form-group">
            <label for="emailAddress">Email Address</label>
            <input type="email" class="form-control {{emailAddress.iifIsValid('border-success','border-danger')}}" id="emailAddress" placeholder="Enter Email" [(ngModel)]="emailAddress.value" [ngModelOptions]="{standalone: true}">
            <small class="form-text text-danger" *ngIf="emailAddress.showErrorHelp()">Invalid Email Address</small>
          </div>
          <div class="form-group">
            <label for="password">Password</label>
            <input type="password" class="form-control {{password.iifIsValid('border-success','border-danger')}}" id="password" placeholder="Password" [(ngModel)]="password.value" [ngModelOptions]="{standalone: true}">
            <small class="form-text text-danger" *ngIf="password.showErrorHelp()">Invalid Password</small>
          </div>
          <div class="row">
            <div class="forgot-password-link">
              <a [routerLink]="['/forgot']" [queryParams]="{email: emailAddress.value}">Forgot password</a>
            </div>
          </div>
          <div class="row">
            <div class="col-12 text-center">
              <!---
                <a class="btn btn-primary col-4 offset-1" [routerLink]="'/register'">Register</a>
              --->
              <button class="btn btn-success px-4 py-1" (click)="submit()">Login</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
