import { NotFoundError, ServerError } from '../../model/HttpErrors';
import FormLocation from '../db/models/formLocation.model';
import {
  ICreateLocationInputModel, ILocationsQueryParams,
  IUpdateLocationInputModel,
} from '../../common/contracts/location';
import { IGetByIdParams } from '../../common/contracts/common';
import { Sequelize } from 'sequelize-typescript';

export class LocationController {

  public static async getLocations(req: JwtRequest) {

    const queryParams = req.query as unknown as ILocationsQueryParams;

    const attributes: any[] = ['id', 'name'];

    if (queryParams.countUsers) {
      /**
       * add subQuery to count users in locations
       */
      attributes.push([Sequelize.literal(
        `(SELECT COUNT(*)
          FROM user_to_location LEFT JOIN users ON users.id = user_to_location.userId
          WHERE user_to_location.locationId = FormLocation.id AND users.isArchived = 0)`
      ), 'usersCount']);
    }

    const {rows, count} = await FormLocation.findAndCountAll<FormLocation>({
      limit: queryParams.limit,
      offset: queryParams.skip,
      where: {
        isArchived: false,
      },
      attributes,
      order: [ [ Sequelize.literal(queryParams.sortBy) as any as string, queryParams.order.toUpperCase() ] ],
    });

    return {
      items: rows,
      totalCount: count,
    };

  }

  public static async getLocationById(req: JwtRequest) {

    const { id: locationId } = req.params as unknown as IGetByIdParams;

    const location = await FormLocation.findOne({
      where: {
        id: locationId,
        isArchived: false,
      },
      attributes: {
        exclude: ['isArchived'],
      }
    });

    if (!location) {
      throw new NotFoundError('Location is not found.');
    }

    return location;
  }

  public static async createLocation(req: JwtRequest) {

    const { name } = req.body as unknown as ICreateLocationInputModel;

    try {
      return await FormLocation.create({name}, {fields: ['name']});
    } catch (e) {
      console.error('Exception in LocationController.createLocation', e);
      throw new ServerError();
    }
  }

  public static async updateLocation(req: JwtRequest) {

    const { id, name } = req.body as unknown as IUpdateLocationInputModel;

    const location: FormLocation | null = await FormLocation.findByPrimary<FormLocation>(id);

    if (!location || location.isArchived) {
      throw new NotFoundError('Location is not found');
    }

    try {
      const [ recordsUpdated ] = await FormLocation.update({name}, {where: {id: id}});
      return {status: 'ok', recordsUpdated};
    } catch (e) {
      console.error('Exception in LocationController.updateLocation', e);
      throw new ServerError();
    }
  }

  public static async archiveLocation(req: JwtRequest) {

    const { id: locationId } = req.params as unknown as IGetByIdParams;

    const location: FormLocation | null = await FormLocation.findByPrimary(locationId);

    if (!location || location.isArchived) {
      throw new NotFoundError('Location is not found.');
    }

    const [ recordsArchived ] = await FormLocation.update({
      isArchived: true,
    }, {
      where: {id: locationId}
    });

    return {
      archived: recordsArchived,
    };

  }
}
