/*--- Report Form Style ---*/


/*-- Header --*/
body {
    background: #e1e1e1;
    font-family: '<PERSON><PERSON>', sans-serif;
    margin: 0;
}
input, textarea, select {
    font-family: '<PERSON><PERSON>';
    font-size: 14px !important;
    padding-left: 8px !important;
}
#main-wrap {
    width: 980px;
    background: #fff;
    margin: 0 auto;
    padding: 30px;
}

header {
}

#logo img {
    width: 25% !important;
    margin-top: 20px;
}

#right-info-box {
    width: 300px;
    border: 1px dashed #747474;
    text-align: center;
    float: right;
    margin-top: -85px;
    padding: 20px 5px;
}
#right-info-box h5 {
    font-size: 17px;
    margin: 0;
    font-weight: 300
}
#right-info-box h5 strong {
    font-weight: 500;
}
#right-info-box p {
    font-size: 17px;
    margin: 10px 0 0;
}

/*-- End of Header --*/

/*-- Container Wrap --*/
.main-top-header {
    color: #ed7d31;
    font-size: 45px;
    margin-top: 50px;
}
.main-top-header-border {
    height: 5px;
    background: #747474;
    margin-top: -60px;
    width: 41%;
    float: right;
}
#top-section {
    overflow: hidden;
}
.one-line-input {
    float: left;
    width: 25%;
}

.one-line-input input, .one-line-input select {
    width: 90%;
    height: 35px !important;
    margin-top: 7px;
}
.one-line-input input {
    height: 29px !important;
    width: 85%;
}

/*--- Form Structure ----*/
.fisrt-layer {
    position: relative;
    height: 90px;
}
.second-layer {
    position: relative;
    min-height: 355px;
    margin-bottom: 30px;
}

/*- Layer Left Side -*/
.layer-left-side {
    position: relative;
    width: 70%;
    float: left;
    border-right: 1px dashed #b7b7b7;
    padding-right: 10px;
}

.layer-left-side .one-line-input {
    float: left;
    width: 216px;
}
.originator-type-box .one-line-input input, .originator-type-box .one-line-input select {
    margin-left: 11px;
}
.originator-type-box {
    width: 650px;
    height: 53px;
    border: 1px solid #e1e1e1;
    padding: 10px;
    margin: 10px 0px;
}

.h2-headers {
    margin-top: 0px;
    font-weight: 400;
    font-size: 18px;
    margin-bottom: 5px;
}

textarea.textarea-default {
    width: 650px;
    height: 145px;
    padding: 10px;
    border: 1px solid #b7b7b7;
}

/*- Layer Right Side -*/
.layer-right-side {
    float: left;
    margin-left: 5px !important;
    padding-left: 20px;
    width: 230px;
    padding-top: 0px;
    padding-bottom: 20px;
}

/*- Upload Button -*/
.upload-btn {
    width: 230px;
    padding: 15px 10px;
    color: #fff;
    background: #ed7d31;
    border: 0;
    font-size: 19px;
    cursor: pointer;
}
.upload-btn strong {
    font-family: 'Roboto';
    letter-spacing: 1px;
    font-weight: 300;
}
.fa-upload {
    letter-spacing: 10px;
}

/*- File Upload Lists -*/
.file-upload-header {
    font-size: 21px;
    font-weight: 400;
    margin-bottom: -10px;
    margin-top: 30px
}
.file-upload-list {
    padding-left: 10px;
}
.file-upload-list li {
    list-style: none;
    padding: 6px 0px;
    border-bottom: 1px solid #e1e1e1;
    margin: 4px 0px;
    font-size: 14px
}

/*- Thrid Layer -*/
.third-layer {
    width: 100%;
    background: #e7e7e7;
    height: 20px;
    clear: both;
    position: relative;
    padding: 25px 20px;
}

.status-lists {
    margin-top: -6px !important;
    margin-left: 10px;
    position: absolute;
    width: 215px !important;
}
.bottom-box-input {
    width: 29%;
    padding: 0px 20px;
    border-left: 1px solid #898989;
}
.bottom-box-input:nth-child(1) {
    border: none !important;
    padding-left: 5px;
    padding-right: 35px;
}
.bottom-box-input label {
    color: #323232;
}
.bottom-box-input label strong {
    color: #19a0b7;
    font-weight: 400;
}

/*- Bottom Buttons -*/
.save-cancel-btn-col  {
    clear: both;
}
.save-cancel-btn-col  {
    margin-top: 20px
}
.save-cancel-btns {
    padding: 10px 0;
    margin-right: 13px;
    font-size: 18px;
    border: none;
    color: #fff;
    width: 120px;
    cursor: pointer;
}
.save {
    background: #5cb85c;
}
.cancel {
    background: #747474;
}


/*-- Header Separator ---*/
.header-separator {
    background: #19a0b7;
    color: #fff;
    font-weight: 400;
    padding: 6px 20px;
    text-transform: uppercase;
    margin: 60px 0 30px;
}

.spacer {
    height: 25px;
}
.spacer-medium {
    height: 10px;
}
.spacer-small {
    height: 10px;
}

/*-- Date Picker --*/
#ui-datepicker-div {
    background: #fff;
    border: 1px solid #c5c5c5;
    width: 300px;
}
.ui-datepicker-header {
    background: #e9e9e9;

}
.ui-datepicker-calendar {
    width: 100%
}

.ui-datepicker-calendar tbody tr td {
    text-align: right !important;
    border: 1px solid #c5c5c5;
    background: #f6f6f6;
    padding: 3px 4px
}
.ui-state-disabled {
    border: 0 !important;
}
.ui-datepicker-calendar tbody tr td a {
    color: #323232 !important;
    text-decoration: none;
    display: block;
}

.ui-datepicker .ui-datepicker-header {
    position: relative;
    padding: .2em 0;
}
.ui-datepicker .ui-datepicker-next {
    right: 2px;
}
.ui-datepicker .ui-datepicker-title {
    margin: 0 2.3em;
    line-height: 1.8em;
    text-align: center;
}
.ui-datepicker .ui-datepicker-prev, .ui-datepicker .ui-datepicker-next {
    position: absolute;
    top: 2px;
    width: 1.8em;
    height: 1.8em;
}
.ui-datepicker .ui-datepicker-prev {
    left: 2px;
}
.ui-datepicker .ui-datepicker-next {
    right: 2px;
}
.ui-datepicker .ui-datepicker-prev span, .ui-datepicker .ui-datepicker-next span {
    display: block;
    position: absolute;
    left: 50%;
    margin-left: -18px;
    top: 50%;
    margin-top: -8px;
}

.ui-datepicker-next {
    background: url('../../../../assets/images/calendar-ico-right.png') no-repeat 0px !important;
    cursor: pointer;
}
.ui-datepicker-next span {
    display: none !important;
}

.ui-datepicker-prev {
    background: url('../../../../assets/images/calendar-ico-left.png') no-repeat 7px;
    cursor: pointer;
}
.ui-datepicker-prev span {
    display: none !important;
}

.ui-datepicker-calendar th {
    padding: 10px;
    font-weight: 400;
}

.datepicker-cal {
    height: 35px;
    margin-left: 10px;
    width: 210px;
    background: url('../../../../assets/images/orange-calendar-ico.png') no-repeat 188px 6px;
    border: 1px solid #b7b7b7;
}

.datepicker-due-date {
    background: url('../../../../assets/images/orange-calendar-ico.png') no-repeat 160px 3px;
}
.follow-up-box {
    height: auto;
}
.follow-up-box h3 {
    font-size: 20px;
    font-weight: 400;
    margin: 15px 11px 10px 11px;
}
.follow-up-box label {
    margin-left: 11px;
}
.add-follow-up {
    margin-left: 0;
    color: #323232;
    display: block;
    margin-top: 20px;
}
.add-follow-up span {
    font-size: 13px;
}
.add-follow-up img {
    margin-bottom: -3px;
    margin-right: 5px;
}

.staff-name-lists {
    height: 35px;
    width: 200px;
    margin-left: 20px;
}

.bottom-box-two-layers {
    width: 67%;
    height: 80px
}

.bottom-box-two-layers .bottom-box-input {
    width: 40%;
    margin-bottom: 40px;
}
.bottom-box-two-layers .bottom-box-input:nth-child(3) {
    border: 0;
    padding-left: 7px !important;
    margin-right: 13px;
}

.bottom-box-full-width {
    width: 96%;
}

.email-add-input {
    height: 35px;
    margin-left: 10px;
    width: 60%;
}


/*---------- Add Follow Up Box ----------*/
.add-follow-up-label {
    height: 28px;
}

.add-follow-up-box {
    height: 50px;
    border-bottom: 1px dashed #e1e1e1;
    margin-bottom: 7px;
    padding-bottom: 7px;
}
.remove-btn {
    border-radius: 15px;
    border: 0;
    background: #cf0000;
    color: #fff;
    position: absolute;
    width: 11px;
    height: 11px;
    padding: 0;
    font-size: 7px;
    margin-left: -7px;
    margin-top: -4px;
    cursor: pointer;
    font-weight: 600;
}
.add_details {
    background: transparent;
    border: none;
    font-family: 'Roboto';
    font-weight: 300;
    height: 20px;
    cursor: pointer;
    background: url('../../../../assets/images/add-ico.png') no-repeat 2px 4px;
    padding-left: 25px !important;
}
/*---------- End of Add Follow Up Box ----------*/

.top-layer-box-radio-btn-list {
    border: 1px solid #e1e1e1;
    padding: 25px !important;
    margin-bottom: 40px
}
.top-layer-box-radio-btn-list li {
    list-style: none;
    display: inline;
    margin: 10px 7.5%;
}
.numbered-headers {
    font-size: 18px;
    margin-bottom: 10px;
    font-weight: 500;
}

.datepicker-contain {
    margin-top: 1px;
}
.one-line-input input#datepicker {
    width: 65%;
}
.one-line-input input.datepicker-frm-to {
    background: url('../../../../assets/images/orange-calendar-ico.png') no-repeat 140px 4px;
}
.this-fur-is {
    padding-left: 0px;
}
.this-fur-is li {
    list-style: none;
}
.numbered-headers label {
    font-weight: 300;
    font-size: 20px;
}
.child-forms-container {
    border-left: 20px #19a0b7 solid;
    border-bottom: 20px #19a0b7 solid;
    padding-left: 10px;
    margin-bottom: 10px;
}
