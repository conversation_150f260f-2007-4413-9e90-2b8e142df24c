import * as express from 'express';

import { responseWrap } from '../utils/responseWrap';
import { validateBodyParams } from '../middleware/validationMidleware';
import { FormRecordParams } from '../../common/contracts/form';
import {RecordController} from '../controller/record.controller';
import { adminMiddleware } from '../middleware/adminMiddleware';
import { UpdateRecordDateParams } from '../../common/contracts/record';

const router = express.Router();

/**
 * Allow access to admins only
 */
// router.use('/record', adminMiddleware);

/**
 Record routes
 */
router.post('/record',
  validateBodyParams(FormRecordParams),
  responseWrap(RecordController.createRecord));

router.put('/record',
  adminMiddleware,
  validateBodyParams(UpdateRecordDateParams),
  responseWrap(RecordController.updateRecordDate)
);

export default router;
