import { Response, NextFunction } from 'express';
import { ForbiddenError } from '../../model/HttpErrors';
import UserModel from '../db/models/user.model';
import UserGroup from '../db/models/group.model';
import { DocumentEditGroup, DocumentEditRole } from '../../common/constants';

const adminRoleName = "administrator";

/**
 * Provides a way to confirm that a user is an administrator, or has a role within a specific group name
 * 
 * @param {string} role role which the user must have for the group to be inspected
 * @param {string|undefined} groupName Optional, the name of the group the user must be a member of
 */
export const adminRoleGroupMiddleware = (role: string, groupName?: string) => async (req: JwtRequest, res: Response, next: NextFunction) => {
  const errMsg = `Admin or ${role} role ` + (groupName ? `in ${groupName}` : '') + ' is required to perform the action';

  if (
    req.tokenData && req.tokenData.user &&
    (req.tokenData.user.role === adminRoleName || req.tokenData.user.role === role)
  ) {
    if (groupName && req.tokenData.user.role !== adminRoleName) {
      const user = await UserModel.findByPrimary(req.tokenData.user.id, {
        include: [{
          model: UserGroup,
          required: false
        }]
      });

      const hasGroupAccess = user && user.groups && user.groups.length && !!user.groups.find(group => group.groupName === groupName);

      if (!hasGroupAccess) {
        res.status(ForbiddenError.errorCode).json({
          error: errMsg,
          code: ForbiddenError.errorCode,
        });
        return;
      }
    }

    next();
  } else {
    res.status(ForbiddenError.errorCode).json({
      error: errMsg,
      code: ForbiddenError.errorCode,
    });
  }
};

/**
 * Convenience middleware definition for DocAdmin
 */
export const docEditMiddleware = adminRoleGroupMiddleware(DocumentEditRole, DocumentEditGroup);