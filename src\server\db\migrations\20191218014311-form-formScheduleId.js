'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    const tableDefinition = await queryInterface.describeTable('forms');

    if (tableDefinition['formScheduleId']) {
      return;
    }

    await queryInterface.addColumn('forms', 'formScheduleId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: null,
      references: {
        model: 'form_schedule',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('forms', 'formScheduleId');
  }
};
