import * as winston from "winston";

import { format } from "winston";
import { Format } from "logform";
import { LOG_LEVEL } from "./Secrets";

const start = (new Date()).getTime();

const formatter: Format = {
  transform: (info) => {
	const now = new Date();
	info.message = `${process.pid}-${now.getTime() - start}: ${info.message}`;

	return info;
  }
};

function createLoggerInstance(): winston.Logger {
	const logger = winston.createLogger({
		level: LOG_LEVEL,
		transports: [
			new winston.transports.Console({
				format: format.combine(
					{ transform: (info) => info },
					format.combine(formatter, winston.format.simple())
				)
			})
		]
	});

	logger.debug(`Logger initialized at ${logger.level}`);

	return logger;
}

const logger = createLoggerInstance();

export default logger;