import 'moment-timezone';
import Form from '../db/models/form.model';
import FormRecordProperty from '../db/models/formRecordProperty.model';
import Property from '../db/models/property.model';
import FormRecord from '../db/models/formRecord.model';
import FormDocument from '../db/models/formDocument.model';
import { Sequelize } from 'sequelize-typescript';
import documentInstance from '../db/models/documentInstance.model';
import Document from '../db/models/document.model';
import { NotFoundError } from '../../model/HttpErrors';
import logger from '../utils/Logger';

const className = "DocumentDRMServiceClass";

class DocumentDRMServiceClass {
	public async getFormsAttached(documentId: number) {
		const id = documentId
		const whereClause: [{ [key: string]: any }] = [{
			'summary': {
				[Sequelize.Op.not]: null,
			}
		}];

		whereClause[0]['id'] = {
			[Sequelize.Op.in]: Sequelize.literal(
				`(
              SELECT formid
              FROM forms_record_property
              INNER JOIN forms_record
              ON forms_record_property.formRecordId = forms_record.id
              WHERE
              (
                propertyID = (
                  SELECT id
                  FROM properties
                  WHERE \`name\` = 'documentId'
                )
                AND stringData = ${id}
              )
            )`
			)
		};

		const form = await Form.findAll({
			where: {
				[Sequelize.Op.and]: whereClause
			},
			distinct: true,
			attributes: {
				include: [
					[Sequelize.literal('`Form`.`dueAt` < NOW() AND `Form`.`archivedAt` IS NULL'), 'isOverdue'],
					[Sequelize.literal('NOW() BETWEEN `Form`.`alertAt` AND `Form`.`dueAt` AND `Form`.`archivedAt` IS NULL'), 'isAlertOverdue'],
				]
			}, include: [
				{
					model: FormRecord,
					as: 'records',
					required: false,
					include: [{
						model: FormRecordProperty,
						required: false,
						include: [
							{
								model: Property,
								required: true
							},
						]
					}, {
						model: FormDocument,
						as: 'documents',
						required: true,
						through: {
							attributes: []
						},
						attributes: {
							exclude: ['path', 'userId'],
						},
					}],
				},
			]
		});

		return form;
	}

	public async getLatestDraft(documentId: number) {
		const signature = className + `.getLatestDraft: DocumentId[${documentId}]`;

		if (isNaN(documentId)) {
			logger.warn(signature + "Invalid DocumentId");
			return null;
		}

		const forms = await this.getFormsAttached(documentId);

		if (!forms.length) {
			logger.warn(signature + "No Draft Version of this document found");
			return null;
		}

		let latestForm: Form = forms[0];
		let latestFormStamp = latestForm.createdAt.getTime();
		forms.forEach(form => {
			const timeStamp = form.createdAt.getTime();
			if (timeStamp > latestFormStamp) {
				latestFormStamp = timeStamp;
				latestForm = form;
			}
		});

		if (!latestForm.records || !latestForm.records.length) {
			logger.warn(signature + "No Draft version of this document found in the most recent form");
			return null;
		}

		let latestRecord: FormRecord = latestForm.records[0];
		let latestRecordStamp = latestRecord.createdAt.getTime();
		latestForm.records.forEach(record => {
			const timeStamp = record.createdAt.getTime();
			if ((!latestRecord.documents || !(latestRecord.documents.length)) ||
				(timeStamp > latestRecordStamp && record.documents && record.documents.length)
			) {
				latestRecordStamp = timeStamp;
				latestRecord = record;
			}
		});

		if (!latestRecord.documents || !latestRecord.documents.length) {
			logger.warn(signature + "No Draft version of this document found in the most recent form record");
			return null;
		}

		return latestRecord.documents[0];
	}

	public async getLatestAprovedVersion(id: number) {

		if (isNaN(id)) {
			return null
		}

		const latestVersion = await documentInstance.max('version', {
			where: {
				documentId: id,
			}
		});

		if (!latestVersion) {
			return null
		}

		const file = await documentInstance.findOne({
			where: {
				documentId: id,
				version: latestVersion,
			}
		});

		if (!file) {
			return null;
		}

		return file;

	}

	public async updateAllFormSummaries(documentId: number) {
		const document = await Document.findByPk(documentId);

		if (!document) throw new NotFoundError('Document is not found.');

		return await Form.update({ summary: document.name }, {
			where: {
				id: {
					[Sequelize.Op.in]: Sequelize.literal(
						`(
					  		SELECT formid
					  		FROM forms_record_property
					  		INNER JOIN forms_record
					  		ON forms_record_property.formRecordId = forms_record.id
					  		WHERE
					  		(
								propertyID = (
						  			SELECT id
						  			FROM properties
						  			WHERE \`name\` = 'documentId'
								)
								AND stringData = '${document.id}'
					  		)
						)`
					)
				}
			}
		});
	}
}
export const DocumentDRMService = new DocumentDRMServiceClass()