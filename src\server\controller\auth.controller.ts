import * as express from 'express';
import { promisify } from 'util';
import * as crypto from 'crypto';
import * as argon2 from 'argon2';
import { AuthService } from '../service/server.auth.service';
import { JWTPayload } from '../../model/Auth';
import { IAuthParams, IForgotPassParams, IResetPassParams } from '../../common/contracts/auth';
import User from '../db/models/user.model';
import { BadRequestError, NotFoundError } from '../../model/HttpErrors';
import { MailService } from '../service/mail.service';
import PasswordReset from '../db/models/passwordResets.model';
import { Sequelize } from 'sequelize-typescript';
import { IGetByIdParams } from '../../common/contracts/common';

export class AuthController {
	public static async emulateUser(req: express.Request): Promise<JWTPayload> {
		const { id: userId } = req.params as unknown as IGetByIdParams;

		const auth: JWTPayload = await AuthService.emulateUser(userId);

		return auth;
	}

	public static async login(req: express.Request): Promise<JWTPayload> {
		const { username, password } = req.body as IAuthParams;

		const auth: JWTPayload = await AuthService.login({ username, password });

		return auth;
	}

	public static async forgotPassword(req: express.Request) {
		const { email } = req.body as IForgotPassParams;

		const user: User | null = await User.findOne({
			where: {
				isArchived: false,
				username: email,
			},
			attributes: [
				'id'
			],
		});

		if (!user) {
			throw new NotFoundError('User with this e-mail doesn\'t exists.');
		}

		/*
		System Email Settings should not prevent Emails from being sent
		const isEmailsEnabled = await MailService.canSendEmail();
	
	
		if( !isEmailsEnabled ) {
			// The email functionality is currently disabled, the user needs to find an admin
			throw new ConflictError( "Emails are currently disabled. Password cannot be reset.");
		}
		*/

		const buffer = await promisify(crypto.randomBytes)(32);

		const resetToken = buffer.toString('hex');

		await PasswordReset.create({
			token: resetToken,
			userId: user.id,
			isUsed: false,
		});

		await MailService.sendEmail({
			to: email,
			from: process.env.EMAIL_FROM || '<EMAIL>',
			subject: 'Reset password',
			template: 'reset-password',
			context: {
				resetLink: `${process.env.FRONT_HOST}/reset/${resetToken}?email=${encodeURIComponent(email)}`,
			}
		}, {
			force: true
		});

		return {
			status: 'ok',
		};

	}

	public static async resetPassword(req: express.Request): Promise<JWTPayload> {

		const { resetToken, username, password } = req.body as IResetPassParams;

		const passwordReset = await PasswordReset.findOne({
			where: {
				token: resetToken,
				isUsed: false,
			},
			attributes: [
				'id',
				[Sequelize.literal('(`PasswordReset`.`createdAt` < SUBDATE(NOW(), INTERVAL 1 DAY))'), 'isExpired']
			],
			include: [
				{
					model: User,
					required: true,
					attributes: ['id', 'username', 'role', 'isArchived'],
				}
			]
		});

		if (!passwordReset) {
			throw new NotFoundError('Password reset token is not found.');
		}

		if (passwordReset.user.isArchived) {
			throw new BadRequestError('User was archived and cannot change password.');
		}

		if (passwordReset.isExpired) {
			throw new BadRequestError('Password reset link has been expired.');
		}

		if (passwordReset.user.username !== username) {
			throw new BadRequestError('Wrong email has been provided to reset password.');
		}

		const encodedPassword = await argon2.hash(password);

		await Promise.all([

			User.update({
				password: encodedPassword
			}, {
				where: {
					id: passwordReset.user.id,
				}
			}),

			PasswordReset.update({
				isUsed: true,
			}, {
				where: {
					id: passwordReset.id,
				}
			})

		]);

		return AuthService.issueJwtPayload(passwordReset.user.id, passwordReset.user.username, passwordReset.user.role);

	}

}
