import DocumentTag from "../db/models/documentTag.model";
import { IGetByIdParams } from "../../common/contracts/common";
import { NotFoundError, BadRequestError, ServerError } from "../../model/HttpErrors";
import { ICreateDocumentTagInputModel, IUpdateDocumentTagInputModel } from "../../common/contracts/documentTag";


export class DocumentTagController {
	public static async getDocumentTagsByDocumentId(req: JwtRequest) {
		const { id } = req.params as unknown as IGetByIdParams;

		const tags = await DocumentTag.findAll({
			where: {
				documentId: id,
				isArchived: false,
			}
		});

		if (!tags) {
			throw new NotFoundError('Tag is not found.');
		}

		return tags;
	}

	public static async getDocumentTagbyId(req: JwtRequest) {
		const { id } = req.params as unknown as IGetByIdParams;

		const tags = await DocumentTag.findOne({
			where: {
				id: id,
			}
		});

		if (!tags) {
			throw new NotFoundError('Tag is not found.');
		}

		return tags;
	}

	public static async createDocumentTag(req: any, res: any) {
		const { documentId, tagId } = req.body as ICreateDocumentTagInputModel;

		try {
			return await DocumentTag.create({ documentId, tagId }, { fields: ['documentId', 'tagId'] });
		} catch (e) {
			if (e && e.name === 'SequelizeUniqueConstraintError') {
				throw new BadRequestError('Document collection with this name already exists.');
			} else {
				console.log('createTag exception', e);
				throw new ServerError();
			}
		}
	}

	public static async archiveDocumentTag(req: JwtRequest) {
		const { id } = req.params as unknown as IGetByIdParams;
		const tag: DocumentTag | null = await DocumentTag.findByPrimary(id);

		if (!tag || tag.isArchived) {
			throw new NotFoundError('Tag is not found.');
		}

		const [recordsArchived] = await DocumentTag.update({
			isArchived: true,
		}, {
			where: { id: id }
		});

		return {
			archived: recordsArchived,
		};
	}

	public static async updateDocumentTag(req: any, res: any) {
		const { id, documentId, tagId } = req.body as IUpdateDocumentTagInputModel;
		const tag: DocumentTag | null = await DocumentTag.findByPrimary<DocumentTag>(id);

		if (!tag || tag.isArchived) {
			throw new NotFoundError('Tag is not found');
		}

		try {
			const [recordsUpdated] = await DocumentTag.update({ documentId, tagId }, { where: { id: id } });
			return { status: 'ok', recordsUpdated };
		} catch (e) {
			if (e && e.name === 'SequelizeUniqueConstraintError') {
				throw new BadRequestError('Tag with this name already exists.');
			} else {
				console.log('updateTag exception', e);
				throw new ServerError();
			}
		}
	}
}