import { Component, Input, ComponentFactoryResolver, ViewContainerRef, OnInit, ViewChild } from "@angular/core";
import { IFormOutputModel, IFormRecordOutputModel } from "../../../../../../common/contracts/form";
import {FollowUpFormStage0Component} from "../stage0/followUpFormStage0.component";
import {FollowUpFormStage2Component} from "../stage2/followUpFormStage2.component";
import {FollowUpFormStage1Component} from "../stage1/followUpFormStage1.component";

const stageFormMap = {
  0: FollowUpFormStage0Component,
  1: FollowUpFormStage1Component,
  2: FollowUpFormStage2Component
};

/*
  This is the final, read only state for all the stages
*/

@Component({
  selector: 'follow-up-form-3',
  templateUrl: './followUpFormStage3.component.html'
})
export class FollowUpFormStage3Component implements OnInit {

  private _formData: IFormOutputModel;

  @Input('formData')
  get formData() {
    return this._formData;
  }
  /** this is fix for navigation via child forms tree
   *  since this component might not be unmounted we need to clear all dynamic (read-only) forms
   **/
  set formData(newFormData: IFormOutputModel) {
    this._formData = newFormData;
    this.isReportEnabled = false;
    this.vc.clear();
    this.addReadOnlyForms();
  }

  @ViewChild('vc', {read: ViewContainerRef}) vc: ViewContainerRef;

  public isReportEnabled = false;

  constructor(private factoryResolver: ComponentFactoryResolver) { }

  ngOnInit() {}

  public addReadOnlyForms() {
    if( !this.formData || !this.formData.records ) {
      console.error('form data is not defined');
      return;
    }


    //Get completed Records
    this.formData.records
    .filter( record => record.isComplete)
    .forEach( record => this.addReadOnlyForm(record))
  }

  public addReadOnlyForm(record:IFormRecordOutputModel) {
    const _component = stageFormMap[record.stage];
    if (!_component) {
      console.error('Cannot find follow-up form component for stage ', record.stage);
      return;
    }
    const factory = this.factoryResolver.resolveComponentFactory(_component);
    const component = this.vc.createComponent(factory);

    // @ts-ignore
    component.instance.readOnly = true;
    // @ts-ignore
    component.instance.formData = this.formData;
    // @ts-ignore
    component.instance.hideHeader = true;
    // @ts-ignore
    component.instance.sequence = record.sequence;

    this.vc.insert(component.hostView);

    this.isReportEnabled = true;
  }
}
