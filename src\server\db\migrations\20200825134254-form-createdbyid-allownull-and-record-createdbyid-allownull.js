'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.changeColumn('forms', 'createdById', {
        type: Sequelize.INTEGER,
        allowNull: true
      }),
      queryInterface.changeColumn('forms_record','createdById', {
        type: Sequelize.INTEGER,
        allowNull: true
      })
    ]);
  },

  down: (queryInterface, Sequelize) => {
    return Promise.all([
      queryInterface.changeColumn('forms', 'createdById', {
        type: Sequelize.INTEGER,
        allowNull: false
      }),
      queryInterface.changeColumn('forms_record','createdById', {
        type: Sequelize.INTEGER,
        allowNull: false
      })
    ]);
  }
};
