'use strict';

module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDefinition = await queryInterface.describeTable('document_instance');

		if (!tableDefinition['fileName']) {
			await queryInterface.addColumn('document_instance', 'fileName', {
				type: Sequelize.STRING,
				allowNull: true,
			})
		}

		if (!tableDefinition['originalfileName']) {
			await queryInterface.addColumn('document_instance', 'originalfileName', {
				type: Sequelize.STRING,
				allowNull: true,
			})
		}
	},

	down: (queryInterface, Sequelize) => {
		return Promise.all([
			queryInterface.removeColumn('document_instance', 'fileName'),
			queryInterface.removeColumn('document_instance', 'originalfileName')
		])
	}
};
