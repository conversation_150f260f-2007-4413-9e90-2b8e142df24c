import { <PERSON>, <PERSON><PERSON><PERSON>, Column, <PERSON>, <PERSON>ongsTo } from "sequelize-typescript";
import ResourcePack from "./resource-pack.model";
import FormDocument from './formDocument.model';

@Table({
  tableName: 'resource_pack_documents',
  paranoid: true
})
export default class ResourcePackDocument extends Model<ResourcePackDocument> {
    @ForeignKey(() => ResourcePack)
    @Column({
        allowNull: false
    })
    resourcePackId: number;
    
    @Column
    path: string;

    @BelongsTo(() => ResourcePack)
    resourcePack: ResourcePack;

    @ForeignKey(() => FormDocument)
    @Column({
        allowNull: true
    })
    formDocumentId: number;

    @BelongsTo(() => FormDocument)
    document: FormDocument;
}