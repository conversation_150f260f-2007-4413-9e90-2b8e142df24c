'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if table exists first
      const tableExists = await queryInterface.showAllTables().then(tables =>
        tables.includes('document_collections')
      );

      if (!tableExists) {
        console.log('Table "document_collections" does not exist, skipping migration');
        return;
      }

      const tableDefinition = await queryInterface.describeTable('document_collections');

      if (!tableDefinition['reviewSchedule']) {
        return;
      }

      // Check if enum already has the correct values
      const result = await queryInterface.sequelize.query(
        'SELECT unnest(enum_range(NULL::"enum_document_collections_reviewSchedule")) as enum_value',
        { type: Sequelize.QueryTypes.SELECT }
      );

      const enumValues = result.map(row => row.enum_value);
      const expectedValues = ['ANNUALLY', 'BIANNUALLY', 'BIENNIALLY'];

      // Check if all expected values exist
      const hasAllValues = expectedValues.every(val => enumValues.includes(val));

      if (hasAllValues) {
        console.log('Enum already has correct values, skipping...');
        return;
      }

      return queryInterface.changeColumn(
        'document_collections', // table name
        'reviewSchedule', // new field name
        {
          type: Sequelize.ENUM('ANNUALLY', 'BIANNUALLY', 'BIENNIALLY'),
        },
      )
    } catch (error) {
      console.log('Error in migration:', error.message);
      // Don't throw error if enum already exists with correct values
      if (error.message.includes('already exists')) {
        console.log('Enum already exists, continuing...');
        return;
      }
      throw error;
    }
  },

  down: (queryInterface, Sequelize) => {
  }
};
