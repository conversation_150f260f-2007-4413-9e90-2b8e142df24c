import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Session } from '../../service/util/Session';
import { CurrentUserService } from "../../service/currentUser/CurrentUserService";

@Component({
  selector: 'authentication-icon',
  templateUrl: './authentication-icon.component.html',
  styleUrls: ['./authentication-icon.component.css'],
})
export class AuthenticationIconComponent implements OnInit {
  @Input() small = false;

  constructor(
    private router: Router,
    public session: Session,
    private currentUserService: CurrentUserService,
  ) { }

  ngOnInit() {
  }

  public signOut() {
    this.session.lockInput(async () => {
      this.currentUserService.cleanUserCredentials();
      await this.session.init();
      this.router.navigateByUrl('/');
    });
  }
}
