import SettingsModel from '../db/models/settings.model';
import { ServerError } from '../../model/HttpErrors';

export const ACTUAL_SETTINGS_KEY = process.env.ACTUAL_SETTINGS_KEY || 'actual_system_settings';

class SystemSettingsServiceClass {

  public async getSettings() {

    const settings = await SettingsModel.findOne({
      attributes: {
        exclude: ['key', 'id'],
      },
      where: {
        key: ACTUAL_SETTINGS_KEY,
      }
    });

    if (!settings) {
      throw new ServerError('System settings were not found.');
    }

    return settings.toJSON();

  }

  public async getSettingsValue(key: string): Promise<any> {

    const settings = await SettingsModel.findOne({
      attributes: [key],
      where: {
        key: ACTUAL_SETTINGS_KEY,
      }
    });

    if (!settings || settings[key] === undefined) {
      throw new ServerError('System settings value is not found.');
    }

    return settings[key];

  }

}

export const systemSettingsService = new SystemSettingsServiceClass();
