import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  Sequelize,
  BelongsToMany
} from 'sequelize-typescript';
import FormRecord from './formRecord.model';
import User from './user.model';
import DocumentToRecord from './document_to_record.model';

@Table({
  tableName: 'form_document'
})
class FormDocument extends Model<FormDocument> {

  @Column
  fileName: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
  })
  mimeType: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  path: string;

  @Column({
    type: DataType.INTEGER(15),
    allowNull: false,
  })
  size: number;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
  })
  userId: number;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)'),
    onUpdate: 'CURRENT_TIMESTAMP(3)'
  })
  updatedAt: Date;

  @BelongsToMany(() => FormRecord, () => DocumentToRecord)
  records: FormRecord[];
}

export default FormDocument;
