import {
  Table,
  Column,
  Model,
  DataType,
  Sequelize,
  Unique,
  Foreign<PERSON>ey,
  BelongsTo
} from 'sequelize-typescript';
import User from './user.model';

@Table({
  tableName: 'password_resets'
})
class PasswordReset extends Model<PasswordReset> {

  @Unique
  @Column
  token: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @BelongsTo(() => User, 'userId')
  user: User;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false
  })
  isUsed: boolean;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)')
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE(3),
    defaultValue: Sequelize.literal('CURRENT_TIMESTAMP(3)'),
    onUpdate: 'CURRENT_TIMESTAMP(3)'
  })
  updatedAt: Date;

  @Column({
    type: DataType.VIRTUAL,
  })
  isExpired: number;

}

export default PasswordReset;


