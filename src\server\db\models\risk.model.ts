import { Table, Model, Column, DataType, Foreign<PERSON>ey, BelongsTo } from 'sequelize-typescript';
import FormLocation from './formLocation.model';
import UserGroup from './group.model';

@Table({
  tableName: 'risks',
  paranoid: true,
  timestamps: true
})
class Risks extends Model<Risks> {
  @BelongsTo(() => FormLocation)
  form_locations: FormLocation;

  @BelongsTo(() => UserGroup)
  groups: UserGroup[];

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  summary: string | null;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  riskLevel: string;

  @Column({
    type: DataType.STRING,
    allowNull: true
  })
  residentName: string;

  @Column({
    type: DataType.STRING,
    allowNull: false
  })
  riskStatus: string;

  @Column({
    type: DataType.DATE,
    allowNull: true
  })
  dateOfNextAssessment: Date | null;

  @Column({
    type: DataType.DATE,
    allowNull: true
  })
  dateOfLastAssessment?: Date | null;

  @ForeignKey(() => FormLocation)
  @Column
  locationId: number;

  @ForeignKey(() => UserGroup)
  @Column
  groupId: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
  })
  createdAt: Date
}

export default Risks;


